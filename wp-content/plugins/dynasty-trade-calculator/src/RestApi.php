<?php
/**
 * REST API for Dynasty Trade Calculator
 */

namespace DynastyTradeCalculator;

if (!defined('ABSPATH')) exit;

use WP_Error;

class RestApi {

    private $levels;

    public function __construct() {
        // add_action('rest_api_init', array($this, 'register_endpoints')); // not needed for now. we will use rotoGPT team endpoints
    }

    public function register_endpoints() {
        // Sample Rest Route Endpoint
        // register_rest_route('dtc/v1', '/subscriptions/create', array(
        //     'methods' => 'POST',
        //     'callback' => array($this, 'create_subscription'),
        //     'permission_callback' => function() { return current_user_can('manage_options'); },
        //     'args' => array(
        //         'user_id' => array(
        //             'required' => true,
        //             'type' => 'string',
        //             'sanitize_callback' => 'sanitize_user',
        //             'validate_callback' => function($param) {
        //                 return !empty($param) && validate_username($param);
        //             }
        //         ),
        //         'client_id' => array(
        //             'required' => true,
        //             'type' => 'string',
        //             'sanitize_callback' => 'sanitize_text_field',
        //             'validate_callback' => function($param) {
        //                 return !empty($param);
        //             }
        //         ),
        //         'subscription_type' => array(
        //             'required' => true,
        //             'type' => 'string',
        //             'sanitize_callback' => 'sanitize_text_field',
        //             'validate_callback' => array($this, 'validate_subscription_type')
        //         )
        //     )
        // ));
        
        //
    }

}
