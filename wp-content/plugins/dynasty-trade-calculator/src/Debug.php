<?php
/**
 * Dynasty Trade Calculator - Debug Utility Class
 *
 * Provides conditional logging functionality based on WordPress configuration constants
 * Helps manage debug output and prevents excessive logging in production
 */

namespace DynastyTradeCalculator;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Debug
{
    /**
     * Log levels for different types of messages
     */
    const LEVEL_ERROR = 'ERROR';
    const LEVEL_WARNING = 'WARNING';
    const LEVEL_INFO = 'INFO';
    const LEVEL_DEBUG = 'DEBUG';

    /**
     * Check if DTC debug mode is enabled
     * 
     * @return bool True if debug mode is enabled
     */
    public static function isEnabled()
    {
        // Check for DTC-specific debug constant first
        if (defined('DTC_DEBUG') && DTC_DEBUG) {
            return true;
        }

        // Fall back to WordPress debug constant
        // if (defined('WP_DEBUG') && WP_DEBUG && defined('WP_DEBUG_LOG') && WP_DEBUG_LOG) {
        //     return true;
        // }

        return false;
    }

    /**
     * Check if verbose debug mode is enabled (for detailed object dumps)
     * 
     * @return bool True if verbose debug mode is enabled
     */
    public static function isVerboseEnabled()
    {
        return defined('DTC_DEBUG_VERBOSE') && DTC_DEBUG_VERBOSE;
    }

    /**
     * Log a message if debug mode is enabled
     * 
     * @param string $message The message to log
     * @param string $level Log level (ERROR, WARNING, INFO, DEBUG)
     * @param bool $force Force logging even if debug is disabled
     */
    public static function log($message, $level = self::LEVEL_INFO, $force = false)
    {
        if (!$force && !self::isEnabled()) {
            return;
        }

        $timestamp = date('Y-m-d H:i:s');
        $formatted_message = "[{$timestamp}] [DTC-{$level}] {$message}";
        
        error_log($formatted_message);
    }

    /**
     * Log an error message
     * 
     * @param string $message The error message to log
     * @param bool $force Force logging even if debug is disabled
     */
    public static function error($message, $force = false)
    {
        self::log($message, self::LEVEL_ERROR, $force);
    }

    /**
     * Log a warning message
     * 
     * @param string $message The warning message to log
     * @param bool $force Force logging even if debug is disabled
     */
    public static function warning($message, $force = false)
    {
        self::log($message, self::LEVEL_WARNING, $force);
    }

    /**
     * Log an info message
     * 
     * @param string $message The info message to log
     */
    public static function info($message)
    {
        self::log($message, self::LEVEL_INFO);
    }

    /**
     * Log a debug message
     * 
     * @param string $message The debug message to log
     */
    public static function debug($message)
    {
        self::log($message, self::LEVEL_DEBUG);
    }

    /**
     * Log an object or array with print_r (only if verbose debug is enabled)
     * 
     * @param mixed $data The data to log
     * @param string $label Optional label for the data
     * @param bool $force Force logging even if verbose debug is disabled
     */
    public static function logObject($data, $label = 'Object', $force = false)
    {
        if (!$force && !self::isVerboseEnabled()) {
            return;
        }

        $output = print_r($data, true);
        self::log("{$label}: " . $output, self::LEVEL_DEBUG, $force);
    }

    /**
     * Log a variable dump (only if verbose debug is enabled)
     * 
     * @param mixed $data The data to dump
     * @param string $label Optional label for the data
     * @param bool $force Force logging even if verbose debug is disabled
     */
    public static function dump($data, $label = 'Dump', $force = false)
    {
        if (!$force && !self::isVerboseEnabled()) {
            return;
        }

        ob_start();
        var_dump($data);
        $output = ob_get_clean();
        
        self::log("{$label}: " . $output, self::LEVEL_DEBUG, $force);
    }

    /**
     * Log HTTP request/response data (useful for API debugging)
     * 
     * @param string $endpoint The API endpoint
     * @param array $request_data The request data
     * @param mixed $response The response data
     * @param string $operation The operation being performed (e.g., 'Create', 'Update')
     */
    public static function logApiCall($endpoint, $request_data = null, $response = null, $operation = 'API Call')
    {
        if (!self::isEnabled()) {
            return;
        }

        self::info("DTC RotoGPT {$operation}: Sending request to {$endpoint}");
        
        if ($request_data && self::isVerboseEnabled()) {
            self::logObject($request_data, "DTC RotoGPT {$operation}: Request body");
        }
        
        if ($response && self::isVerboseEnabled()) {
            self::logObject($response, "DTC RotoGPT {$operation}: Response");
        }
    }

    /**
     * Log membership-related operations
     * 
     * @param string $operation The operation being performed
     * @param int $membership_id The membership ID
     * @param array $additional_data Additional data to log
     */
    public static function logMembership($operation, $membership_id, $additional_data = [])
    {
        if (!self::isEnabled()) {
            return;
        }

        $message = "DTC RotoGPT {$operation}: Processing membership #{$membership_id}";
        
        if (!empty($additional_data)) {
            $data_string = implode(', ', array_map(function($key, $value) {
                return "{$key}: {$value}";
            }, array_keys($additional_data), $additional_data));
            $message .= " - {$data_string}";
        }
        
        self::info($message);
    }

    /**
     * Get debug status information
     * 
     * @return array Debug status information
     */
    public static function getStatus()
    {
        return [
            'dtc_debug_enabled' => defined('DTC_DEBUG') && DTC_DEBUG,
            'dtc_debug_verbose_enabled' => defined('DTC_DEBUG_VERBOSE') && DTC_DEBUG_VERBOSE,
            'wp_debug_enabled' => defined('WP_DEBUG') && WP_DEBUG,
            'wp_debug_log_enabled' => defined('WP_DEBUG_LOG') && WP_DEBUG_LOG,
            'debug_active' => self::isEnabled(),
            'verbose_active' => self::isVerboseEnabled(),
        ];
    }
}
