<?php
/**
 * Dynasty Trade Calculator - Plugin Class
 *
 * Main plugin class that handles initialization and coordinates all functionality
 */

namespace DynastyTradeCalculator;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Plugin
{
    /**
     * Initialize the plugin
     */
    public static function init()
    {
        // Initialize membership functionality with instance
        $membership = Membership::getInstance();
        $membership->init();

        // Initialize REST API
        new RestApi();
    }

    /**
     * Get all registered hooks (for debugging purposes)
     * 
     * @return array List of all registered hooks
     */
    public static function getRegisteredHooks()
    {
        global $wp_filter;
        
        $plugin_hooks = [];
        
        // Get hooks that belong to this plugin
        foreach ($wp_filter as $hook_name => $hook_data) {
            foreach ($hook_data->callbacks as $priority => $callbacks) {
                foreach ($callbacks as $callback_id => $callback_data) {
                    $callback = $callback_data['function'];
                    
                    // Check if this is one of our class methods
                    if (is_array($callback) && isset($callback[0])) {
                        $class_name = is_string($callback[0]) ? $callback[0] : get_class($callback[0]);
                        
                        // Check if it's one of our namespace classes
                        if (strpos($class_name, 'DynastyTradeCalculator\\') === 0) {
                            $plugin_hooks[$hook_name][] = [
                                'class' => $class_name,
                                'method' => $callback[1],
                                'priority' => $priority,
                                'callback_id' => $callback_id
                            ];
                        }
                    }
                }
            }
        }
        
        return $plugin_hooks;
    }

    /**
     * Remove all plugin hooks (useful for testing or cleanup)
     */
    public static function removeAllHooks()
    {
        $hooks = self::getRegisteredHooks();
        
        foreach ($hooks as $hook_name => $hook_callbacks) {
            foreach ($hook_callbacks as $callback_info) {
                remove_action($hook_name, [$callback_info['class'], $callback_info['method']], $callback_info['priority']);
            }
        }
    }

    /**
     * Log all registered hooks (for debugging)
     */
    public static function logRegisteredHooks()
    {
        $hooks = self::getRegisteredHooks();

        Debug::logObject($hooks, 'DTC Registered Hooks');

        return $hooks;
    }
}
