<?php

// autoload_psr4.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Php<PERSON>arser\\' => array($vendorDir . '/nikic/php-parser/lib/PhpParser'),
    'ExtPHP\\XmlToJson\\' => array($vendorDir . '/extphp/xml-to-json/src'),
    'DynastyTradeCalculator\\' => array($baseDir . '/src'),
    'Doctrine\\Instantiator\\' => array($vendorDir . '/doctrine/instantiator/src/Doctrine/Instantiator'),
    'DeepCopy\\' => array($vendorDir . '/myclabs/deep-copy/src/DeepCopy'),
);
