# Dynasty Trade Calculator - PRD

## Status (June 20, 2025)

### ✅ Completed
- **Fatal Error Fix**: Resolved PHP dependency issues, calculator loads without errors
- **Class-Based Migration**: Migrated membership functionality from functions to classes
- **Test Suite**: 50 tests, 370 assertions - ALL PASSING ✅
- **Debug System**: Conditional logging with `DTC_DEBUG` and `DTC_DEBUG_VERBOSE` constants
- **Shortcodes**: Recovered missing `dtc_register_form` and `dtc-easy-pricing-table`

### 🔧 Debug Configuration (Essential)
Add to `wp-config.php`:
```php
// Basic debug (production: false)
define('DTC_DEBUG', true);
// Verbose debug with object dumps (production: false)
define('DTC_DEBUG_VERBOSE', true);
```

## 📋 Task List

### 🔄 In Progress
- [ ] **Update Remaining Files**: Find and replace old membership functions with class methods
- [ ] **Calculator Class Migration**: Convert calculator functionality to class-based approach
- [ ] **API Class Migration**: Convert REST API functionality to class-based approach
- [ ] **Admin Class Migration**: Convert admin functionality to class-based approach

### ⏳ Pending
- [ ] **Remove Old Files**: Delete function-based files after migration complete
- [ ] **Expand README.md**: Add proper project documentation for GitHub
- [ ] **Performance Testing**: Verify no performance regression after migration
- [ ] **Production Deployment**: Deploy class-based version to production

### 🎯 Current Priority
**Complete function-to-class migration** - Search for old function calls and replace with class methods

## 🏗️ Architecture
```
src/
├── Plugin.php      # Main initialization
├── Membership.php  # Complete membership functionality
├── Debug.php       # Conditional logging
└── RestApi.php     # REST API endpoints
```

## 🧪 Testing
- **6 Test Files**: 50 tests, 370 assertions - ALL PASSING ✅
- **Mock Data Only**: No secrets or wp-config data in tests (git-safe)
- **Run Tests**: `vendor/bin/phpunit` (~0.07 seconds)

## 🔄 Migration Pattern
```php
// OLD: dtc_get_current_user_customer()
// NEW: Membership::getCurrentUserCustomer()
// Add: use DynastyTradeCalculator\Membership;
```
