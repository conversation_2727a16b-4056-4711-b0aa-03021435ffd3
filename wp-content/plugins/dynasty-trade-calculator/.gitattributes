.deploy                 export-ignore
.distignore             export-ignore
.DS_Store               export-ignore
.editorconfig           export-ignore
.git                    export-ignore
.github                 export-ignore
.gitignore              export-ignore
.gitmodules             export-ignore
*.sql                   export-ignore
*.tar.gz                export-ignore
*.zip                   export-ignore
bin                     export-ignore
composer.json           export-ignore
composer.lock           export-ignore
Gruntfile.js            export-ignore
node_modules            export-ignore
package.json            export-ignore
phpcs.xml.dist          export-ignore
phpcs.ruleset.xml       export-ignore
phpstan.neon.dist       export-ignore
phpunit.xml             export-ignore
phpunit.xml.dist        export-ignore
README.md               export-ignore
tests                   export-ignore
Thumbs.db               export-ignore
wp-cli.local.yml        export-ignore

# Composer vendor directory - mirror .gitignore whitelist pattern
vendor/*                export-ignore
\!vendor/autoload.php    export-ignore
\!vendor/composer/       export-ignore
vendor/composer/*       export-ignore
\!vendor/composer/autoload_*.php export-ignore
\!vendor/composer/ClassLoader.php export-ignore
\!vendor/composer/InstalledVersions.php export-ignore
\!vendor/composer/installed.php export-ignore
\!vendor/composer/installed.json export-ignore
\!vendor/composer/LICENSE export-ignore

# Test-related files (already covered by 'tests' above)
.phpunit.result.cache   export-ignore

# Test-related documentation (security)

# Example files (development only)
src/Example             export-ignore

# New patterns from the code block
