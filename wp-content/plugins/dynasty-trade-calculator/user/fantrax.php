<?php 

new  fantrax_api;

class fantrax_api {
	protected $slug;
	protected $name;
	// protected $username;
	protected $endpoint;
	protected $season;
	protected $api_url;
    
	protected $userId;
	protected $leagueTeams;
	protected $rounds;
	protected $includeDraftPicks;
	
	public function __construct() {
		$this->slug = 'fantrax';
		$this->name = 'Fantrax';
		$settings = get_option('dtc_settings');
		$this->endpoint = isset($settings[$this->slug.'_url']) ? $settings[$this->slug.'_url'] : '';
		$this->season = isset($settings[$this->slug.'_season']) ? $settings[$this->slug.'_season'] : '';

        $this->api_url = "https://www.fantrax.com/fxea/general/";

		// Not sure if this is needed. Leaving it for now.
		$this->rounds = 4;

		$this->includeDraftPicks = true;

		$this->leagueTeams = [];

		add_action('dtc/admin/settings',array($this,'settings'));
		add_action('dtc/admin/settings/api_settings',array($this,'api_settings'));
		add_action('dtc_calculator_bottom', array($this,'view'));
		add_action('dtc/league_import/content',array($this,'import_content'));
		add_action('dtc/league_import/button',array($this,'import_button'));

		add_action( 'wp_ajax_dtc_ajax_'.$this->slug.'_get_league_settings', array($this,'ajax_get_league_settings'));	

		add_action( 'wp_ajax_dtc_ajax_'.$this->slug.'_get_opposing_players_dropdown', array($this,'ajax_get_opposing_players_dropdown'));	
		add_action( 'wp_ajax_nopriv_dtc_'.$this->slug.'_ajax_get_opposing_players_dropdown', array($this,'ajax_get_opposing_players_dropdown'));

		add_action( 'wp_ajax_dtc_ajax_'.$this->slug.'_get_league_players', array($this,'ajax_get_league_players'));	
		add_action( 'wp_ajax_nopriv_dtc_ajax_'.$this->slug.'_get_league_players', array($this,'ajax_get_league_players'));
		
		add_action( 'wp_ajax_dtc_ajax_'.$this->slug.'_login', array($this,'ajax_login'));	
		add_action( 'wp_ajax_nopriv_dtc_ajax_'.$this->slug.'_login', array($this,'ajax_login'));
		
		add_action( 'wp_ajax_dtc_'.$this->slug.'_ajax_logout', array($this,'ajax_logout'));	
		add_action( 'wp_ajax_nopriv_dtc_'.$this->slug.'_ajax_logout', array($this,'ajax_logout'));

		add_action( 'wp_ajax_dtc_ajax_'.$this->slug.'_main_loader', array($this,'ajax_main_loader'));	
		add_action( 'wp_ajax_nopriv_dtc_ajax_'.$this->slug.'_main_loader', array($this,'ajax_main_loader'));

		add_action('wp_ajax_process_fantrax_login', array($this,'process_fantrax_login'));
		add_action('wp_ajax_nopriv_process_fantrax_login', array($this,'process_fantrax_login'));
	}

	/* Fantrax Basic Integration Functions */

	function ajax_main_loader() {
		global $current_user;
		$user_id = $current_user->ID;

		$user_secret = get_user_meta($user_id, 'fantrax_user_secret', true);
        
		if (empty($user_secret)) {
			$plugin_base_url = plugin_dir_url(__DIR__);
			?>
			<div class="dtc-mfl-nav fantrax-login">
				<div>
					<div style="text-align: center;"><img style="max-width: 40%;" src="<?php echo $plugin_base_url; ?>/asetts/images/api/fantrax-logo.png"></div>
					<h1 id="fantrax-login-text">Login</h1>
				</div>
					<div style="display: flex; justify-content: center; align-items: center; width: 100%;">
						<form id="auth-form" style="max-width: 400px; width: 100%; text-align: center;">
							<label class="fantrax-label" for="fantrax_user_secret">Fantrax User Secret</label>
							<input type="text" id="fantrax_user_secret" name="fantrax_user_secret" placeholder="Fantrax User Secret" required />
							<button id="fantrax_submit_button" type="button">Submit</button>
							<div id="fantrax-login-overlay" style="display: none;"><div class="dtc-mfl-overlay-wrap"><img src="<?php echo DTC_URL; ?>/asetts/images/loading.gif"></div></div>
						</form>
					</div>
				</div>
				<div id="fantrax-loading-area"  style="display:block; text-align: center; color:#000000;">
					Loading . . .
					<br/>Please wait
				</div>
				<script type="text/javascript">
					jQuery(document).ready(function($) {
						$('#fantrax_submit_button').on('click.authForm', function(event) {
							event.preventDefault(); // Prevent the default form submission

							document.getElementById('fantrax_submit_button').style.display = 'none';
							document.getElementById('fantrax-login-overlay').style.display = 'block';

							var fantrax_user_secret = $('#fantrax_user_secret').val();

                            var data = {
                                    action: 'process_fantrax_login',
                                    fantrax_user_secret: fantrax_user_secret
                                };

							// Send the AJAX request
							$.post('<?php echo admin_url('admin-ajax.php?action=process_fantrax_login'); ?>', data, function(response) {
								if (response.success === true) {
									localStorage.setItem('dtc_integration_name', 'fantrax');
									window.IntegrationName = "fantrax";
									window.rotoGPT_LeagueIntegrationName = "fantrax";
									window.rotoGPT_LeagueUserId = undefined;
                                    window.rotoGPT_LeagueToken = undefined;
									window.rotoGPT_LeagueId = undefined;
									window.rotoGPT_LeagueName = undefined;
									window.rotoGPT_UserEmail = undefined;

									dtc_refresh_main_loader();
								} else {
									document.getElementById('fantrax_submit_button').style.display = 'block';
									document.getElementById('fantrax-login-overlay').style.display = 'none';

									// Need to think about what to do here!
									alert(response.data);
								}
							});
						});
					});


				</script>
			</div>
			<?php

		} else {
			echo '	
			<h1>'.$this->name.'</h1>
			<div class="dtc-mfl-nav">
				<a href="#" class="'.$this->slug.'-refresh-data mfl-button" data-id="'.$current_user->ID.'">Refresh Data</a>
				<a href="#" class="'.$this->slug.'-logout mfl-button" data-id="'.$current_user->ID.'">Logout</a>
			</div>
			';
	
			$leagues = $this->get_leagues();

			echo '<div class="dtc-mfl-half">';
			echo '	<div class="dtc-half-inner">';

			$name = 'fantrax_your_team';
			$html = '<select name="'.$name.'" class="'.$name.' league_dropdown"><option value="">Your Team</option>';
		
			foreach ($leagues as $league) {
				$html .= '<option value="'.$league->leagueId.'" data-my-team-id="'.$league->teamId.'" data-my-team-name="'.$league->teamName.'">'.$league->leagueName.' -  '.$league->teamName.'</option>';
			}

			$html .='</select>';

			$html .= '
				<script>
					jQuery(document).ready(function(){
						var league_id = localStorage.getItem("dtc_league_id");
						
						if (league_id) {
							jQuery("select[name=\'fantrax_your_team\'] option[value=\'" + league_id + "\']").prop("selected", true);
							jQuery("select[name=\'fantrax_your_team\']").trigger("change");
						}
					});
				</script>
				';

			echo $html;

			echo '
				</div>
			</div>
			';

			echo '
				<div class="dtc-mfl-half">
					<div class="dtc-half-inner">
			';
			echo '<div class="'.$this->slug.'-opposing-teams"><select disabled="disabled" ><option value="">Your Competitor</option></select></div>';
			echo '
				</div>
			</div>
			
			<div style="clear:both"></div>
			
			<div class="mfl-import-calc">
				<a href="#" class="mfl-import-calc-button">Import to Calc</a>
			</div>
			
			<div class="'.$this->slug.'-league-settings"></div>
			
			<div class="mfl-team-gutter">
				<div class="dtc-mfl-half">
					<div class="dtc-half-inner">
						<div class="'.$this->slug.'-import-team-one">
						
						</div>
					</div>
				</div>
			
				<div class="dtc-mfl-half">
					<div class="dtc-half-inner">
						<div class="'.$this->slug.'-import-team-two">
				
						</div>
					</div>
				</div>
			
				<div style="clear:both">
				</div>
			</div>

			<a href="#" class="mfl-import-calc-button" style="margin-bottom:20px">Import to Calc</a>
			';
		}

		die();
	}

	function ajax_login() {
		$this->request_token();	
		die();
	}

	function ajax_logout() {
		$user_id = get_current_user_id();

		delete_user_meta($user_id, 'fantrax_user_secret');

		echo $this->ajax_main_loader();
	}

	function request_token() {

		global $current_user;
		
		echo $this->ajax_main_loader();
	}

	function is_active() {

		$settings = get_option('dtc_settings');

		if (!isset($settings[$this->slug.'_display']) || $settings[$this->slug.'_display'] == 0) {
			return false;	
		}

		if ($settings[$this->slug.'_display'] == 1 && !current_user_can('manage_options') ) {
			return false;	
		}
			
		return true;
	}

    function process_fantrax_login() {
        global $current_user;
		$user_id = $current_user->ID;

        if (isset($_POST['fantrax_user_secret'])) {
			$fantrax_user_secret = sanitize_text_field($_POST['fantrax_user_secret']); // Sanitize the input
			
			update_user_meta($user_id, 'fantrax_user_secret', $fantrax_user_secret);

            wp_send_json_success('Secret stored successfully!');
		} else {
			wp_send_json_error('No secret provided.');
		}
	
		wp_die();
    }

	function import_content() {
		if ($this->is_active() == true) {

			$this->javascript_functions();
			?>

			<style type="text/css">
				.<?php echo $this->slug; ?>-loader select{width:100%;}
				.<?php echo $this->slug; ?>-loader {color:#c3c6ca;}
				.<?php echo $this->slug; ?>-loader h1{color:#c3c6ca;}
				.<?php echo $this->slug; ?>-login{min-height:500px;padding:20px 0px;}
				.<?php echo $this->slug; ?>-login input[type=text]{width:80%;border-radius:5px;}
				.<?php echo $this->slug; ?>-login  .dtc-button{-moz-border-radius: 5px;
					border-radius: 26px;
					height: 52px;
					font-size: 14px;
					color: #FFF;
					background-color: #333;
					text-align:center;
					font-weight:600;
					min-width:100px;
				}
			</style>
			<div class="<?php echo $this->slug; ?>-loader league-import-item">
				
			</div>
			<?php
		}
	}

	function import_button() {
		if ($this->is_active()  == true) {
				echo '<a href="" class="dtc-refresh-main-loader '.$this->slug.'-loader-button" data-id="'.$this->slug.'">'.$this->name.'</a>';
		}
	}

	public function view() {
		
	}

	function api_settings($settings) {
		$url = isset($settings[$this->slug.'_url']) ? $settings[$this->slug.'_url'] : '';
		$season = isset($settings[$this->slug.'_season']) ? $settings[$this->slug.'_season'] : '';

		echo ' 
		<tr>
			<td style="width:150px !important">'.$this->name.' URL</td>
			<td><input type="text" name="dtc_settings['.$this->slug.'_url]" value="'.$url.'" style="width:300px;max-width:none"> </td>
		</tr>
		<tr>
			<td style="width:150px !important">'.$this->name.' Season</td>
			<td><input type="text" name="dtc_settings['.$this->slug.'_season]" value="'.$season.'" style="width:300px;max-width:none"> </td>
		</tr>
		';
	}

	function ajax_get_league_settings() {
		global $current_user;
		$user_id = $current_user->ID;

		$league_id = $_POST['league_id'] ?? '';

		if ($league_id == '') {
			die();
		}

		$leagueInfo = $this->get_settings_for_league($league_id);

        $fantrax_user_secret = get_user_meta($user_id, 'fantrax_user_secret', true);

		?>
			<script type="text/javascript">
				localStorage.setItem('dtc_integration_name', 'fantrax');
				localStorage.setItem('dtc_league_name', '<?php echo $leagueInfo->league_name; ?>');
				localStorage.setItem('dtc_league_id', '<?php echo $league_id; ?>');

				// Populating this so we don't have conflicting uses of rotoGPT and DTC
				window.IntegrationName = "fantrax";
				window.IntegrationLeagueName = "<?php echo $leagueInfo->league_name; ?>";
				window.IntegrationLeagueId = "<?php echo $league_id; ?>";

				window.rotoGPT_LeagueIntegrationName = "fantrax";

				<?php
                if (!empty($fantrax_user_secret)) {
				?>
                    window.rotoGPT_LeagueUserId = "<?php echo $fantrax_user_secret; ?>";
				<?php
                }
				?>

				window.rotoGPT_LeagueId = "<?php echo $league_id; ?>";
				window.rotoGPT_LeagueName = "<?php echo $leagueInfo->league_name; ?>";

				var event = new Event('IntegrationLeagueNameChanged');
				window.dispatchEvent(event);
			</script>
		<?php

		
		$rules_array['team_type'] = $leagueInfo->league_format;
		
		if ($leagueInfo->settings['superflex']) {
			$rules_array['team_format'] = 'SF';
		} else if ($leagueInfo->settings['2QB']) {
			$rules_array['team_format'] = '2QB';
		} else {
			$rules_array['team_format'] = 'STANDARD';
		}

		$rules_array['team_size'] = $leagueInfo->league_size;

		if ($leagueInfo->settings['TE_Premium']) {
			$rules_array['tepre'] = 'YES';
		} else {
			$rules_array['tepre'] = 'NO';
		}

		if ($leagueInfo->settings['RB_PPC']) {
			$rules_array['rbppc'] = 'YES';
		} else {
			$rules_array['rbppc'] = 'NO';
		}

		echo '	
		<input type="hidden" class="mfl-rule-team-type" value="'.$rules_array['team_type'].'">
		<input type="hidden" class="mfl-rule-team-format" value="'.$rules_array['team_format'].'">
		<input type="hidden" class="mfl-rule-team-size" value="'.$rules_array['team_size'].'">
		<input type="hidden" class="mfl-rule-team-tepre" value="'.$rules_array['tepre'].'">
		<input type="hidden" class="mfl-rule-team-rbppc" value="'.$rules_array['rbppc'].'">';

		echo '
		<div class="mfl-league_settings">
			<span><strong>Type: </strong>'.$rules_array['team_type'].'</span>
			<span><strong>Format: </strong>'.$rules_array['team_format'].'</span>
			<span><strong>Size: </strong>'.$rules_array['team_size'].'tm</span>
			<span><strong>TE Premium? </strong>'.$rules_array['tepre'].'</span>
			<span><strong>RB PPC? </strong>'.$rules_array['rbppc'].'</span>
		</div>';

		?>
			<script type="text/javascript">
				_dtc_save_setting('ls', "<?php echo $rules_array['team_size']; ?>");

				if ("<?php echo $rules_array['team_type']; ?>" == "PPR") {
					_dtc_save_setting('lt', "ppr");
				} else if ("<?php echo $rules_array['team_type']; ?>" == "HALF PPR") {
					_dtc_save_setting('lt', "half_ppr");
				} else if ("<?php echo $rules_array['team_type']; ?>" == "NON PPR") {
					_dtc_save_setting('lt', "nonppr");
				}
				
				_dtc_save_setting('lf', "<?php echo strtolower($rules_array['team_format']); ?>");

				var lfa_settings = '';

				if ("<?php echo $rules_array['tepre']; ?>" == "YES") {
					lfa_settings += 'te-premium,';
				}

				if ("<?php echo $rules_array['rbppc']; ?>" == "YES") {
					lfa_settings += 'rb-ppc-premium,';
				}

				lfa_settings += 'offense,';

				_dtc_save_setting('lfa',lfa_settings);

				recalculatePlayers();
			</script>
		<?php

		die();
	}

	function ajax_get_opposing_players_dropdown() {
		global $current_user;
		
		$league_id = $_POST['league_id'] ?? '';
		$team_id = $_POST['my_team_id'] ?? '';
		
		$class_name = $this->slug . '_load_opposing_team';

		$teams = $this->get_opponents($league_id,$team_id);	
		
		if (!empty($teams)) {
			$firstTeam = $teams[0];
		
			$leagueId = $firstTeam->leagueId;
			$leagueName = $firstTeam->leagueName;

			$league_name = ! empty( $leagueName ) ? $leagueName : '';
			$html = '<select name="'.$class_name.'" class="'.$class_name.'" data-id="'.$leagueId.'" data-name="'.$league_name.'"><option value="">Your Competitor</option>';
			
			foreach($teams as $team) {
				if ($team->teamId !== $team_id) {
					$html .= '<option value="'.$team->teamId.'" data-my-team-name="'.$team->teamName.'" >'.$team->teamName.'</option>';				
				}
			}
			
			$html .='</select>';
			
			echo $html;
		}

		die();
	}

	function settings($settings) {
		$selected0 = '';
		$selected1 = '';
		$selected2 = '';

		$display_setting = isset($settings[$this->slug.'_display']) ? $settings[$this->slug.'_display'] : 0;

		echo '  
		<tr>
			<td style="width:150px">'.$this->name.' Display</td>
			<td>';
			if ($display_setting == 0) {
				$selected0 ='selected="selected"'; 
			}

			if ($display_setting == 1) {
				$selected1 ='selected="selected"'; 
			}

			if ($display_setting == 2) {
				$selected2 ='selected="selected"'; 
			}

			echo ' <select name="dtc_settings['.$this->slug.'_display]"><option value="0" '.$selected0.'>Nobody</option><option value="1" '.$selected1.'>Admins</option><option value="2" '.$selected2.'>Everyone</option></select></td>
		</tr>
		';
	}

	function restrict_pages() {
		global $current_user, $post;
			
		$restrict_pages = array(30349);
		
		if (in_array($post->ID,$restrict_pages ) && !is_admin() && is_user_logged_in()  == false) {
				wp_redirect('/login/?redirect=/import-team/');	
		}
	}

	function ajax_get_league_players() {
		global $wpdb, $current_user;
		
		$league_id = $_POST['league_id'] ?? '';
		$team_id = $_POST['my_team_id'] ?? '';

        if (empty($league_id) || empty($team_id)) {
            die();
        }

		$plugin_base_url = plugin_dir_url(__DIR__);
		echo'
		<table class="mfl-trade-table">
			<thead>
				<tr>
					<th style="min-width:24px; padding-top:2px;"><img style="width:24px" src="' . $plugin_base_url . '/asetts/images/league-import-trade.svg" title="TRADE"></t>
					<th style="min-width:24px; padding-top:2px; text-align:left"><img style="width:24px" src="' . $plugin_base_url . '/asetts/images/league-import-player.svg" title="PLAYER"></t>
					<th style="min-width:24px; padding-top:2px;"><img style="width:24px" src="' . $plugin_base_url . '/asetts/images/league-import-position.svg" title="POSITION"></th>
					<th style="min-width:24px; padding-top:2px;"><img style="width:24px" src="' . $plugin_base_url . '/asetts/images/league-import-value.svg" title="DTC VALUE"></th>
				</tr>
			</thead>
		<tbody>
		';

		$rules_array = ! empty( $rules_array ) ? $rules_array : [];

		$roster = $this->get_team_roster($league_id, $team_id);

		foreach($roster as $player_o) {
			if ( ! empty( $player_o->position ) && in_array($player_o->position,array('QB','RB','WR','TE'))) {
				$player_info = $this->get_player($player_o);

				$player_info = ! empty( $player_info ) ? $player_info : [];
				$player_info['mfl_id'] = ! empty( $player_info['mfl_id'] ) ? $player_info['mfl_id'] : '';
			
				if ($player_info) {
					#print_r($player_info);	
					echo '	
					<tr>
						<td style="text-align:center"><input type="checkbox" data-id="player" name="trade-' . sanitize_text_field( $_POST['side'] ) . '[]" class="mfl-trade-object" data-side="' . sanitize_text_field( $_POST['side'] ) . '" value="'.$player_info['id'].'"  data-nonppr="'.$player_info['nonppr'].'" data-type="'.$player_info['type'].'"></td>
						<td class="mfl-trade-table-player-team" style="text-align:left; padding-left:5px; padding-right:5px;"><span class="mfl-trade-table-player">'.stripslashes($player_info['name']).'</span> <span class="mfl-trade-table-team">'.$player_info['team'].'</span></td>
						<td style="text-align:center; padding-left:3px; padding-right:3px;">
					';
					
					if ($player_info['position'] == 'TE') {
						echo '<span style="display:none">Z</span>';	
					}

					echo ''.$player_info['position'].'
						</td>
						<td  style="text-align:center">'.dtc_get_player_total($player_info['mfl_id'],$rules_array,true).'</td>
					</tr>
					';
				}
			}
		}

		$season = date("Y");

		if ($this->includeDraftPicks) {
			$draft_picks = $this->get_draft_picks($league_id, $team_id);
		
			foreach ($draft_picks as $pick) {
				$pick_data = $this->get_pick($pick->year . ' ' . $pick->pickValue);

				if (!empty($pick_data)) {
					$fields = ['nonppr', 'ten', 'twelve', 'fourteen', 'sixteen', 'tensf', 'twelvesf', 'fourteensf', 'sixteensf', 'id'];
					foreach ($fields as $field) {
						$pick_data[$field] = !empty($pick_data[$field]) ? $pick_data[$field] : '';
					}
		
					echo sprintf('
					<tr>
						<td style="text-align:center">
							<input type="checkbox" data-id="pick" class="mfl-trade-object" data-side="%s" data-type="" class="trade-%s" data-year="%s" data-round="%s" data-pick="0" data-nonppr="%s" data-ten="%s" data-twelve="%s" data-fourteen="%s" data-sixteen="%s" data-tensf="%s" data-twelvesf="%s" data-fourteensf="%s" data-sixteensf="%s" value="%s">
						</td>
						<td style="text-align:left; padding-left:5px; padding-right:5px;">%s</td>
						<td style="font-size:0px;">ZZ</td>
						<td style="text-align:center">%s</td>
					</tr>',
						htmlspecialchars($_POST['side'], ENT_QUOTES, 'UTF-8'),
						htmlspecialchars($_POST['side'], ENT_QUOTES, 'UTF-8'),
						htmlspecialchars($pick->year, ENT_QUOTES, 'UTF-8'),
						htmlspecialchars($pick->round, ENT_QUOTES, 'UTF-8'),
						htmlspecialchars($pick_data['nonppr'], ENT_QUOTES, 'UTF-8'),
						htmlspecialchars($pick_data['ten'], ENT_QUOTES, 'UTF-8'),
						htmlspecialchars($pick_data['twelve'], ENT_QUOTES, 'UTF-8'),
						htmlspecialchars($pick_data['fourteen'], ENT_QUOTES, 'UTF-8'),
						htmlspecialchars($pick_data['sixteen'], ENT_QUOTES, 'UTF-8'),
						htmlspecialchars($pick_data['tensf'], ENT_QUOTES, 'UTF-8'),
						htmlspecialchars($pick_data['twelvesf'], ENT_QUOTES, 'UTF-8'),
						htmlspecialchars($pick_data['fourteensf'], ENT_QUOTES, 'UTF-8'),
						htmlspecialchars($pick_data['sixteensf'], ENT_QUOTES, 'UTF-8'),
						htmlspecialchars($pick_data['id'], ENT_QUOTES, 'UTF-8'),
						htmlspecialchars($pick->displayText, ENT_QUOTES, 'UTF-8'),
						htmlspecialchars(dtc_get_player_total(0, $rules_array, true, 'draft_picks', $pick->year . ' ' . $pick->pickValue), ENT_QUOTES, 'UTF-8')
					);
				} else {
					// Ignore this for now.
					// echo '<tr><td colspan="4" style="text-align:center;">Pick data not found</td></tr>';
				}
			}
		}
		
		echo '</tbody></table>';
		die();
	}

	function fix_name($name) {
		$name = str_replace("","%",$name);
		$name = str_replace(" Jr.","%",$name);
		$name = str_replace(" Sr.","%",$name);
		$name = str_replace("'","%",$name);
		$name = str_replace('"','%',$name);
		$name = str_replace(".",'%',$name);
		$name = str_replace("-",'%',$name);

		return $name;
	}

	function get_player($player) {
		global $wpdb;
		
		$query = "SELECT * FROM  wp_dtc_players where name like '%".$this->fix_name($player->player_first_name)."%".$this->fix_name($player->player_last_name)."%' AND position = '".$this->fix_name($player->position)."'";

		$r = $wpdb->get_results($query, ARRAY_A);
		
		if ($r) {
			return $r[0];
		} else {
			return false;	
		}
	}

	function get_pick($pick_id) {
		global $wpdb;
		$r = $wpdb->get_results($wpdb->prepare("SELECT * FROM  wp_dtc_draft_picks where pick = %s", $pick_id), ARRAY_A);
		return $r[0] ?? '';
	}

	function javascript_functions() {
	?>
		<script type="text/javascript">
			function dtc_refresh_fantrax_loader() {
				add_dtc_mfl_create_overlay(".mfl-loader");
				jQuery(".league-import-item").hide();
		
				jQuery.post(dtc.ajaxurl, {'action':'dtc_ajax_<?php echo $this->slug; ?>_main_loader'},
					function(response) {					
						jQuery(".<?php echo $this->slug; ?>-loader").html(response);
						jQuery(".<?php echo $this->slug; ?>-loader").show();
				});
			}

			function dtc_<?php echo $this->slug; ?>_load_team(league_id,franchise_id,team_name,location,side){
				add_dtc_mfl_create_overlay(location);
				jQuery.post(dtc.ajaxurl, {'action':'dtc_ajax_<?php echo $this->slug; ?>_get_league_players','side':side, 'league_id':league_id,'my_team_id':franchise_id,'my_team_name':team_name}, function(response) {					
						jQuery(location).html(response);
				});
			}

			jQuery(document).ready(function($) {
				$( document ).on( "click", ".<?php echo $this->slug; ?>-logout",
					function() {
						let sbutmitButton = $(this);
						let currentText = sbutmitButton.text();
						sbutmitButton.text('Logging Out...');
						sbutmitButton.attr('disabled', true);
					
						$.post(dtc.ajaxurl, {'action':'dtc_<?php echo $this->slug; ?>_ajax_logout', 'user_id':$(this).attr('data-id')},
							function(response) {
								// Making sure to clear out the values on logout
								localStorage.removeItem('dtc_league_name');
								localStorage.removeItem('dtc_league_id');

								// Ensure we stay on the Fantrax tab after logout
								localStorage.setItem('dtc_integration_name', 'fantrax');
								console.log(localStorage.getItem('dtc_integration_name'));
								window.IntegrationLeagueName = undefined;
								window.IntegrationLeagueId = undefined;

								window.rotoGPT_LeagueToken = undefined;
								window.rotoGPT_LeagueId = undefined;
								window.rotoGPT_LeagueName = undefined;
								window.rotoGPT_UserEmail = undefined;

								var event = new Event('IntegrationLeagueNameChanged');
								window.dispatchEvent(event);

								dtc_refresh_main_loader();
								dtc_ga_track('<?php echo $this->name; ?>','User Logout');
								sbutmitButton.text(currentText);
								sbutmitButton.attr('disabled', false);
							});

						return false;		
					}
				);

				$( document ).on( "click", ".<?php echo $this->slug; ?>-refresh-data",
					function() {
						$.post(dtc.ajaxurl, {'action':'dtc_<?php echo $this->slug; ?>_ajax_refresh_data', 'user_id':$(this).attr('data-id')},
							function(response) {					
								dtc_refresh_main_loader();
								alert("Refreshed League Data");
								dtc_ga_track('<?php echo $this->name; ?>','Refresh Data');
							});
				
						return false;
					}
				);

				$( document ).on( "change", ".<?php echo $this->slug; ?>_load_opposing_team", function() {
					dtc_<?php echo $this->slug; ?>_load_team(jQuery(this).attr('data-id'),jQuery(this).val(),jQuery('option:selected', this).attr('data-my-team-name'),".<?php echo $this->slug; ?>-import-team-two","right");	
				});

				$( document ).on( "change", ".fantrax_your_team", function() {
					$(".<?php echo $this->slug; ?>-import-team-two").empty();
					$(".<?php echo $this->slug; ?>-import-team-one").empty();	
					$(".<?php echo $this->slug; ?>_load_opposing_team").val("");
			
					$.post(dtc.ajaxurl, {'action':'dtc_ajax_<?php echo $this->slug; ?>_get_league_settings', 'league_id':$(this).val(),'my_team_id':$('option:selected', this).attr('data-my-team-id')}, function(response) {					
						$(".<?php echo $this->slug; ?>-league-settings").html(response);
						dtc_ga_track('<?php echo $this->name; ?>','League Settings');					
					});
			
					dtc_<?php echo $this->slug; ?>_load_team(jQuery(this).val(),jQuery('option:selected', this).attr('data-my-team-id'),jQuery('option:selected', this).attr('data-my-team-name'),".<?php echo $this->slug; ?>-import-team-one","left");
				
					$.post(dtc.ajaxurl, {'action':'dtc_ajax_<?php echo $this->slug; ?>_get_opposing_players_dropdown', 'league_id':$(this).val(),'my_team_id':$('option:selected', this).attr('data-my-team-id'),'my_team_name':$('option:selected', this).attr('data-my-team-name')}, function(response) {					
						$(".<?php echo $this->slug; ?>-opposing-teams").html(response);
					});
				});
			});
		</script>
	<?php
	}


	/****************************************************************************************
	 * 
	 * 
	 *              Fantrax Data Manipulation Functions
	 *              (Mapping it to what we need for DTC)
	 * 
	 * 
	 ****************************************************************************************/
    public function get_leagues()
    {
        global $current_user;
        $user_id = $current_user->ID;

        $leagues = [];

        $fantrax_user_secret = get_user_meta($user_id, 'fantrax_user_secret', true);
        $sport = 'NFL';

        if (empty($fantrax_user_secret)) {
            return $leagues;
        }

        $leagues_data = $this->getAllLeaguesForUser($fantrax_user_secret, $sport);

        if (!$leagues_data) {
            return $leagues; // No leagues or API failure
        }

        foreach ($leagues_data as $league) {
            $leagueId = $league['leagueId'] ?? null;
            $leagueName = $league['leagueName'] ?? null;
            $teamId = $league['teamId'] ?? null;
            $teamName = $league['teamName'] ?? null;

            // Keep these for structure compatibility — not used in Fantrax
            $fantraxLeagueTypeId = null;
            $fantraxLeagueType = null;

            $leagueTeam = new stdClass();
            $leagueTeam->leagueId = $leagueId;
            $leagueTeam->leagueName = $leagueName;
            $leagueTeam->teamId = $teamId;
            $leagueTeam->teamName = $teamName;
            $leagueTeam->fantraxLeagueTypeId = $fantraxLeagueTypeId;
            $leagueTeam->fantraxLeagueType = $fantraxLeagueType;

            $leagues[] = $leagueTeam;
        }

        return $leagues;
    }


    public function get_settings_for_league($league_id) {
		global $current_user;
		$user_id = $current_user->ID;
		$fantrax_user_secret = get_user_meta($user_id, 'fantrax_user_secret', true);
		$sport = 'NFL';
		
		// Retrieve all leagues for the current user.
		$leagues_data = $this->getAllLeaguesForUser($fantrax_user_secret, $sport);
		if (!$leagues_data) {
			return null; // No leagues available or API failure.
		}
		
		// Loop through the leagues to find the one matching the provided league ID.
		$foundLeague = null;
		foreach ($leagues_data as $league) {
			$leagueId = $league['leagueId'] ?? null;
			if ($leagueId === $league_id) {
				$foundLeague = $league;
				break;
			}
		}
		
		if (!$foundLeague) {
			// League not found.
			return null;
		}
		
		// Extract league name from the found league.
		$leagueName = $foundLeague['leagueName'] ?? '';
	
		// Retrieve extended league information for settings.
		$leagueInfo = $this->getLeagueInfo($league_id);
		if (!$leagueInfo) {
			return null;
		}
		
		$standings = $this->getStandings($league_id);
    	$league_size = is_array($standings) ? count($standings) : 0;
		
		// Process settings using the helper methods (which expect an associative array).
		$leagueFormat = $this->checkIfLeagueIsPPR($leagueInfo);
		$settings     = $this->getLeagueSettings($leagueInfo);
		$positions    = $this->getRosterPositions($leagueInfo);
		
		// Draft start time is not available.
		$draft_start_time = null;
		
		// Retrieve the league status.
		$leagueStatus = $this->getLeagueStatus($league_id);
		
		// Assemble and return an object with all the league settings.
		$leagueData = new stdClass();
		$leagueData->league_id        = $league_id;
		$leagueData->league_name      = $leagueName;
		$leagueData->league_size      = (int)$league_size;
		$leagueData->league_format    = $leagueFormat;
		$leagueData->settings         = $settings;
		$leagueData->draft_date       = $draft_start_time;
		$leagueData->league_status    = $leagueStatus;
		$leagueData->roster_positions = $positions;
		
		return $leagueData;
	}


    public function get_opponents($leagueId, $myTeamId): array
    {
        $opponents = [];

        $rosters = $this->getTeamRosters($leagueId);
        $leagueInfo = $this->getLeagueInfo($leagueId);

        if (!$rosters || !isset($rosters['rosters'])) {
            return [];
        }

        $leagueName = $leagueInfo['leagueName'] ?? '';

        foreach ($rosters['rosters'] as $teamId => $teamData) {
            if ((string) $teamId === (string) $myTeamId) {
                continue; // Skip user's team
            }

            $teamName = $teamData['teamName'] ?? 'Unknown';

            $team = new stdClass();
            $team->leagueId = $leagueId;
            $team->leagueName = $leagueName;
            $team->teamId = $teamId;
            $team->teamName = $teamName;

            $opponents[] = $team;
        }

        return $opponents;
    }
    

    public function get_team_roster(string $leagueId, string $teamId): array
    {

        $rostersResponse = $this->getTeamRosters($leagueId);
        $allPlayers = get_transient('fantrax_all_players_nfl');
        if (!$allPlayers) {
            $allPlayers = $this->getAllPlayers('NFL');
            set_transient('fantrax_all_players_nfl', $allPlayers, 12 * HOUR_IN_SECONDS);
        }

        if (!$rostersResponse || !$allPlayers || !isset($rostersResponse['rosters'][$teamId])) {
            return [];
        }

        $teamRoster = $rostersResponse['rosters'][$teamId]['rosterItems'] ?? [];
        $roster = [];

        // Convert allPlayers into a lookup table by fantraxId
        $playerLookup = [];
        foreach ($allPlayers as $player) {
            if (isset($player['fantraxId'])) {
                $playerLookup[$player['fantraxId']] = $player;
            }
        }

        foreach ($teamRoster as $item) {
            $playerId = $item['id'];
            $position = $item['position'] ?? 'BN';

            $playerData = $playerLookup[$playerId] ?? null;
            if (!$playerData) {
                continue; // skip if player data not found
            }

            // Parse name: "Lastname, Firstname"
            $fullName = $playerData['name'] ?? '';
            $nameParts = explode(',', $fullName);

            if (count($nameParts) < 2) {
                $firstName = $fullName;
                $lastName = '';
            } else {
                $lastName = trim($nameParts[0] ?? '');
                $firstName = trim($nameParts[1] ?? '');
            }
            
            $playerTeam = strtoupper($playerData['team'] ?? '');

            $currentPlayer = new stdClass();
            $currentPlayer->player_id = $playerId;
            $currentPlayer->player_first_name = $firstName;
            $currentPlayer->player_last_name = $lastName;
            $currentPlayer->player_team = $playerTeam;
            $currentPlayer->position = $position;

            $roster[] = $currentPlayer;
        }

        // Optional: sort by position
        $positionOrder = [
            'QB' => 1,
            'RB' => 2,
            'WR' => 3,
            'TE' => 4
        ];

        usort($roster, function ($a, $b) use ($positionOrder) {
            $aPos = $positionOrder[$a->position] ?? PHP_INT_MAX;
            $bPos = $positionOrder[$b->position] ?? PHP_INT_MAX;
            return $aPos <=> $bPos;
        });

        return $roster;
    }

	public function get_draft_picks(string $leagueId, string $teamId): array
    {
        $draftData = $this->getDraftPicks($leagueId);
        $standings = $this->getStandings($leagueId);

        if (!$draftData || !$standings) {
            return [];
        }

        // Determine team rank for pick formatting
        $teamRank = 5; // Default
        foreach ($standings['standings'] ?? [] as $entry) {
            if ($entry['teamId'] === $teamId && isset($entry['rank'])) {
                $teamRank = (int) $entry['rank'];
                break;
            }
        }

        $draftPicks = [];
        $currentYear = (int) date("Y");

        // Process currentDraftPicks
        foreach ($draftData['currentDraftPicks'] ?? [] as $pick) {
            if (
                $pick['teamId'] === $teamId &&
                isset($pick['round'], $pick['pick'])
            ) {
                $formatted = $pick['round'] . '.' . str_pad($pick['pick'], 2, '0', STR_PAD_LEFT);

                $draftPick = new stdClass();
                $draftPick->year = $currentYear;
                $draftPick->pickValue = $formatted;
                $draftPick->displayText = $currentYear . ' Draft Pick Round ' . $pick['round'];

                $draftPicks[] = $draftPick;
            }
        }

        // Process futureDraftPicks
        foreach ($draftData['futureDraftPicks'] ?? [] as $pick) {
            if (
                $pick['currentOwnerTeamId'] === $teamId &&
                isset($pick['round'], $pick['year'])
            ) {
                $formatted = $pick['round'] . '.' . str_pad($teamRank, 2, '0', STR_PAD_LEFT);

                $draftPick = new stdClass();
                $draftPick->year = (int) $pick['year'];
                $draftPick->pickValue = $formatted;

                if ($pick['currentOwnerTeamId'] === $pick['originalOwnerTeamId']) {
                    $draftPick->displayText = $pick['year'] . ' Draft Pick Round ' . $pick['round'];
                } else {
                    $draftPick->displayText = $pick['year'] . ' Draft Pick Round ' . $pick['round'] . ' from ' . $pick['originalOwnerTeamId'];
                }

                $draftPicks[] = $draftPick;
            }
        }

        return $draftPicks;
    }

	function ordinal($num) {
		// Special case "teenth"
		if ( ($num / 10) % 10 != 1 ) {
			// Handle 1st, 2nd, 3rd
			switch( $num % 10 ) {
				case 1: return $num . 'st';
				case 2: return $num . 'nd';
				case 3: return $num . 'rd';  
			}
		}

		// Everything else is "nth"
		return $num . 'th';
	}

    public function getLeagueSettings(array $leagueInfo): array
    {
        $settings = [
            "superflex" => false,
            "2QB" => false,
            "standard" => false,
            "TE_Premium" => false,
            "RB_PPC" => false
        ];

        // Handle position constraints
        $positionConstraints = $leagueInfo['rosterInfo']['positionConstraints'] ?? [];

        if (isset($positionConstraints['SFX'])) {
            $settings["superflex"] = true;
        }

        if (isset($positionConstraints['QB']) && ($positionConstraints['QB']['maxActive'] ?? 0) == 2) {
            $settings["2QB"] = true;
        }

        if (!$settings["superflex"] && !$settings["2QB"]) {
            $settings["standard"] = true;
        }

        // Scoring rules
        $scoringRules = $leagueInfo['scoringSystem']['scoringRules'] ?? [];

        foreach ($scoringRules as $rule) {
            $scoringCategory = $rule['scoringCategory']['code'] ?? null;
            if ($scoringCategory === "INDIVIDUAL_RUSHES") {
                $points = floatval($rule['points'] ?? 0);
                if ($points > 0) {
                    $settings["RB_PPC"] = true;
                }
            }
        }

        foreach ($scoringRules as $rule) {
            $position = $rule['position']['code'] ?? null;
            if ($position === "TIGHT_END") {
                $settings["TE_Premium"] = true;
                break;
            }
        }

        return $settings;
    }

    public function checkIfLeagueIsPPR(array $leagueInfo): string
    {
        if (!($leagueInfo['ppr'] ?? false)) {
            return "Non-PPR";
        }

        $scoringRules = $leagueInfo['scoringSystem']['scoringRules'] ?? [];

        foreach ($scoringRules as $rule) {
            $scoringCategory = $rule['scoringCategory']['code'] ?? null;

            if ($scoringCategory === "INDIVIDUAL_RECEPTIONS") {
                $scoringValues = $rule['scoringValue'] ?? [];

                if (empty($scoringValues)) {
                    return "Non-PPR";
                }

                $fantasyPoints = floatval($scoringValues[0]['fantasyPoints'] ?? 0);

                if ($fantasyPoints === 0.0) {
                    return "Non-PPR";
                } elseif ($fantasyPoints === 0.5) {
                    return ".5 PPR";
                } elseif ($fantasyPoints >= 1.0) {
                    return "PPR";
                } else {
                    return "Unknown";
                }
            }
        }

        return "PPR"; // fallback if INDIVIDUAL_RECEPTIONS found, but not evaluable
    }

    function getLeagueStatus($leagueId) {
        // Default response. Check with Fantrax
        return "pre_draft";
    }


    public function getRosterPositions(array $leagueInfo): array
    {
        $positionList = [];

        $mapping = [
            "SFX" => "SUPER_FLEX",
            "DST" => "DEF",
            "RWT" => "FLEX"
        ];

        $desiredOrder = ["QB", "RB", "WR", "TE", "RWT", "SFX", "DST", "K"];
        $positionConstraints = $leagueInfo['rosterInfo']['positionConstraints'] ?? [];

        foreach ($desiredOrder as $pos) {
            if (isset($positionConstraints[$pos])) {
                $count = (int) ($positionConstraints[$pos]['maxActive'] ?? 0);
                $mapped = $mapping[$pos] ?? $pos;

                for ($i = 0; $i < $count; $i++) {
                    $positionList[] = $mapped;
                }
            }
        }

        $benchCount = $leagueInfo['rosterInfo']['maxTotalReservePlayers'] ?? 0;
        for ($i = 0; $i < $benchCount; $i++) {
            $positionList[] = 'BN';
        }

        return $positionList;
    }
    
    


    



	/****************************************************************************************
	 * 
	 * 
	 *              Fantrax Web API Functions
	 *              (Retrieving the data from Fantrax)
	 * 
	 * 
	 ****************************************************************************************/
    private function makeRequest(string $endpoint, array $queryParams = []): ?array
    {
        $url = $this->api_url . $endpoint;

        if (!empty($queryParams)) {
            $url .= '?' . http_build_query($queryParams);
        }

        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FAILONERROR => true,
        ]);

        $response = curl_exec($curl);

        if (curl_errno($curl)) {
            // echo "Request failed: " . curl_error($curl);
            curl_close($curl);
            return null;
        }

        curl_close($curl);

        $decoded = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            // echo "JSON decoding failed: " . json_last_error_msg();
            return null;
        }

        return $decoded;
    }

    public function getAllPlayers(string $sport): ?array
    {
        return $this->makeRequest("getPlayerIds", ['sport' => $sport]);
    }

    public function getAllLeaguesForUser(string $userSecretId, string $sport): ?array
    {
        $data = $this->makeRequest("getLeagues", ['userSecretId' => $userSecretId]);
        if (!$data || !isset($data['leagues'])) return null;

        // Filter by sport
        return array_filter($data['leagues'], function ($league) use ($sport) {
            return $league['sport'] === $sport;
        });
    }

    public function getLeagueInfo(string $leagueId): ?array
    {
        return $this->makeRequest("getLeagueInfo", ['leagueId' => $leagueId]);
    }

    public function getDraftPicks(string $leagueId): ?array
    {
        return $this->makeRequest("getDraftPicks", ['leagueId' => $leagueId]);
    }

    public function getDraftResults(string $leagueId): ?array
    {
        return $this->makeRequest("getDraftResults", ['leagueId' => $leagueId]);
    }

    public function getTeamRosters(string $leagueId, ?int $period = null): ?array
    {
        $params = ['leagueId' => $leagueId];
        if ($period !== null) {
            $params['period'] = $period;
        }
        return $this->makeRequest("getTeamRosters", $params);
    }

    public function getStandings(string $leagueId): ?array
    {
        return $this->makeRequest("getStandings", ['leagueId' => $leagueId]);
    }

    // GETS THE NFL STATE FROM THE SLEEPER API
    public function getNFLState($sport)
    {
        $settings = get_option('dtc_settings');	
		$api_url =$settings['sleeper_api_url'];

        $url = $api_url . "state/" . $sport;

        $ch = curl_init();

        // Set cURL options
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);

        $response = curl_exec($ch);

        // Check for errors
        if (curl_errno($ch)) {
            echo "cURL Error: " . curl_error($ch);
            curl_close($ch);
            return null;
        }

        curl_close($ch);

        // Convert the JSON response to stdClass
        $data = json_decode($response);

        if (json_last_error() !== JSON_ERROR_NONE) {
            echo "JSON Error: " . json_last_error_msg();
            return null;
        }

        return $data;
    }

}