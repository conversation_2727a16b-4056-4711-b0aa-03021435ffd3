<?php 

$dtc_mfl = new dtc_mfl;

add_shortcode("import_team", array($dtc_mfl,'view'));
add_action('dtc_calculator_bottom', array($dtc_mfl,'view'));
add_action( 'wp_ajax_dtc_ajax_get_league_players', array($dtc_mfl,'ajax_get_league_players'));	
add_action( 'wp_ajax_nopriv_dtc_ajax_get_league_players', array($dtc_mfl,'ajax_get_league_players'));

add_action( 'wp_ajax_dtc_ajax_mfl_api_get_league_settings', array($dtc_mfl,'ajax_get_league_settings'));	
add_action( 'wp_ajax_dtc_ajax_get_opposing_players_dropdown', array($dtc_mfl,'ajax_get_opposing_players_dropdown'));	
add_action( 'wp_ajax_nopriv_dtc_ajax_get_opposing_players_dropdown', array($dtc_mfl,'ajax_get_opposing_players_dropdown'));

add_action( 'wp_ajax_dtc_ajax_refresh_data', array($dtc_mfl,'ajax_refresh_user_league'));	
add_action( 'wp_ajax_nopriv_dtc_ajax_refresh_data', array($dtc_mfl,'ajax_refresh_user_league'));

add_action( 'wp_ajax_dtc_ajax_mfl_main_loader', array($dtc_mfl,'ajax_main_loader'));	
add_action( 'wp_ajax_nopriv_dtc_ajax_mfl_main_loader', array($dtc_mfl,'ajax_main_loader'));

add_action( 'wp_ajax_dtc_ajax_mfl_login', array($dtc_mfl,'ajax_login'));	
add_action( 'wp_ajax_nopriv_dtc_ajax_mfl_login', array($dtc_mfl,'ajax_login'));

add_action( 'wp_ajax_dtc_mfl_ajax_logout', array($dtc_mfl,'ajax_logout'));	
add_action( 'wp_ajax_nopriv_dtc_mfl_ajax_logout', array($dtc_mfl,'ajax_logout'));

add_action('run_mfl', array($dtc_mfl,'update_bye_weeks'));

add_action('wp', array($dtc_mfl,'restrict_pages'));

add_action('dtc_get_all_apis', array($dtc_mfl,'dtc_get_all_apis'));

add_action('wp',array($dtc_mfl,'get_dtc_trades'));
## add_action('wp',array($dtc_mfl,'get_dtc_trade_leagues'));

class dtc_mfl {
	protected $mfl_user_agent;
	protected $current_year;
	protected $db_field;
	protected $endpoint;
	protected $slug;
	protected $name;

	
	public function __construct() {
		$settings = get_option('dtc_settings');
		$this->current_year = date("Y");
		$this->db_field = 'mfl_id';
		$this->endpoint =$settings['mfl_api_url'];
		$this->slug = 'mfl_api';
		$this->name =  'MFL API';
		$this->mfl_user_agent = 'DTC_FFB';
		
		if ( ! empty( $_GET['fix_leauge_rules'] ) && $_GET['fix_leauge_rules'] ==1) {
			# $this->get_dtc_trade_team_rules();
			$rules = $this-> fix_public_rules();
			print_r($rules);
			exit;
		}	
	
		if ( ! empty( $_GET['zup'] ) && $_GET['zup'] ==1) {
			#$this->get_dtc_trade_team_rules();
			#$rules = $this-> fix_public_rules();
			#print_r($rules);
			#exit;
		}
	
		if ( ! empty( $_GET['yup'] ) && $_GET['yup'] == 1) {
			#$this->get_dtc_trade_leagues();
		}
	}

	function menu() {
		add_submenu_page( 'dtc-settings', 'MFL League Import', 'MFL League Import', 'manage_options', 'dtc-tools-league-import', array($this, 'league_import'));	
	}
	
	function league_import() {
		
	}

	function get_mfl_leagues() {
		global $wpdb;
		$rp = $wpdb->get_results("SELECT * FROM  " . $wpdb->prefix . "dtc_mfl_leagues ", ARRAY_A);
		
		$leagues = array();
		if ($rp) {
			foreach($rp as $r){	
				$leagues[] = $r['league_id'];	
			}
		}
		
		return $leagues;
	}

	function franchise_to_teamname($league_id,$franchise_id) {
		$leagues = wp_remote_get('http://www03.myfantasyleague.com/'.$this->current_year.'/export?TYPE=league&L='.$league_id.'&APIKEY=&JSON=1');
		$name = '';
		$league = $leagues['body'];
		
		foreach($league->franchises->franchise as $franchise) {
			if ($franchise_id == $franchise->id) {
				$name = $franchise->name;	
			}
		}

		return $name;
	}

	function league_exists($id) {
		global $wpdb;
		
		$rp = $wpdb->get_results($wpdb->prepare("SELECT * FROM  " . $wpdb->prefix . "dtc_mfl_leagues where league_id = %d", $id), ARRAY_A);
		if ($rp == false) {
			return false;	
		} else {
			return $rp[0]['id'];	
		}
	}

	function maybe_update_team_rules($league_id) {
		global $wpdb;
		
		$leagues = $wpdb->get_results($wpdb->prepare("SELECT * FROM  " . $wpdb->prefix . "dtc_mfl_leagues where league_id = %d",$league_id), ARRAY_A);
		
		if ($leagues[0]['league_size'] == '') {
			$league = $this->get_league( $l['league_id'], 'league');	
			$rules = $this->get_league($l['league_id'], 'rules');
			$rules_array = $this->get_rules($league,$rules);	
			
			$update['league_format'] =$rules_array['team_format'];
			$update['league_size'] =$rules_array['team_size'];
			$update['league_type'] = $rules_array['team_type'];
			$update['tepre'] = $rules_array['tepre'];
			$update['rbppc'] =$rules_array['rbppc'];
			
			$wpdb->update("" . $wpdb->prefix . "dtc_mfl_leagues",$update,array('league_id' =>$league_id));
			$leagues = $wpdb->get_results($wpdb->prepare("SELECT * FROM  " . $wpdb->prefix . "dtc_mfl_leagues where league_id = %d",$league_id), ARRAY_A);
		}
		
		return $leagues[0];
	}

	function get_dtc_trade_team_rules() {
		global $wpdb;
		
		$leagues = $wpdb->get_results("SELECT * FROM  " . $wpdb->prefix . "dtc_mfl_leagues where league_format IS NULL AND private = 0", ARRAY_A);
		
		if ($leagues) {
			foreach($leagues  as $l) {
				$league = $this->get_league( $l['league_id'], 'league');	
				
				if (isset($league->Error)) {
					return;
				}

				if ($league != '') {
					$rules = $this->get_league($l['league_id'], 'rules');
					$rules_array = $this->get_rules($league,$rules);
				
					$update['league_format'] =$rules_array['team_format'];
					$update['league_size'] =$rules_array['team_size'];
					$update['league_type'] = $rules_array['team_type'];
					$update['tepre'] = $rules_array['tepre'];
					$update['rbppc'] =$rules_array['rbppc'];
					
					$wpdb->update("" . $wpdb->prefix . "dtc_mfl_leagues",$update,array('league_id' =>$l['league_id']));
				} else {
					#$update['private'] =1;
					#$wpdb->update("" . $wpdb->prefix . "dtc_mfl_leagues",$update,array('league_id' =>$l['league_id']));
				}
			}
		}
		
	}
  

	function fix_public_rules() {
		global $wpdb;
		
		$leagues = $wpdb->get_results("SELECT * FROM  " . $wpdb->prefix . "dtc_mfl_leagues where league_size IS NULL AND private = 0 limit 20", ARRAY_A);
		
		if ($leagues) {
			foreach($leagues  as $l) {
				sleep(2);
			
				$return = 	$this->get_dtc_trade_team_rules_by_id($l['league_id']);
				echo $l['league_id'];echo '<br>';
				print_r($return);
			}
		}

		if (count($leagues)>0) {
			echo '
			<script type="text/javascript">
				location.reload();
			</script>
			';
		}
	}
	
	function get_dtc_trade_team_rules_by_id($league_id) {
		global $wpdb;
	
		$request = wp_remote_get($this->endpoint.'export?TYPE=league&L='.$league_id.'&APIKEY=&JSON=1&t='.time().'');
		$body = json_decode($request['body']);
		sleep(2);
		
		if (isset($body->error)) {
			$update['private'] =1;
			$wpdb->update("" . $wpdb->prefix . "dtc_mfl_leagues",$update,array('league_id' =>$league_id));
			$update['error'] = $body;
			
			return $update;	
		}

		$league = $body->league;
	
		if ($league != '') {
			#$rules = $this->get_league($league_id, 'rules');
			$request_rules = wp_remote_get($this->endpoint.'export?TYPE=rules&L='.$league_id.'&APIKEY=&JSON=1&t='.time().'');
			$body_rules = json_decode($request_rules['body']);

			$rules = $body_rules->rules;
			$rules_array = $this->get_rules($league,$rules);
		
			$update['league_format'] =$rules_array['team_format'];
			$update['league_size'] =$rules_array['team_size'];
			$update['league_type'] = $rules_array['team_type'];
			$update['tepre'] = $rules_array['tepre'];
			$update['rbppc'] =$rules_array['rbppc'];
			
			$wpdb->update("" . $wpdb->prefix . "dtc_mfl_leagues",$update,array('league_id' =>$league_id));
    		$update['post'] = $league ;
			
    		return $update;
		} else {
			# $update['private'] =1;
			#$wpdb->update("" . $wpdb->prefix . "dtc_mfl_leagues",$update,array('league_id' =>$league_id));
		}
	}  
    
	function get_dtc_trade_leagues() {
		if($_GET['run_trades_leagues'] == 1) {   
			global $wpdb;
			
			if (isset($_GET['keyword'])) {
				$keyword = sanitize_text_field( $_GET['keyword'] ); 
			} else {
				$keyword = 's';
			}
			
			$leagues = wp_remote_get('http://www03.myfantasyleague.com/'.$this->current_year.'/index?YEAR='.$this->current_year.'&SEARCH='.$keyword.'');
			
			echo 'http://www03.myfantasyleague.com/'.$this->current_year.'/index?YEAR='.$this->current_year.'&SEARCH='.$keyword.'';
			$league_html = $leagues['body'];
			
			$dom = new DOMDocument;
			$dom->loadHTML($league_html);
			$installed_leagues = $this->get_mfl_leagues();
			
			$count = 0;
			
			foreach ($dom->getElementsByTagName('a') as $node) {
				if (strpos(strtolower($node->nodeValue), strtolower($keyword) ) !== false) {
					$parse_url = explode("/",$node->getAttribute("href"));
					$league_id = end($parse_url);

					echo  $node->nodeValue; 
					if (!in_array($league_id,$installed_leagues)) {
						$update['league_name'] = $node->nodeValue;
						$update['league_url'] = $node->getAttribute("href");
						$update['league_id'] = $league_id;
						$wpdb->insert("" . $wpdb->prefix . "dtc_mfl_leagues",$update);	
						#$this->get_dtc_trade_team_rules_by_id($league_id);
						$count++;
					}

				} else {
					$update['league_name'] = $node->nodeValue;
					$update['league_url'] = $node->getAttribute("href");
					$where['league_id'] = $league_id;
					$wpdb->update("" . $wpdb->prefix . "dtc_mfl_leagues",$update,$where);	
				}
			} 
		
			echo $count;
			# $this->get_dtc_trade_team_rules();
			die();
		}
	}

	function get_dtc_trades() {
		if ( ! empty( $_GET['run_trades'] ) && $_GET['run_trades'] == 1) {
			global $wpdb;	
			$keyword = 'dynasty';
			$query = "SELECT * FROM  " . $wpdb->prefix . "dtc_mfl_leagues WHERE (trades_processed < '".date("Y-m-d")."' or trades_processed IS NULL)AND league_size IS NOT NULL AND private = 0 limit 50";
			
			$league_chunk = $wpdb->get_results($query, ARRAY_A);
			if ($league_chunk == false) {
				#$this->get_dtc_trade_team_rules();	
				die( 'Finsihed');	
			}
	
			$plarray = array();
			$players = $wpdb->get_results("SELECT * FROM  " . $wpdb->prefix . "dtc_players ", ARRAY_A);
			foreach($players as $player){
				$plarray[$player['mfl_id']] =stripslashes($player['name']);
			}
	
			$full_trades = array();
	
			foreach($league_chunk as $league_arr) {
				$installed_league = $league_arr['league_id'];
				$trades = wp_remote_get('http://www66.myfantasyleague.com/'.$this->current_year.'/export?TYPE=transactions&L='.$installed_league.'&APIKEY=&W=&TRANS_TYPE=TRADE&FRANCHISE=&DAYS=7&COUNT=&JSON=1');
	 			echo 'http://www66.myfantasyleague.com/'.$this->current_year.'/export?TYPE=transactions&L='.$installed_league.'&APIKEY=&W=&TRANS_TYPE=TRADE&FRANCHISE=&DAYS=7&COUNT=&JSON=1';
		
				$trades_body = json_decode($trades['body']);
		
				if ($trades_body != '') {
					print_r($trades_body);
					if ($trades_body->transactions->transaction) {
						foreach($trades_body->transactions->transaction as $transaction) {
							/*
							$player1 = array_filter(explode(",",$transaction->franchise1_gave_up));
							$player2 = array_filter(explode(",",$transaction->franchise2_gave_up));
							$player1_gave_up = '';
							if(count($player1)>0){
								foreach($player1 as $player_id){
									if (strpos($player_id, 'DP') !== false) {
										$player1_gave_up .= ''.$player_id.',';	
									}else{
										if(isset($plarray[$player_id])){
											$player1_gave_up .= ''.@$plarray[$player_id].',';
										}
									}
								}
							}

							$player2_gave_up = '';
							if(count($player2)>0){
									foreach($player2 as $player_id){
										if (strpos($player_id, 'DP') !== false) {
											$player2_gave_up .= ''.$player_id.',';	
										}else{
											if(isset($plarray[$player_id])){
												$player2_gave_up .= ''.@$plarray[$player_id].',';
											}
										}
									}
							}
							*/
		
							$full_trades[$installed_league][] = array(
								'league_id'=>$installed_league,
								'franchise2_gave_up'=>$transaction->franchise2_gave_up,
								
								'franchise2'=>$transaction->franchise2,																
								'timestamp'=>$transaction->timestamp,
								'franchise1_gave_up'=>$transaction->franchise1_gave_up,
								
								'franchise'=>$transaction->franchise,
																
							);
						}
					} else {
						$full_trades[$installed_league][] = array();	
					}
				}
			}

			$total_trades = 0;
			
			foreach($full_trades as $key=>$trades) {
				if (count($trades)>0) {
					for ($i = 0; $i < count($trades); $i++) {
						if ($trades[$i]['league_id'] != '') {
							$wpdb->insert('' . $wpdb->prefix . 'dtc_mfl_trades',$trades[$i] );
							$total_trades ++;
						}
					}
				}
					
				$wpdb->update('' . $wpdb->prefix . 'dtc_mfl_leagues', array('trades_processed'=>date("Y-m-d")), array('league_id'=>$key));
			}
	
			if (count($league_chunk)>0) {
				echo '
				<script type="text/javascript">
					location.reload();
				</script>
				';
			}
			
			die('Processed '.$total_trades.' Trades');
		}

	}

	function dtc_get_all_apis($apis) {
		$apis[$this->db_field] 	= $this->name;
		return $apis;
	}

	function restrict_pages() {
		global $current_user, $post;
			
		$restrict_pages = array(30349);
		
		if ( ! empty( $post->ID ) && in_array($post->ID,$restrict_pages ) && !is_admin() && is_user_logged_in()  == false) {
			wp_redirect('/login/?redirect=/import-team/');	
		}
	}

	function update_bye_weeks() {
		global $wpdb;

		$request = wp_remote_get($this->endpoint.'export?TYPE=nflByeWeeks&W=&JSON=1');
		$body = json_decode($request['body']);	
		
		foreach($body->nflByeWeeks->team as $team) {
			$rp = $wpdb->get_results($wpdb->prepare("SELECT * FROM  " . $wpdb->prefix . "dtc_players_bye where id = %s", $team->id), ARRAY_A);
			
			if ($rp == false) {
				$insert['id'] = $team->id;
				$insert['bye'] = $team->bye_week;	
				$wpdb->insert("" . $wpdb->prefix . "dtc_players_bye", $insert);
			} else {
				$insert['bye'] = $team->bye_week;	
				$where['id'] = $team->id;	
				$wpdb->update("" . $wpdb->prefix . "dtc_players_bye", $insert, $where);	
			}
		}
		
	}
	
	function ajax_login() {
		$this->request_cookie(sanitize_text_field( $_POST['mfl-username'] ), sanitize_text_field( $_POST['mfl-password'] ));	
		die();
	}

	function ajax_main_loader() {
		global $current_user;
		$user_id = $current_user->ID;

		// set_transient('_selected_integration_user_' . $user_id, 'mfl', 24 * HOUR_IN_SECONDS);

		if ($this->login() === false) {
			$login_cookie = get_user_meta($current_user->ID, '_mfl_login_cookie', true);
			$leagues = $this->get_leagues($current_user->ID);
			
			if (count($leagues->league) > 0) {
				if (is_array($leagues->league)) {
					$user_leagues = $leagues->league;
				} else {
					$user_leagues = array($leagues->league);
				}
			}

			echo '
			<h1>My Fantasy League</h1>
			<div class="dtc-mfl-nav">
				<a href="#" class="mfl-refresh-data mfl-button" data-id="' . $current_user->ID . '">Refresh Data</a>
				<a href="#" class="mfl-logout mfl-button" data-id="' . $current_user->ID . '">Logout</a>
			</div>
			';

			if (isset($leagues->Error)) {
				echo '<div id="leagues-json-response" style="display:none;">' . $leagues->Error . ' - ' . htmlspecialchars(json_encode($leagues->Response), ENT_QUOTES, 'UTF-8') . '</div>';
			}

			if (count($leagues->league) > 0) {
				echo '
				<div class="dtc-mfl-half">
					<div class="dtc-half-inner">
				';

				echo $this->league_dropdown($user_leagues, 'mfl_league_id');
				
				echo '
					</div>
				</div>
				';
				
				echo '
				<div class="dtc-mfl-half">
					<div class="dtc-half-inner">
						<div class="mf-opposing-teams">
							<select disabled="disabled">
								<option value="">Your Competitor</option>
							</select>
						</div>
					</div>
				</div>
				
				<div style="clear:both"></div>
				
				<div class="mfl-import-calc">
					<a href="#" class="mfl-import-calc-button" data-id="' . $this->name . '">Import to Calc</a>
				</div>

				<div class="' . $this->slug . '-league-settings"></div>
				
				<div class="mfl-team-gutter">
					<div class="dtc-mfl-half">
						<div class="dtc-half-inner">
							<div class="mfl-import-team-one"></div>
						</div>
					</div>
					<div class="dtc-mfl-half">
						<div class="dtc-half-inner">
							<div class="mfl-import-team-two"></div>
						</div>
					</div>
					<div style="clear:both"></div>
				</div>

				<a href="#" class="mfl-import-calc-button" style="margin-bottom:20px">Import to Calc</a>
				';
			} else {
				// Removing this for now. If they have a valid login then why do we need to force a logout?
				// $this->ajax_logout();
				echo 'You have no leagues!';
			}
		} else {
			echo $this->login();
		}

		die();
	}

	function ajax_refresh_user_league() {
		global $wpdb, $current_user;
		
		if ($current_user->ID) {
			$wpdb->query("DELETE FROM ".$wpdb->prefix . "options WHERE option_name like '%mfl_user_".$current_user->ID."%'");	
		} else {

		}
	
		die();	
	}

	function ajax_logout(){
		global $wpdb, $current_user;

		$wpdb->query("DELETE FROM ".$wpdb->prefix . "options WHERE option_name like '%mfl_user_".$current_user->ID."%'");	
		delete_user_meta($current_user->ID,'_mfl_login_cookie');
	}

	function ajax_get_league_settings() {
		global $current_user;

		$league_id = $_POST['league_id'];
		
		if ($league_id == '') {
		    die();
		}
		$login_cookie =  get_user_meta($current_user->ID,'_'.$this->slug.'_login_cookie',true);
		$league = $this->get_league($league_id, 'league');
		$rules = $this->get_league($league_id, 'rules');
		$rules_array = $this->get_rules($league,$rules);
		
		if (isset($league->Error)) {
			echo '<div id="league-json-response" style="display:none;">' . $league->Error . ' - ' . htmlspecialchars(json_encode($league->Response), ENT_QUOTES, 'UTF-8') . '</div>';
		}

		if (isset($rules->Error)) {
			echo '<div id="rules-json-response" style="display:none;">' . $rules->Error . ' - ' . htmlspecialchars(json_encode($rules->Response), ENT_QUOTES, 'UTF-8') . '</div>';
		}

		if($rules_array['tepre'] >=1){ $tepre = 'Yes'; }else{ $tepre = 'No'; }
		if($rules_array['rbppc'] >=1){ $rbppc = 'Yes'; }else{ $rbppc = 'No'; }

		/* NOT NEEDED? dtc_number_to_name doesn't exist and $value_key never used
		if($rules_array['team_type']=='2qb'|| $rules_array['team_type']=='sf'){
			$value_key = $team_size.'sf';
		} else {
			$value_key =dtc_number_to_name($rules_array['team_size']);
		}
		*/

		echo '
		<input type="hidden" class="mfl-rule-team-type" value="'.$rules_array['team_type'].'">
		<input type="hidden" class="mfl-rule-team-format" value="'.$rules_array['team_format'].'">
		<input type="hidden" class="mfl-rule-team-size" value="'.$rules_array['team_size'].'">
		<input type="hidden" class="mfl-rule-team-tepre" value="'.$rules_array['tepre'].'">
		<input type="hidden" class="mfl-rule-team-rbppc" value="'.$rules_array['rbppc'].'">';
		
		$ppr_type = '';
		if ($rules_array['team_type'] == 'ppr') {
			$ppr_type = 'PPR';
		} elseif ($rules_array['team_type'] == 'half_ppr') {
			$ppr_type = 'HALF PPR';
		} else {
			$ppr_type = 'NON PPR';
		}

		echo '
		<div class="mfl-league_settings">
			<span><strong>Type: </strong> '.$ppr_type.'</span>
			<span><strong>Format: </strong> '.strtoupper($rules_array['team_format']).'</span>
			<span><strong>Size: </strong> '.$rules_array['team_size'].'tm</span>
			<span><strong>TE Premium? </strong> '.$tepre.'</span>
			<span><strong>RB PPC? </strong> '.$rbppc.'</span>
		</div>
		';

		die();
	}

	function ajax_get_opposing_players_dropdown() {
		global $current_user;

		$league_id = $_POST['league_id'] ?? '';
		$my_team_id = $_POST['my_team_id'] ?? '';
		$login_cookie =  get_user_meta($current_user->ID,'_mfl_login_cookie',true);
		$league = $this->get_league($league_id);
		
		if (isset($league->Error)) {
			echo '<div id="league-json-response" style="display:none;">' . $league->Error . ' - ' . htmlspecialchars(json_encode($league->Response), ENT_QUOTES, 'UTF-8') . '</div>';
		}

		echo $this->team_dropdown($league,$league_id,'mfl_load_opposing_team', $my_team_id);
		
		die();
	}

	function get_pick($pick_id) {
		global $wpdb;
	
		// Try to get the exact pick
		$r = $wpdb->get_results($wpdb->prepare("SELECT * FROM wp_dtc_draft_picks WHERE pick = %s", $pick_id), ARRAY_A);
	
		if (!empty($r)) {
			return $r[0]; // Return the exact match
		}
	
		// Extract year and round from the pick_id
		if (preg_match('/^(\d{4}) (\d+)\.\d+$/', $pick_id, $matches)) {
			$year = $matches[1];
			$round = (int) $matches[2];
	
			// Construct the "Mid" value
			$mid_pick = sprintf("%s %s (Mid)", $year, $this->ordinal($round));
	
			// Try to find the mid pick
			$r = $wpdb->get_results($wpdb->prepare("SELECT * FROM wp_dtc_draft_picks WHERE pick = %s", $mid_pick), ARRAY_A);
			
			return $r[0] ?? '';
		}
	
		return '';
	}

	function get_player($player_id) {
		global $wpdb;

		$query =  $wpdb->prepare("SELECT * FROM (SELECT * FROM " . $wpdb->prefix . "dtc_players UNION SELECT ALL * FROM " . $wpdb->prefix . "dtc_players_devy UNION SELECT ALL * FROM " . $wpdb->prefix . "dtc_players_idp) as t where mfl_id = %s", $player_id);
		$r = $wpdb->get_results($query, ARRAY_A);
		
		return $r[0] ?? '';
	}

	function calculate_player_score($scores,$player_id) {
		$total_score = 0;

		foreach($scores->playerScore as $score) {
			if ($score->id == $player_id && $score->score != '') {
				$total_score += $score->score;
			}
		}

		return $total_score;
	}

	function get_bye_week($team) {
		global $wpdb;	
		$query = $wpdb->prepare("SELECT * FROM  " . $wpdb->prefix . "dtc_players_bye where id = %s", $team);

		$r = $wpdb->get_results($query, ARRAY_A);
		
		if ($r) {
			return $r[0]['bye'];
		} else {
			return false;	
		}
	}

	function get_asset_picks( $assets, $franchise_id ) {
		$picks_array = array();
		$unique_picks = array(); // Track unique picks to prevent duplicates

		if ( ! empty( $assets->franchise ) ) {
			foreach($assets->franchise as $franchise) {
				if ($franchise_id == $franchise->id) {
					if ( ! empty( $franchise->currentYearDraftPicks ) ) {
						if ( ! empty( $franchise->currentYearDraftPicks->draftPick ) ) {
							$draftPicks = $franchise->currentYearDraftPicks->draftPick ?? '';

							// If there's only one item it was returning an object and NOT an array
							if (!is_array($draftPicks)) {
								$draftPicks = [$draftPicks];
							}

							foreach($draftPicks as $picks) {
								$pick_parts = explode("_",$picks->pick);

								#print_r($picks);
								$round = $pick_parts[1] + 1;
								$pick = $pick_parts[2] + 1;
								if(strlen($pick) == 1){
									$pick = '0'.$pick;
								}

								// Create unique identifier for this pick using year and round
								// This will catch duplicates between current and future year picks
								$unique_key = $this->current_year . '_' . $round;

								// Only add if we haven't seen this pick before
								if (!isset($unique_picks[$unique_key])) {
									$unique_picks[$unique_key] = true;
									$picks_array[] = array('year'=>$this->current_year,'round'=>$round,'pick'=>$pick,'description'=>$picks->description);
								}
							}
						}
					}

					if ( ! empty( $franchise->futureYearDraftPicks ) ) {
						if ( ! empty( $franchise->futureYearDraftPicks->draftPick ) ) {
							$draftPicks = $franchise->futureYearDraftPicks->draftPick;

							// If there's only one item it was returning an object and NOT an array
							if (!is_array($draftPicks)) {
								$draftPicks = [$draftPicks];
							}

							foreach($draftPicks as $picks) {
								$pick_parts = explode("_",$picks->pick);
								#print_r($picks);

								$year = $pick_parts[2];
								$round = $pick_parts[3];
								$pick = '0'; // Future picks don't have specific pick numbers

								// Create unique identifier for this pick using year and round
								// This will catch duplicates between current and future year picks
								$unique_key = $year . '_' . $round;

								// Only add if we haven't seen this pick before
								if (!isset($unique_picks[$unique_key])) {
									$unique_picks[$unique_key] = true;
									$picks_array[] = array('year'=>$year,'round'=>$round,'pick'=>$pick,'description'=>$picks->description);
								}
							}
						}
					}
				}
			}
		}

		return $picks_array;
	}

	function remove_star($val) {
		return str_replace("*","",$val);	
	}

	function get_rules($league,$rules) {
		$rules_array = array();
		#print_r($rules);
		#print_r($league);
		
		$rules_array['ppr'] = 0;
		$rules_array['half_ppr'] = 0;
		$rules_array['non_ppr'] = 0;
		
		$rules_array['standard'] = 0;
		$rules_array['sf'] = 0;
		$rules_array['qb2'] = 0;
		$rules_array['tepre'] = 0;
		$rules_array['rbppc'] = 0;
		
		if( ! empty( $rules->positionRules ) ) {
			foreach($rules->positionRules as $positionRules) {
				if ($positionRules->positions== 'TE') {
					if ($positionRules->rule) {
						foreach($positionRules->rule as $rule){
							if ( ! empty( $rule->{'$t'} ) && $rule->{'$t'} == 'CC' ) {
								$rules_array['tepre'] +=1;	
							}
						}
					}	
				}
						
				if ($positionRules->rule) {
					foreach($positionRules->rule as $rule) {
						if ( ! empty( $rule->event->{'$t'} ) && $rule->event->{'$t'} == 'CC') {
							if ($rule->points->{'$t'} == '*.5') {
								$rules_array['half_ppr'] += 1;	
							}
							
							if ($rule->points->{'$t'} == '*1') {
								$rules_array['ppr'] += 1;	
							}
						}
							
						if ( ! empty( $rule->event->{'$t'} ) && $rule->event->{'$t'} == 'RA') {
							if ($rule->points->{'$t'} != '') {
								$rules_array['rbppc'] += 1;	
							}
						}
					}
				}
					
				#print_r( $positionRules);		
			}
		}

		$rules_array['team_size'] = $league->franchises->count ?? '';	
			
		if ( ! empty( $league->starters->position ) ) {
			foreach($league->starters->position as $position) {
				if ($position->name == 'QB') {
					if ($position->limit == '1') {
						$rules_array['standard'] +=1;
					}
					
					if ($position->limit == '1-2') {
						$rules_array['sf'] +=1;
					}

					if ($position->limit == '2') {
						$rules_array['qb2'] +=1;
					}
				}
			}
		}
			
		if ($rules_array['ppr'] >=1) {
			$rules_array['team_type'] = 'ppr';	
		} elseif($rules_array['half_ppr'] >=1) {
			$rules_array['team_type'] = 'half_ppr';	
		} else {
			$rules_array['team_type'] = 'nonppr';	
		}
		
		$rules_array['team_format'] = 'standard';		
		
		if ($rules_array['sf'] >=1) {
			$rules_array['team_format'] = 'sf';	
		}
		
		if ($rules_array['qb2'] >=1) {
			$rules_array['team_format'] = '2qb';	
		}
			
		return $rules_array;
	}

	function ajax_get_league_players() {
		global $wpdb, $current_user;
		
		$league_id = $_POST['league_id'] ?? '';
		$my_team_id = $_POST['my_team_id'] ?? '';
		
		if ($league_id == '' || $my_team_id == '') {
		    die();
		}
		
		$login_cookie = get_user_meta($current_user->ID, '_mfl_login_cookie', true);

		$maxRuns = 10;
		$attempt = 0;
		$shouldRetrieveData = true;
		
		while ($shouldRetrieveData && $attempt < $maxRuns) {
			$attempt++;
		
			$team = $this->get_team($league_id, $my_team_id, $login_cookie, 'players'); // cached 6hrs
			$scores = $this->get_team($league_id, $my_team_id, $login_cookie, 'scores'); // cached 6hrs
			$league = $this->get_league($league_id, 'league'); // cached 0.5hrs; updated to 6hrs
			$assets = $this->get_league($league_id, 'assets'); // cached 0.5hrs; updated to 6hrs
			$rules = $this->get_league($league_id, 'rules'); // cached 0.5hrs; updated to 6hrs
			$picks = $this->get_asset_picks($assets, $my_team_id);
			$rules_array = $this->get_rules($league, $rules);
		
			// Check for errors
			$hasError = false;
			
			if (isset($league->Error)) {
				echo '<div id="league-json-response" style="display:none;">' . $league->Error . ' - ' . htmlspecialchars(json_encode($league->Response), ENT_QUOTES, 'UTF-8') . '</div>';
				$hasError = true;
			}
		
			if (isset($assets->Error)) {
				echo '<div id="league-json-response" style="display:none;">' . $assets->Error . ' - ' . htmlspecialchars(json_encode($assets->Response), ENT_QUOTES, 'UTF-8') . '</div>';
				$hasError = true;
			}
		
			if (isset($rules->Error)) {
				echo '<div id="league-json-response" style="display:none;">' . $rules->Error . ' - ' . htmlspecialchars(json_encode($rules->Response), ENT_QUOTES, 'UTF-8') . '</div>';
				$hasError = true;
			}
		
			if (isset($team->Error)) {
				echo '<div id="team-json-response" style="display:none;">' . $team->Error . ' - ' . htmlspecialchars(json_encode($team->Response), ENT_QUOTES, 'UTF-8') . '</div>';
				$hasError = true;
			}
		
			if (isset($scores->Error)) {
				echo '<div id="scores-json-response" style="display:none;">' . $scores->Error . ' - ' . htmlspecialchars(json_encode($scores->Response), ENT_QUOTES, 'UTF-8') . '</div>';
				$hasError = true;
			}
		
			// If there are no errors, stop the loop
			if (!$hasError) {
				$shouldRetrieveData = false;
			} else {
				// Wait for 2 seconds before retrying
				sleep(2);
			}
		}
		
		// If $maxRuns is reached and there are still errors, you can handle it here
		if ($hasError && $attempt == $maxRuns) {
			echo '<div id="error-response">Failed to retrieve data from the MFL Service. Please try again later.</div>';
			return;
		}

		?>
		<script type="text/javascript">
			jQuery(document).ready(function($) {
				function mfl_sort_table(compare) {
					var table = $(compare).parents('table').eq(0);
					var rows = table.find('tr:gt(0)').toArray().sort(comparer($(compare).index()));
					compare.asc = !compare.asc;
					if (!compare.asc) {
						rows = rows.reverse();
					}
					for (var i = 0; i < rows.length; i++) {
						table.append(rows[i]);
					}
				}

				function comparer(index) {
					return function(a, b) {
						var valA = getCellValue(a, index), valB = getCellValue(b, index);
						// console.log(valA);
						return $.isNumeric(valA) && $.isNumeric(valB) ? valA - valB : valA.toString().localeCompare(valB);
					};
				}

				function getCellValue(row, index) {
					return $(row).children('td').eq(index).text();
				}

				// mfl_sort_table($('.mfl-trade-table th:nth-child(3)'));

				$('.mfl-trade-table th').click(function() {
					mfl_sort_table(this);
				});
			});
		</script>
		
		<?php
		$plugin_base_url = plugin_dir_url(__DIR__);
		echo '
		<table class="mfl-trade-table">
			<thead>
				<tr>
					<th style="min-width:24px; padding-top:2px;">
						<img style="width:24px" src="' . $plugin_base_url . 'asetts/images/league-import-trade.svg" title="TRADE">
					</th>
					<th style="min-width:24px; padding-top:2px; text-align:left">
						<img style="width:24px" src="' . $plugin_base_url . 'asetts/images/league-import-player.svg" title="PLAYER">
					</th>
					<th style="min-width:24px; padding-top:2px;">
						<img style="width:24px" src="' . $plugin_base_url . 'asetts/images/league-import-position.svg" title="POSITION">
					</th>
					<th style="min-width:24px; padding-top:2px;">
						<img style="width:24px" src="' . $plugin_base_url . 'asetts/images/league-import-value.svg" title="DTC VALUE">
					</th>
				</tr>
			</thead>
			<tbody>';

		$positionOrder = [
			'QB' => 1,
			'RB' => 2,
			'WR' => 3,
			'TE' => 4,
			'DE' => 5,
			'DT' => 6,
			'LB' => 7,
			'CB' => 8,
			'S'  => 9,
		];

		$player_list = [];

		if (isset($team->rosters->franchise->player) && !empty($team->rosters->franchise->player)) {
			foreach ($team->rosters->franchise->player as $team_member) {
				$player = $this->get_player($team_member->id);
				if ($player) {
					$player_list[] = $player;
				}
			}

			usort($player_list, function ($a, $b) use ($positionOrder) {
				$posA = $positionOrder[$a['position']] ?? PHP_INT_MAX;
				$posB = $positionOrder[$b['position']] ?? PHP_INT_MAX;
				return $posA <=> $posB;
			});

			foreach ($player_list as $player) {
				echo '
				<tr>
					<td style="text-align:center">
						<input type="checkbox" data-id="player" name="trade-' . sanitize_text_field( $_POST['side'] ) . '[]" class="mfl-trade-object" data-side="' . sanitize_text_field( $_POST['side'] ) . '" value="' . $player['id'] . '" data-nonppr="' . $player['nonppr'] . '" data-type="' . $player['type'] . '">
					</td>
					<td class="mfl-trade-table-player-team" style="text-align:left; padding-left:5px; padding-right:5px;">
						<span class="mfl-trade-table-player">' . stripslashes($player['name']) . '</span> 
						<span class="mfl-trade-table-team">' . $player['team'] . '</span>
					</td>
					<td style="text-align:center; padding-left:3px; padding-right:3px;">';

				if (in_array($player['position'], ['QB', 'WR', 'RB'])) {
					echo '<span style="display:none">Z</span>';
				} elseif ($player['position'] == 'TE') {
					echo '<span style="display:none">ZZ</span>';
				}

				echo $player['position'] . '</td>
					<td style="text-align:center">' . dtc_get_player_total($player['mfl_id'], $rules_array, true) . '</td>
				</tr>';
			}
		}

		if ($picks) {
			$player = ! empty($player) ? $player : [];

			foreach ($picks as $pick) {
				// Remove the || $pick['round'] > 3 if rounds 4 or later have independent values in the DTC DB
				// $pick_info = $pick['pick'] == 0 || $pick['round'] > 3
				$pick_info = $pick['pick'] == 0 || $pick['round'] > 2
					? $pick['year'] . ' ' . $this->ordinal($pick['round']) . ' (Mid)' 
					: $pick['year'] . ' ' . $pick['round'] . '.' . $pick['pick'];
				
				$pick_data = $this->get_pick($pick_info);
				$pick_info_description = $pick['description'];
				$pick_data = empty( $pick_data ) ? [] : $pick_data;
				
				$pick_data['nonppr'] = ! empty( $pick_data['nonppr'] ) ? $pick_data['nonppr'] : '';
				$player['mfl_id'] = ! empty( $player['mfl_id'] ) ? $player['mfl_id'] : '';
				
				$pick_data['ten'] =  ! empty( $pick_data['ten'] ) ? $pick_data['ten'] : '';
				$pick_data['twelve'] = ! empty( $pick_data['twelve'] ) ? $pick_data['twelve'] : '';
				$pick_data['fourteen'] = ! empty( $pick_data['fourteen'] ) ? $pick_data['fourteen'] : '';
				$pick_data['sixteen'] = ! empty( $pick_data['sixteen'] ) ? $pick_data['sixteen'] : '';

				$pick_data['tensf'] = ! empty( $pick_data['tensf'] ) ? $pick_data['tensf'] : '';
				$pick_data['twelvesf'] = ! empty( $pick_data['twelvesf'] ) ? $pick_data['twelvesf'] : '';
				$pick_data['fourteensf'] = ! empty( $pick_data['fourteensf'] ) ? $pick_data['fourteensf'] : '';
				$pick_data['sixteensf'] = ! empty( $pick_data['sixteensf'] ) ? $pick_data['sixteensf'] : '';
				$pick_data['id'] = ! empty( $pick_data['id'] ) ? $pick_data['id'] : '';

				echo '
				<tr>
					<td style="text-align:center">
						<input type="checkbox" data-id="pick" class="mfl-trade-object" data-side="' . sanitize_text_field( $_POST['side'] ) . '" data-type="" name="trade-' . sanitize_text_field( $_POST['side'] ) . '" data-year="' . $pick['year'] . '" data-round="' . $pick['round'] . '" data-pick="' . $pick['pick'] . '" data-nonppr="' . $pick_data['nonppr'] . '" data-ten="' . $pick_data['ten'] . '" data-twelve="' . $pick_data['twelve'] . '" data-fourteen="' . $pick_data['fourteen'] . '" data-sixteen="' . $pick_data['sixteen'] . '" data-tensf="' . $pick_data['tensf'] . '" data-twelvesf="' . $pick_data['twelvesf'] . '" data-fourteensf="' . $pick_data['fourteensf'] . '" data-sixteensf="' . $pick_data['sixteensf'] . '" value="' . $pick_data['id'] . '">
					</td>
					<td style="text-align:left; padding-left:5px; padding-right:5px;">
						' . $pick_info_description . '
					</td>
					<td style="font-size:0px;">ZZZ</td>
					<td style="text-align:center">' . dtc_get_player_total($player['mfl_id'], $rules_array, true, 'draft_picks', $pick_info) . '</td>
				</tr>';
			}
		}

		echo '</tbody></table>';
		die();
	}

	function ordinal($num) {
		// Special case "teenth"
		if ( ($num / 10) % 10 != 1 )
		{
			// Handle 1st, 2nd, 3rd
			switch( $num % 10 )
			{
				case 1: return $num . 'st';
				case 2: return $num . 'nd';
				case 3: return $num . 'rd';  
			}
		}
		// Everything else is "nth"
		return $num . 'th';
	}

	function request() {
	
	}

	function login() {
		global $wpdb, $current_user;
		
		$login_cookie =  get_user_meta($current_user->ID,'_mfl_login_cookie',true);
		$show_form = false;
		
		if ($login_cookie == '') {
			$show_form = true;
		}
		
		if ($show_form == true) {
		
		return '
		<div class="dtc-mfl-nav mfl-login">
			<div class="mfl-login-block">
				<form action="" method="post" class="mfl-import-login">
					<table width="100%">
						<thead>
							<tr>
								<td colspan="2"><h3>Login Here</h3></td>
							</tr>
						</thead>
						
						<tr>
							<td style="text-align:right">Username</td>
							<td><input type="text" class="mfl-username" placeholder="Username"></td>
						</tr>

						<tr>
							<td style="text-align:right">Password</td>
							<td><input type="password" class="mfl-password" placeholder="Password"></td>
						</tr>

						<tr>
							<td class="footer" colspan="2"><input type="submit" name="mfl-login" value="Login"></td>
						</tr>
					</table>		
				</form>
			</div>

			<div class="mfl-login-block">
				<table width="100%">
					<thead>
						<tr>
							<td><h3>Lost your password?</h3></td>
						</tr>
					</thead>
					
					<tr>
						<td class="footer"><a href="' . $this->endpoint . 'login" target="_blank">Reset Your Password</a>
						</td>
					</tr>
				</table>
			</div>

			<div class="mfl-login-block">
				<table width="100%">
					<thead>
						<tr>
							<td><h3>Don\'t have an account?</h3></td>
						</tr>
					</thead>
					
					<tr>
						<td class="footer"><a href="' . $this->endpoint . 'public?O=04&URL=http%3A%2F%2Fhome.myfantasyleague.com" target="_blank">Create an account</a>
						</td>
					</tr>
				</table>
			</div>
		</div>
		';			
		} else {
			return false;	
		}
		
	}

	function xml_attribute($object, $attribute) {
		if(isset($object[$attribute]))
			return (string) $object[$attribute];
	}

	function request_cookie($username, $password) {
		global $current_user;

		$url = $this->endpoint . 'login?USERNAME=' . urlencode($username) . '&PASSWORD=' . urlencode($password) . '&XML=1';
		$request = wp_remote_get($url, array());
		
		if (is_wp_error($request)) {
			echo '<div class="dtc-error"><p>There was an error with your login to MFL</p></div>';
			return;
		}

		$response_body = wp_remote_retrieve_body($request);
		$json = simplexml_load_string($response_body);

		if ($json[0] == 'Invalid Password') {
			echo '<div class="dtc-error"><p>Invalid MFL Username or Password</p></div>';
			echo $this->login();
		} else {
			$atts = $json->attributes();
			
			if ($atts && isset($atts['MFL_USER_ID'])) {
				update_user_meta($current_user->ID, '_mfl_login_cookie', (string)$atts['MFL_USER_ID']);
				$login_cookie = get_user_meta($current_user->ID, '_mfl_login_cookie', true);
				echo $this->ajax_main_loader();
			} else {
				echo '<div class="dtc-error"><p>Unable to login to MFL</p></div>';
				echo '<div id="login-json-response" style="display:none;">' . htmlspecialchars(json_encode($json), ENT_QUOTES, 'UTF-8') . '</div>';
			}
		}
	}

	function get_team($league_id, $franchise_id, $login_cookie, $type = 'players') {
		global $current_user;
		
		$team_transient_key = '_mfl_user_' . $current_user->ID . '_league_' . $league_id . '_team_' . $franchise_id;
		$scores_transient_key = '_mfl_user_' . $current_user->ID . '_league_' . $league_id . '_team_' . $franchise_id . '_scores';
		
		$team = get_transient($team_transient_key);
		$scores = get_transient($scores_transient_key);

		$args = array( 
			'cookies' => array('MFL_USER_ID' => $login_cookie), 
			'timeout' => 30, 
			'sslverify' => false,
			'user-agent' => $this->mfl_user_agent,
		);

		if ($team === false) {
			// Fetch team roster
			$request = wp_remote_get($this->endpoint . 'export?TYPE=rosters&L=' . $league_id . '&FRANCHISE=' . $franchise_id . '&APIKEY=&JSON=1', $args);
			$body = is_array($request) ? json_decode($request['body']) : $request;

			$errorResponse = $this->checkResponseForErrors('Players', $request, $body, 'rosters');

			if ($errorResponse !== null) {
				return $errorResponse;
			}

			set_transient($team_transient_key, $body, 6 * HOUR_IN_SECONDS);
			$team = get_transient($team_transient_key);
		}

		if ( $type === 'scores' && $scores === false ) {
			// Collect player IDs
			$player_stats = array();
			if (isset($team->rosters->franchise->player) && !empty($team->rosters->franchise->player)) {
				foreach ($team->rosters->franchise->player as $team_member) {
					$player_stats[] = $team_member->id;
				}		
			}

			// Only fetch scores if we have players
			if (!empty($player_stats)) {
				// Fetch player scores
				$url = $this->endpoint . 'export?TYPE=playerScores&L=' . $league_id . '&W=&YEAR=' . $this->current_year . '&PLAYERS=' . implode(",", $player_stats) . '&APIKEY=&JSON=1';
				$request = wp_remote_get($url, $args);
				$body = is_array($request) ? json_decode($request['body']) : $request;

				$errorResponse = $this->checkResponseForErrors('Scores', $request, $body, 'playerScores');

				if ($errorResponse !== null) {
					return $errorResponse;
				}

				set_transient($scores_transient_key, $body->playerScores, 6 * HOUR_IN_SECONDS);
				$scores = get_transient($scores_transient_key);
			}
		}

		if ($type == 'players') {
			return $team;
		}
		
		if ($type == 'scores') {
			return $scores;
		}
	}


	function get_draft($league_id) {
		global $current_user;
		
		$user_id = $current_user->ID;
		$login_cookie =  get_user_meta($user_id,'_mfl_login_cookie',true);
			
		$request = wp_remote_get($this->endpoint.'export?TYPE=draftResults&L='.$league_id.'&APIKEY=&JSON=1',array('cookies'=>array('MFL_USER_ID'=>$login_cookie)));
		$body = json_decode($request['body']);
		
		if ($body) {
			return $body;
		}
		
		return array();
	}

	function get_league($league_id,$type = 'league',$force_refresh= false) {
		global $current_user;
		
		$user_id = $current_user->ID;
		$login_cookie =  get_user_meta($user_id,'_mfl_login_cookie',true);
		
		$league= get_transient('_mfl_user_'.$user_id.'_leauge_'.$league_id.'');
		$assets= get_transient('_mfl_user_'.$user_id.'_leauge_assets_'.$league_id.'');
		$rules= get_transient('_mfl_user_'.$user_id.'_leauge_rules_'.$league_id.'');
		
		if ($force_refresh == true) {
			$league = false;	
		}
		
		$args = array( 
			'cookies' => array('MFL_USER_ID' => $login_cookie), 
			'timeout' => 30, 
			'sslverify' => false,
			'user-agent' => $this->mfl_user_agent,
		);

		if ($league == false) {
			$request = wp_remote_get($this->endpoint.'export?TYPE=league&L='.$league_id.'&APIKEY=&JSON=1', $args);
			$body = is_array($request) ? json_decode($request['body']) : $request;	
		
			$errorResponse = $this->checkResponseForErrors('League', $request, $body, 'league');
			
			if ($errorResponse !== null) {
				return $errorResponse;
			}

			/*
			if( is_array( $request['body'] ) && !isset($request['body'])){
				return json_decode($request);
			}
			*/
		
			set_transient( '_mfl_user_'.$user_id.'_leauge_'.$league_id.'', $body->league, 6 * HOUR_IN_SECONDS );
			$league = get_transient('_mfl_user_'.$user_id.'_leauge_'.$league_id.'');
		}
		?>

		<script type="text/javascript">
		/*
		Duplicate
			jQuery(document).ready(function($) {
				window.rotoGPT_LeagueToken = "<?php echo $login_cookie; ?>";
				window.rotoGPT_LeagueIntegrationName = "mfl";
				window.rotoGPT_LeagueUserId = "<?php echo $user_id; ?>";
				window.rotoGPT_LeagueId = "<?php echo $league_id; ?>";
				window.rotoGPT_LeagueName = "<?php echo $league->name; ?>";
			});
		*/
		</script>
		
		<?php
		# set_transient('_selected_integration_league_user_'.$user_id, $league->name, 24 * HOUR_IN_SECONDS );
		# set_transient('_selected_integration_league_id_user_'.$user_id, $league_id, 24 * HOUR_IN_SECONDS );
		# set_transient('_selected_integration_user_id_'.$user_id, $user_id, 24 * HOUR_IN_SECONDS );
		# set_transient('_selected_integration_token_user_'.$user_id, $login_cookie, 24 * HOUR_IN_SECONDS );
		?>
		
		<script type="text/javascript">
			localStorage.setItem('dtc_integration_name', 'mfl');
			localStorage.setItem('dtc_league_name', '<?php echo $league->name; ?>');
			localStorage.setItem('dtc_league_id', '<?php echo $league_id; ?>');
			localStorage.setItem('dtc_integration_token', '<?php echo $login_cookie; ?>');

			// Populating this so we don't have conflicting uses of rotoGPT and DTC
			window.IntegrationName = "mfl";
			window.IntegrationLeagueName = "<?php echo $league->name; ?>";
			window.IntegrationLeagueId = "<?php echo $league_id; ?>";

			var event = new Event('IntegrationLeagueNameChanged');
			window.dispatchEvent(event);

			// jQuery(document).ready(function($) {
				window.rotoGPT_LeagueToken = "<?php echo $login_cookie; ?>";
				window.rotoGPT_LeagueIntegrationName = "mfl";
				window.rotoGPT_LeagueUserId = "<?php echo $user_id; ?>";
				window.rotoGPT_LeagueId = "<?php echo $league_id; ?>";
				window.rotoGPT_LeagueName = "<?php echo $league->name; ?>";
				window.rotoGPT_UserEmail = undefined;
			//});
		</script>
		
		<?php
		if ($assets == false) {
			$request = wp_remote_get($this->endpoint.'export?TYPE=assets&L='.$league_id.'&APIKEY=&JSON=1', $args);
			$body = is_array($request) ? json_decode($request['body']) : $request;
			set_transient( '_mfl_user_'.$user_id.'_leauge_assets_'.$league_id.'', $body->assets, 0.5 * HOUR_IN_SECONDS );
			$assets = get_transient('_mfl_user_'.$user_id.'_leauge_assets_'.$league_id.'');
		}
		
		if ($rules == false) {
			$request = wp_remote_get($this->endpoint.'export?TYPE=rules&L='.$league_id.'&APIKEY=&JSON=1', $args);
			$body = is_array($request) ? json_decode($request['body']) : $request;
			set_transient( '_mfl_user_'.$user_id.'_leauge_rules_'.$league_id.'', $body->rules, 6 * HOUR_IN_SECONDS );
			$rules = get_transient('_mfl_user_'.$user_id.'_leauge_rules_'.$league_id.'');
		}
			
		if ($type == 'league') {
			return $league;	
		}
		
		if ($type == 'assets') {
			return $assets;	
		}
		
		if ($type == 'rules') {
			return $rules;	
		}
		
	}

	function get_leagues($user_id) {
		$leauges = get_transient('_mfl_user_'.$user_id.'_leauges');
		$login_cookie =  get_user_meta($user_id,'_mfl_login_cookie',true);
		
		$leagues= false;
		if ($leauges == false) {
			$request = wp_remote_get($this->endpoint.'export?TYPE=myleagues&FRANCHISE_NAMES=1&APIKEY=&JSON=1',array('cookies'=>array('MFL_USER_ID'=>$login_cookie)));		
			$body = is_array($request) ? json_decode($request['body']) : $request;

			$errorResponse = $this->checkResponseForErrors('League', $request, $body, 'leagues');

			if ($errorResponse !== null) {
				return $errorResponse;
			}

			set_transient( '_mfl_user_'.$user_id.'_leauges', $body->leagues, .5 * HOUR_IN_SECONDS );
			$leauges = get_transient('_mfl_user_'.$user_id.'_leauges');
		}
		
		return $leauges;
	}

	function urlToLeaugeID($url) {
		$explode = explode("/",$url);
		return end($explode);
	}

	function team_dropdown($league,$league_id,$name, $my_team_id) {
		$league->name = $league->name ?? '';
		$html = '<select name="'.$name.'" class="'.$name.'" data-id="'.$league_id.'" data-name="'.$league->name.'"><option value="">Your Competitor</option>';
		
		if ( ! empty( $league->franchises->franchise )  ) {
			foreach($league->franchises->franchise as $l){
				// We don't want our team displayed as an opponent.
				if ($l->id !== $my_team_id) {
					$l->owner_name = $l->owner_name ?? '';
					$html .= '<option value="'.$l->id.'" data-my-team-name="'.$l->name.'" >'.$l->name.' - '.$l->owner_name.'</option>';
				}
			}
		}
		
		$html .='</select>';
		
		return $html;
	}

	function league_dropdown($league,$name,$selected='') {
		$html = '<select name="'.$name.'" class="'.$name.' league_dropdown"><option value="">Your Team</option>';
		
		foreach($league as $l) {
			$league_id = $this->urlToLeaugeID($l->url);

			# $stored_league_id = get_transient('_selected_integration_league_id_user_'.$user_id);

			if ($selected == $league_id) {
				$selected_dp = 'selected="selected"';
			#} elseif ($stored_league_id == $league_id) {
			#	$selected_dp = 'selected="selected"';	
			} else {
				$selected_dp = '';
			}
			
			$l->franchise_name = $l->franchise_name ?? '';
			$html .= '<option value="'.$league_id.'" data-my-team-id="'.$l->franchise_id.'" data-my-team-name="'.$l->franchise_name.'" '.$selected_dp.'>'.$l->name.' - '.$l->franchise_name.'</option>';
		}
		
		$html .='</select>';

		$html .= '
			<script>
			jQuery(document).ready(function(){
				var league_id = localStorage.getItem("dtc_league_id");
				if (league_id) {
					jQuery("select[name=\''.$name.'\'] option[value=\'" + league_id + "\']").prop("selected", true);
				}
			});
			</script>
		';
		
		return $html;
	}

	function checkResponseForErrors($data_type, $request, $response_object, $attribute) {
		$response_code = wp_remote_retrieve_response_code($request);

		if (is_wp_error($request)) {
			$error_message = $request->get_error_message();
			return (object) ['Error' => 'Request failed', 'Response' => 'Response Code-' . $response_code . ' --- ' . $error_message];
		}

		if (is_null($response_object)) {
			$response_body = wp_remote_retrieve_body($request);
			return (object) ['Error' => 'Cannot Decode JSON Response', 'Response' => 'Response Code-' . $response_code . ' --- ' . $response_body];
		}

		if (!isset($response_object->$attribute)) {
			// response_object throws fatal error when no team is selected 
			return (object) ['Error' => $data_type . ' data not found', 'Response' => 'Response Code-' . $response_code . ' --- ' . '$response_object'];
		}

		return null;
	}

	public function view() {
		global $current_user, $dtc_calculator;
		
		#ini_set('display_errors', 1);
		#ini_set('display_startup_errors', 1);
		#error_reporting(E_ALL);
		
		#echo dtc_get_player_total(1,array());
		?>
		<style type="text/css">
			.remodal{background-color: #000;margin-top:50px}	
			.dtc-error {
				background-color: white;
				color: red;
				padding: 15px;
				font-weight: bold;
				margin: 10px 0px;
				font-size: 18px;
				text-align: center;
			}	

			.league-import-item{min-height:500px;}
			.mfl-loader{background-color:#000000;color:#c3c6ca;min-height:500px;}
			.mfl-loader select{width:100%;}
			.mfl-loader a{color:#c3c6ca;}
			.dtc-mfl-half table{border-top:5px solid #ecb62e;margin-top:10px;width:100%;}
			.dtc-mfl-half thead th{background-color:#444444;text-align:center;font-weight:bold; cursor: pointer;}
			.dtc-mfl-half tbody tr:nth-child(even){background-color:#080809}
			.dtc-mfl-half tbody tr:nth-child(odd){background-color:#121212}
			.dtc-mfl-half{float:left;width:50%;}
			.dtc-mfl-half .dtc-half-inner{padding:10px;}
			.dtc-mfl-nav{padding:10px;text-align:right;}
			.dtc-refresh-main-loader{font-weight:bold}
			.dtc-nav-active{background-color:#ECB62E !important;color:#000 !important;  !important}
			.mfl-import-calc{padding:10px 0px;text-align:center;}
			.mfl-login {background-color:#FFF;min-height:500px;color:#2a3c5f;padding:15px;text-align: center;}
			.mfl-login a {color:red;}
			.mfl-login table{}
			.mfl-login table thead {
				border-bottom:5px solid #2a3c5f;
				background-image: url(/wp-content/plugins/dynasty-trade-calculator/asetts/images/bg-opacity-grey.png);
				box-shadow: inset 0 0 10px rgba(38,62,104,.4);
				box-shadow: inset 0 0 0.625rem rgba(38,62,104,.4);
			}
			.mfl-login .mfl-login-block{max-width:320px;margin:0px auto;border-radius:5px;border:1px solid #2a3c5f;margin-bottom:15px;margin-top:15px}
			.mfl-login table input[type=text], .mfl-login table input[type=password] {
				padding: 8px;
				background-color: #e9f0fd !important;
				color: #000 !important;
				border: 1px solid #c3c3c3;
				font-size: 14px;
				border-radius: 5px;
				line-height: 15px;
				height: auto;
			}
			.mfl-login table input[type=submit] {cursor: pointer;}
			.mfl-login table td{padding:4px;border-radius:5px;}	
			.mfl-login thead h3 {
				font-size:17px;  
				padding-left: 2.813rem;
				background: url(/wp-content/plugins/dynasty-trade-calculator/asetts/images/mfl-cap.png)no-repeat;
				background-size: contain;
				background-position: 2px 0;
				background-position: .125rem 0;
				color:#263e68;
				margin-top:8px;
				margin-bottom:8px;
				text-transform: uppercase;
				text-align:left;
			}
			.mfl-login table .footer{background-color:#e9ebef;text-align:center;}
			.mfl-login table .footer input{    background: #cd2122;color:#FFF;padding:10px;padding:3px;border-radius:5px;border:none }	
			.mfl-team-gutter{min-height: 300px;}	
			a.mfl-button{
				padding:5px;  
				margin: 5px auto;
				border:none;
				border-radius: 5px;
				background-color: #dddddd;
				color: #1a1a1a;
				font-weight:bold;
			}
			.mfl-import-calc-button {
				display: block;
				max-width: 200px;
				margin: 0px auto;
				border-radius: 5px;
				background-color: #ECB62E;
				color: #1a1a1a !important;
				text-align: center;
				padding: 5px;
				font-size: 16px;
				font-weight: bold;
			}
			.dtc-mfl-overlay-wrap{}
			.dtc-mfl-overlay{padding:10px;background-color:#FFF;margin:50px auto;width:100px;text-align:center;; border-radius: 50%;}
			.dtc-mfl-overlay h3{color:#000;}
			.league-import-button{margin-top:20px;}
			.league-import-button a {float:left;padding:5px;;margin:5px; border-radius:5px;background-color:#EFEFEF;color:#000 !important;}
			.league-import-button a img{width:100%}
			@media screen and (max-width: 600px) {
				.dtc-mfl-half{font-size:.9em}
				.mfl-loader{font-size:.8em}
			}
		</style>
			
		<script type="text/javascript">
			//mfl stuff
			function add_dtc_mfl_create_overlay(div){	
				jQuery(div).html('<div class="dtc-mfl-overlay"><div class="dtc-mfl-overlay-wrap"><img src="<?php echo DTC_URL; ?>/asetts/images/loading.gif"></div></div>');
			}
		
			function dtc_mfl_load_team(league_id,franchise_id,team_name,location,side){
				add_dtc_mfl_create_overlay(location);
				jQuery.post(dtc.ajaxurl, {'action':'dtc_ajax_get_league_players','side':side, 'league_id':league_id,'my_team_id':franchise_id,'my_team_name':team_name}, function(response) {					
					jQuery(location).html(response);
					//jQuery(location).prepend('<h3>' + team_name + '</h3>');
				});
			}

			function dtc_refresh_main_loader(){
				// add_dtc_mfl_create_overlay(".mfl-loader");
				let integration_name = localStorage.getItem('dtc_integration_name');
				// console.log(Cookies.get('import_loader') );
				// var importer_type = Cookies.get('import_loader');
				if(integration_name == '' || integration_name == null){
					var slug = 'mfl';	
					integration_name = 'mfl';
				} else if (integration_name == 'sleeper') {
					var slug = 'sleeper_api';
				} else {
					var slug = integration_name; // Cookies.get('import_loader');	
				}

				if (integration_name !== null) {
					// console.log('integration ' + slug);
					jQuery(".dtc-refresh-main-loader").removeClass('dtc-nav-active');
					jQuery("."+slug+"-loader-button").addClass('dtc-nav-active');
				
					jQuery(".league-import-item").hide();
					jQuery.post(dtc.ajaxurl, {'action':'dtc_ajax_'+slug+'_main_loader'}, function(response) {					
						jQuery("."+slug+"-loader").html(response);
						jQuery("."+slug+"-loader").show();
					});
				}
				
			}

			var loadingIntegration = false;
			
			jQuery(document).ready(function($) {
				// Triggering a refresh when the calculator breaks league integration
				window.addEventListener('dtcRefreshMainLoader', dtc_refresh_main_loader);

				dtc_refresh_main_loader();
			
				$( '.mfl-loader-nav').on("click", ".dtc-refresh-main-loader", function(e) {
					e.preventDefault();

					var slug = $(this).attr('data-id');

					// Block if already loading
					if (loadingIntegration) {
						console.log(`Loader for ${slug} is already in progress.`);
						return;
					}

					loadingIntegration = true;

					console.log(Cookies.get('import_loader'));

					var league_integration = slug;
					if (slug == 'sleeper_api') {
						league_integration = 'sleeper';
					}

					window.IntegrationName = league_integration;
					window.rotoGPT_LeagueIntegrationName = league_integration;
					window.rotoGPT_LeagueUserId = undefined;
					window.rotoGPT_LeagueToken = undefined;
					window.rotoGPT_LeagueId = undefined;
					window.rotoGPT_LeagueName = undefined;
					window.rotoGPT_UserEmail = undefined;

					$(".league-import-item").hide();
					$(".mfl-import-team-two").empty();
					$(".mfl-import-team-one").empty();	
					$(".sleeper_api-import-team-one").empty();
					$(".sleeper_api-import-team-two").empty();
					$(".fleaflicker-import-team-one").empty();
					$(".fleaflicker-import-team-two").empty();
					$(".yahoo-import-team-one").empty();
					$(".yahoo-import-team-two").empty();
					$(".ffpc-import-team-one").empty();
					$(".ffpc-import-team-two").empty();
					$(".fantrax-import-team-one").empty();
					$(".fantrax-import-team-two").empty();

					$(".mfl_load_opposing_team").val("");

					jQuery.post(dtc.ajaxurl, { 'action': 'dtc_ajax_' + slug + '_main_loader' }, function(response) {
						jQuery("." + slug + "-loader").html(response).show();

						loadingIntegration = false;
					});

					$("." + slug).show();
					$(".dtc-refresh-main-loader").removeClass('dtc-nav-active');
					$("." + slug + "-loader-button").addClass('dtc-nav-active');
					Cookies.set('import_loader', slug, { expires: 7, path: '/' });
				});

					function getPlayerInputCount(picker) {
						return picker.find('.dtc-player-input').length;
					}
				
					function addPlayerInput(picker) {
						var newPlayerInput = $('<div class="dtc-player-input"><a href="#" class="dtc-add-player dtc-add-another">Add Player / Pick</a></div>');
						newPlayerInput.insertBefore(picker.find('.dtc-player-input-total'));
					}
				
					function equalizePlayerInputs() {
						var leftPicker = $('.dtc-player-picker-left');
						var rightPicker = $('.dtc-player-picker-right');
						
						var leftCount = getPlayerInputCount(leftPicker);
						var rightCount = getPlayerInputCount(rightPicker);
						
						while (leftCount < rightCount) {
							addPlayerInput(leftPicker);
							leftCount++;
						}
						
						while (rightCount < leftCount) {
							addPlayerInput(rightPicker);
							rightCount++;
						}
					}

					$( document ).on( "click", ".mfl-import-calc-button", function() {
						var count_players_left = 0;
						var count_picks_left = 0;
						var count_players_right = 0;
						var count_picks_right = 0;
						var import_type = $(this).attr('data-id');
						dtc_ga_track('Import '+  import_type,'Import to calculator');
						var calc_template_left = '<div class="dtc-player-input mfl-last-entry-left"><a href="#" class="dtc-add-player dtc-add-another">Add Player / Pick </a></div>';
						var calc_template_right = '<div class="dtc-player-input mfl-last-entry-right"><a href="#" class="dtc-add-player dtc-add-another">Add Player / Pick </a></div>';
						$(".dtc-player-picker-left .dtc-player-input").remove(); 
						$(".dtc-player-picker-right .dtc-player-input").remove();
							
						$(".dtc-player-picker-left .dtc-player-input-total").before(calc_template_left);
						$(".dtc-player-picker-right .dtc-player-input-total").before(calc_template_right);

						var promises = [];

						$( ".mfl-trade-object:checkbox:checked" ).each(function( index ) {
							//insert into calc
							var side = $(this).attr('data-side');
							var type = $(this).attr("data-id");
							var type_player = $(this).attr("data-type");
							// console.log(type_player );
							var value = $(this).val();
							
							var team_size = $(".mfl-rule-team-size").val();
							var team_type = $(".mfl-rule-team-type").val();
							
							var parent = $(this).parent();
							
							/*
							$.post(dtc.ajaxurl, {"action":"dtc_ajax_get_player_input","id": value, "type": type,"team_size":team_size,"team_type":team_type,"type_player":type_player},
								function(response) {
									$(".mfl-last-entry-"+side).before('<div class="dtc-player-input"><div class="dtc-player-select2">'+response+'</div></div>');
									// console.log(".dtc-player-picker-"+side);
								
									$('.cycle-slideshow').cycle();
								
									if($(".dtc-showbadges").attr('data-enabled') == 1){
										$(".dtc-badge-logo").hide();
									}else{
										$(".dtc-badge-logo").show();	
									}
								}) .always(function() {
									recalculatePlayers();
								});
								*/

							var request = $.post(dtc.ajaxurl, {
								"action": "dtc_ajax_get_player_input",
								"id": value,
								"type": type,
								"team_size": team_size,
								"team_type": team_type,
								"type_player": type_player
							}, function(response) {
								$(".mfl-last-entry-" + side).before('<div class="dtc-player-input"><div class="dtc-player-select2">' + response + '</div></div>');
								// console.log(".dtc-player-picker-" + side);

								$('.cycle-slideshow').cycle();

								if ($(".dtc-showbadges").attr('data-enabled') == 1) {
									$(".dtc-badge-logo").hide();
								} else {
									$(".dtc-badge-logo").show();
								}
							}).always(function() {
								recalculatePlayers();
							});

							promises.push(request);
								
							if($(this).attr('data-side') == 'left'){
								if(type == 'player'){
									count_players_left +=1;
								}else{
									count_picks_left +=1;
								}
							} else {
								if(type == 'player'){
									count_players_right +=1;
								}else{
									count_picks_right +=1;
								}
							}
						}).promise().done( function() {
							$( ".dtc-leauge-size" ).each(function( index ) {
								// console.log($(this).attr('data-id'));
								if($(this).attr('data-id') == $('.mfl-rule-team-size').val()){
									// console.log('click this');
									// console.log(this);
									$(this).click();
								}
							});

							$.when.apply($, promises).done(function() {
								equalizePlayerInputs();     
							});
							
							$( ".dtc-leauge-format" ).each(function( index ) {
								if($(this).attr('data-id') == $('.mfl-rule-team-format').val()){
									$(this).click();
								}
							});
							
							$( ".dtc-leauge-type" ).each(function( index ) {
								if($(this).attr('data-id') == $('.mfl-rule-team-type').val()){
									$(this).click();
								}
							});
							
							$( ".dtc-leauge-format-alt" ).each(function( index ) {
								if($(this).hasClass('dtc-te-premium-actions') && $('.mfl-rule-team-tepre').val() >= 1 && $(this).attr('data-enabled') !=1){
									$(this).click();	
								}
						
								if($(this).hasClass('dtc-te-premium-actions') && $('.mfl-rule-team-tepre').val() ==0 && $(this).attr('data-enabled') == 1){
									$(this).click();	
								}
						
								if($(this).hasClass('dtc-rb-ppc-premium-actions') && $('.mfl-rule-team-rbppc').val() >= 1 && $(this).attr('data-enabled') !=1){
									$(this).click();	
								}	
						
								if($(this).hasClass('dtc-rb-ppc-premium-actions') && $('.mfl-rule-team-rbppc').val() ==0 && $(this).attr('data-enabled') == 1){
									$(this).click();	
								}
						
							});
						
							if($(".dtc-devy-actions").attr("data-enabled") == 1){
								$(".dtc-devy-actions").click();
							}
							
							var inst = $('[data-remodal-id=dtc-mfl-modal]').remodal();
								inst.close();
							});
						
						return false;
						
					});

					$( document ).on( "click", ".mfl-logout", function() {	
						let sbutmitButton = $(this);
						let currentText = sbutmitButton.text();
						sbutmitButton.text('Logging Out...');
						sbutmitButton.attr('disabled', true);

						$.post(dtc.ajaxurl, {'action':'dtc_mfl_ajax_logout', 'user_id':$(this).attr('data-id')}, function(response) {
							// Making sure to clear out the values on logout
							localStorage.removeItem('dtc_league_name');
							localStorage.removeItem('dtc_league_id');

							// Ensure we stay on the MFL tab after logout
							localStorage.setItem('dtc_integration_name', 'mfl');

							window.IntegrationLeagueName = undefined;
							window.IntegrationLeagueId = undefined;

							window.rotoGPT_LeagueToken = undefined;
							window.rotoGPT_LeagueId = undefined;
							window.rotoGPT_LeagueName = undefined;
							window.rotoGPT_UserEmail = undefined;

							var event = new Event('IntegrationLeagueNameChanged');
							window.dispatchEvent(event);

							dtc_refresh_main_loader();
							dtc_ga_track('<?php echo $this->name; ?>','User Logout');
							sbutmitButton.text(currentText);
							sbutmitButton.attr('disabled', false);
						});
						
						return false;		
					});

					$( document ).on( "click", ".mfl-refresh-data", function() {
							$.post(dtc.ajaxurl, {'action':'dtc_ajax_refresh_data', 'user_id':$(this).attr('data-id')}, function(response) {					
								dtc_refresh_main_loader();
								alert("Refreshed League Data");
								dtc_ga_track('<?php echo $this->name; ?>','Refresh Data');
							});
						
							return false;
					});
				
					$( document ).on( "submit", ".mfl-import-login", function() {
						$(".dtc-error").remove();
						let sbutmitButton = $(this).find('input[type="submit"]');
						let currentText = sbutmitButton.val();
						sbutmitButton.val('Logging In...');
						sbutmitButton.attr('disabled', true);
						
						$.post(dtc.ajaxurl, {'action':'dtc_ajax_mfl_login', 'mfl-username':$(".mfl-username").val(),'mfl-password':$(".mfl-password").val()}, function(response) {					
							$(".mfl-loader").html(response);
							dtc_ga_track('<?php echo $this->name; ?>','User Login');
							sbutmitButton.val(currentText);
							sbutmitButton.attr('disabled', false);
						});
				
						return false;
					});
				
					$( document ).on( "change", ".mfl_load_opposing_team", function() {
						dtc_mfl_load_team(jQuery(this).attr('data-id'),jQuery(this).val(),jQuery('option:selected', this).attr('data-my-team-name'),".mfl-import-team-two","right");	
						dtc_ga_track('<?php echo $this->name; ?>','Load Opposing Team');
					});
			
					$( document ).on( "change", ".mfl_league_id", function() {
						$(".mfl-import-team-two").empty();
						$(".mfl-import-team-one").empty();	
						$(".mfl_load_opposing_team").val("");
					    
						$.post(dtc.ajaxurl, {'action':'dtc_ajax_<?php echo $this->slug; ?>_get_league_settings', 'league_id':$(this).val()}, function(response) {					
							$(".<?php echo $this->slug; ?>-league-settings").html(response);
							dtc_ga_track('<?php echo $this->name; ?>','League Settings');					
						});

						dtc_mfl_load_team(jQuery(this).val(),jQuery('option:selected', this).attr('data-my-team-id'),jQuery('option:selected', this).attr('data-my-team-name'),".mfl-import-team-one","left");
					
						$.post(dtc.ajaxurl, {'action':'dtc_ajax_get_opposing_players_dropdown', 'league_id':$(this).val(),'my_team_id':$('option:selected', this).attr('data-my-team-id'),'my_team_name':$('option:selected', this).attr('data-my-team-name')}, function(response) {					
							
						$(".mf-opposing-teams").html(response);
						dtc_ga_track('<?php echo $this->name; ?>','Load League');
						//
					});
				});

				// Select the element you want to observe
				const targetNode = document.querySelector('[data-remodal-id="dtc-mfl-modal"]');

				// Create a new instance of MutationObserver
				const observer = new MutationObserver((mutationsList, observer) => {
					// Loop through each mutation
					for(const mutation of mutationsList) {
						// Check if the class name changed
						if(mutation.type === 'attributes' && mutation.attributeName === 'class') {
						// Get the current class names
						const classList = mutation.target.classList;
						
							// Check if the class 'remodal-is-closed' was removed and 'remodal-is-opened' was added
							if(!classList.contains('remodal-is-closed') && classList.contains('remodal-is-opened')) {
								var event = new Event('LeagueModelDisplayed');
								window.dispatchEvent(event);
							}
						}
					}
				});

				observer.observe(targetNode, { attributes: true });
			});
			//mfl stuff  
		</script>
		<?php
	}
	
}