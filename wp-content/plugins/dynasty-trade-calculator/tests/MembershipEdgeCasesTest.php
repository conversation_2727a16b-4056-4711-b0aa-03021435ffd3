<?php

use PHPUnit\Framework\TestCase;
use DynastyTradeCalculator\Membership;

/**
 * Test class for edge cases and boundary conditions in membership functionality
 * Tests invalid data, error scenarios, and unusual conditions
 */
class MembershipEdgeCasesTest extends TestCase
{
    /**
     * Test invalid membership levels and subscription types
     */
    public function testInvalidMembershipLevelsAndSubscriptionTypes()
    {
        $invalidMembershipLevels = [
            null,
            0,
            -1,
            999,
            'string',
            [],
            false
        ];
        
        foreach ($invalidMembershipLevels as $invalidLevel) {
            $subscriptionType = $this->getMockSubscriptionTypeForLevel($invalidLevel);
            $this->assertNull($subscriptionType, 
                "Invalid membership level should return null subscription type: " . var_export($invalidLevel, true));
        }
        
        // Test invalid subscription types
        $invalidSubscriptionTypes = [
            '',
            null,
            'invalid_type',
            'UPPERCASE',
            'with spaces',
            'special@chars',
            123,
            [],
            false
        ];
        
        foreach ($invalidSubscriptionTypes as $invalidType) {
            $isValid = $this->mockIsValidSubscriptionType($invalidType);
            $this->assertFalse($isValid, 
                "Invalid subscription type should be rejected: " . var_export($invalidType, true));
        }
    }

    /**
     * Test date boundary conditions and formatting edge cases
     */
    public function testDateBoundaryConditionsAndFormatting()
    {
        $edgeCaseDates = [
            // Leap year dates
            ['date' => '2024-02-29', 'description' => 'leap year date'],
            ['date' => '2023-02-28', 'description' => 'non-leap year last day of February'],
            
            // Year boundaries
            ['date' => '2023-12-31 23:59:59', 'description' => 'end of year'],
            ['date' => '2024-01-01 00:00:00', 'description' => 'start of year'],
            
            // Month boundaries
            ['date' => '2024-01-31', 'description' => 'end of January'],
            ['date' => '2024-02-01', 'description' => 'start of February'],
            
            // Invalid dates
            ['date' => '2024-02-30', 'description' => 'invalid February date'],
            ['date' => '2024-13-01', 'description' => 'invalid month'],
            ['date' => '2024-01-32', 'description' => 'invalid day'],
            
            // Edge case formats
            ['date' => '0000-00-00 00:00:00', 'description' => 'zero date'],
            ['date' => '', 'description' => 'empty date'],
            ['date' => null, 'description' => 'null date']
        ];
        
        foreach ($edgeCaseDates as $testCase) {
            $isValidDate = $this->mockIsValidDate($testCase['date']);
            
            if (strpos($testCase['description'], 'invalid') !== false || 
                strpos($testCase['description'], 'zero') !== false ||
                strpos($testCase['description'], 'empty') !== false ||
                strpos($testCase['description'], 'null') !== false) {
                $this->assertFalse($isValidDate, 
                    "Should reject {$testCase['description']}: {$testCase['date']}");
            } else {
                $this->assertTrue($isValidDate, 
                    "Should accept {$testCase['description']}: {$testCase['date']}");
            }
        }
    }

    /**
     * Test special characters in customer data
     */
    public function testSpecialCharactersInCustomerData()
    {
        $specialCharacterTests = [
            // Unicode characters
            ['notes' => 'Customer notes with émojis 🎉 and ñoñó', 'should_handle' => true],
            ['notes' => 'Notes with Chinese characters: 你好世界', 'should_handle' => true],
            ['notes' => 'Notes with Arabic: مرحبا بالعالم', 'should_handle' => true],
            
            // HTML/XML characters
            ['notes' => 'Notes with <script>alert("xss")</script>', 'should_handle' => true],
            ['notes' => 'Notes with &lt;tag&gt; entities', 'should_handle' => true],
            ['notes' => 'Notes with "quotes" and \'apostrophes\'', 'should_handle' => true],
            
            // SQL injection attempts
            ['notes' => 'Notes with \'; DROP TABLE users; --', 'should_handle' => true],
            ['notes' => 'Notes with \' OR 1=1 --', 'should_handle' => true],
            
            // Very long strings
            ['notes' => str_repeat('A', 10000), 'should_handle' => true],
            ['notes' => str_repeat('🎉', 1000), 'should_handle' => true],
            
            // Control characters
            ['notes' => "Notes with\nnewlines\tand\ttabs", 'should_handle' => true],
            ['notes' => "Notes with\r\nWindows line endings", 'should_handle' => true]
        ];
        
        foreach ($specialCharacterTests as $test) {
            $mockCustomer = $this->createMockCustomer(12345);
            $mockCustomer->method('get_notes')->willReturn($test['notes']);
            
            // Test that the system can handle the special characters
            $notes = $mockCustomer->get_notes();
            $this->assertEquals($test['notes'], $notes, 'Should preserve special characters in notes');
            
            // Test pilot invitation detection with special characters
            $hasPilotInvitation = $this->mockHasPilotInvitationInNotes($notes);
            $this->assertIsBool($hasPilotInvitation, 'Should return boolean for pilot invitation check');
        }
    }

    /**
     * Test JSON encoding/decoding edge cases
     */
    public function testJsonEncodingDecodingEdgeCases()
    {
        $edgeCaseData = [
            // Circular references (simulated)
            ['data' => ['self' => 'reference'], 'description' => 'self reference'],
            
            // Very deep nesting
            ['data' => $this->createDeeplyNestedArray(100), 'description' => 'deeply nested array'],
            
            // Large numbers
            ['data' => ['big_number' => PHP_INT_MAX], 'description' => 'maximum integer'],
            ['data' => ['float' => 1.7976931348623157E+308], 'description' => 'large float'],
            
            // Special float values
            ['data' => ['infinity' => INF], 'description' => 'infinity'],
            ['data' => ['nan' => NAN], 'description' => 'not a number'],
            
            // Unicode and special characters
            ['data' => ['unicode' => '🎉🌟💫'], 'description' => 'unicode emojis'],
            ['data' => ['special' => "\x00\x01\x02"], 'description' => 'control characters'],
            
            // Empty and null values
            ['data' => ['empty_string' => ''], 'description' => 'empty string'],
            ['data' => ['null_value' => null], 'description' => 'null value'],
            ['data' => ['empty_array' => []], 'description' => 'empty array'],
            ['data' => ['empty_object' => new \stdClass()], 'description' => 'empty object']
        ];
        
        foreach ($edgeCaseData as $test) {
            $jsonString = json_encode($test['data']);
            
            if ($test['description'] === 'infinity' || $test['description'] === 'not a number') {
                // These should fail JSON encoding
                $this->assertFalse($jsonString, "Should fail to encode {$test['description']}");
            } else {
                $this->assertNotFalse($jsonString, "Should successfully encode {$test['description']}");
                
                if ($jsonString !== false) {
                    $decodedData = json_decode($jsonString, true);
                    $this->assertNotNull($decodedData, "Should successfully decode {$test['description']}");
                }
            }
        }
    }

    /**
     * Test concurrent membership scenarios
     */
    public function testConcurrentMembershipScenarios()
    {
        // Test customer with multiple active memberships (edge case)
        $mockCustomer = $this->createMockCustomer(12345);
        
        $membership1 = $this->createMockMembership(1001, 7, 'active');
        $membership2 = $this->createMockMembership(1002, 9, 'active');
        $membership3 = $this->createMockMembership(1003, 12, 'cancelled');
        
        $allMemberships = [$membership1, $membership2, $membership3];
        $activeMemberships = [$membership1, $membership2];
        
        $mockCustomer->method('get_memberships')->willReturnCallback(function($args = []) use ($allMemberships, $activeMemberships) {
            if (isset($args['status']) && in_array('active', (array)$args['status'])) {
                return $activeMemberships;
            }
            return $allMemberships;
        });
        
        // Test handling multiple active memberships
        $activeMembers = $mockCustomer->get_memberships(['status' => 'active']);
        $this->assertCount(2, $activeMembers, 'Should handle multiple active memberships');
        
        // Test getting the "primary" membership (first active one)
        $primaryMembership = $activeMembers[0] ?? null;
        $this->assertNotNull($primaryMembership, 'Should have a primary membership');
        $this->assertEquals(7, $primaryMembership->get_object_id(), 'Primary membership should be the first one');
    }

    /**
     * Test membership upgrade/downgrade edge cases
     */
    public function testMembershipUpgradeDowngradeEdgeCases()
    {
        $edgeCaseTransitions = [
            // Same level "upgrade"
            ['from' => 7, 'to' => 7, 'is_change' => false],
            
            // Extreme level differences
            ['from' => 5, 'to' => 12, 'is_change' => true],
            ['from' => 12, 'to' => 5, 'is_change' => true],
            
            // Invalid levels
            ['from' => null, 'to' => 7, 'is_change' => false],
            ['from' => 7, 'to' => null, 'is_change' => false],
            ['from' => -1, 'to' => 7, 'is_change' => false],
            ['from' => 7, 'to' => 999, 'is_change' => false]
        ];
        
        foreach ($edgeCaseTransitions as $transition) {
            $isValidChange = $this->mockIsValidMembershipChange($transition['from'], $transition['to']);
            
            if ($transition['is_change']) {
                $this->assertTrue($isValidChange, 
                    "Should allow change from {$transition['from']} to {$transition['to']}");
            } else {
                $this->assertFalse($isValidChange, 
                    "Should not allow change from {$transition['from']} to {$transition['to']}");
            }
        }
    }

    /**
     * Test API timeout and retry scenarios
     */
    public function testApiTimeoutAndRetryScenarios()
    {
        $timeoutScenarios = [
            ['timeout' => 1, 'should_retry' => true],
            ['timeout' => 30, 'should_retry' => true],
            ['timeout' => 60, 'should_retry' => false], // too long
            ['timeout' => 0, 'should_retry' => false], // invalid
            ['timeout' => -1, 'should_retry' => false] // invalid
        ];
        
        foreach ($timeoutScenarios as $scenario) {
            $shouldRetry = $this->mockShouldRetryAfterTimeout($scenario['timeout']);
            
            if ($scenario['should_retry']) {
                $this->assertTrue($shouldRetry, 
                    "Should retry after {$scenario['timeout']} second timeout");
            } else {
                $this->assertFalse($shouldRetry, 
                    "Should not retry after {$scenario['timeout']} second timeout");
            }
        }
    }

    /**
     * Test memory and performance edge cases
     */
    public function testMemoryAndPerformanceEdgeCases()
    {
        // Test large customer notes
        $largeNotes = str_repeat('Large customer notes with lots of text. ', 1000);
        $mockCustomer = $this->createMockCustomer(12345);
        $mockCustomer->method('get_notes')->willReturn($largeNotes);
        
        $notes = $mockCustomer->get_notes();
        $this->assertGreaterThan(10000, strlen($notes), 'Should handle large notes');
        
        // Test many memberships
        $manyMemberships = [];
        for ($i = 1; $i <= 100; $i++) {
            $manyMemberships[] = $this->createMockMembership($i, 7, 'active');
        }
        
        $mockCustomer2 = $this->createMockCustomer(12345);
        $mockCustomer2->method('get_memberships')->willReturn($manyMemberships);
        
        $memberships = $mockCustomer2->get_memberships();
        $this->assertCount(100, $memberships, 'Should handle many memberships');
    }

    // Helper methods

    private function createMockCustomer($customerId)
    {
        $mockCustomer = $this->getMockBuilder(\stdClass::class)
            ->addMethods(['get_id', 'get_notes', 'get_memberships'])
            ->getMock();
        $mockCustomer->method('get_id')->willReturn($customerId);
        return $mockCustomer;
    }

    private function createMockMembership($membershipId, $membershipLevel, $status)
    {
        $mockMembership = $this->getMockBuilder(\stdClass::class)
            ->addMethods(['get_id', 'get_object_id', 'get_status', 'get_expiration_date', 'get_activated_date', 'get_customer', 'get_recurring_amount'])
            ->getMock();
        $mockMembership->method('get_id')->willReturn($membershipId);
        $mockMembership->method('get_object_id')->willReturn($membershipLevel);
        $mockMembership->method('get_status')->willReturn($status);
        return $mockMembership;
    }

    private function getMockSubscriptionTypeForLevel($level)
    {
        $validMappings = [8 => 'free', 7 => 'standard_50', 9 => 'standard_100', 12 => 'standard_200', 5 => 'admin'];
        return (is_int($level) || is_string($level)) ? ($validMappings[$level] ?? null) : null;
    }

    private function mockIsValidSubscriptionType($type)
    {
        $validTypes = ['free', 'standard_50', 'standard_100', 'standard_200', 'admin'];
        return is_string($type) && in_array($type, $validTypes);
    }

    private function mockIsValidDate($date)
    {
        if (empty($date) || $date === '0000-00-00 00:00:00') {
            return false;
        }

        // Check for obviously invalid dates
        if (strpos($date, '2024-02-30') !== false ||
            strpos($date, '2024-13-') !== false ||
            strpos($date, '-32') !== false) {
            return false;
        }

        return strtotime($date) !== false;
    }

    private function mockHasPilotInvitationInNotes($notes)
    {
        return is_string($notes) && strpos($notes, 'pilot invitation') !== false;
    }

    private function createDeeplyNestedArray($depth)
    {
        if ($depth <= 0) {
            return 'deep';
        }
        return ['nested' => $this->createDeeplyNestedArray($depth - 1)];
    }

    private function mockIsValidMembershipChange($fromLevel, $toLevel)
    {
        $validLevels = [5, 7, 8, 9, 12];
        return in_array($fromLevel, $validLevels) && 
               in_array($toLevel, $validLevels) && 
               $fromLevel !== $toLevel;
    }

    private function mockShouldRetryAfterTimeout($timeout)
    {
        return is_numeric($timeout) && $timeout > 0 && $timeout <= 30;
    }
}
