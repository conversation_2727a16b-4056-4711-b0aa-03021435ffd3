<?php

use PHPUnit\Framework\TestCase;
use DynastyTradeCalculator\Membership;

/**
 * Test class for core Membership functionality
 * Tests customer retrieval, membership status checks, and basic operations
 */
class MembershipTest extends TestCase
{
    /**
     * Test membership level to RotoGPT subscription mapping
     */
    public function testMembershipLevelToRotoGptMapping()
    {
        // Test the expected mappings based on PRD documentation
        $expectedMappings = [
            8 => 'free',
            7 => 'standard_50',
            9 => 'standard_100', 
            12 => 'standard_200',
            5 => 'admin'
        ];

        foreach ($expectedMappings as $membershipLevel => $expectedSubscription) {
            // Create a mock membership object
            $mockMembership = $this->createMockMembership($membershipLevel);
            $mockCustomer = $this->createMockCustomer();
            
            // Mock the membership's get_customer method
            $mockMembership->method('get_customer')->willReturn($mockCustomer);
            
            // Test the mapping
            $actualSubscription = $this->getMockRotoGptSubscription($membershipLevel);
            $this->assertEquals($expectedSubscription, $actualSubscription, 
                "Membership level {$membershipLevel} should map to {$expectedSubscription}");
        }
    }

    /**
     * Test pilot program invitation detection
     */
    public function testPilotProgramInvitationDetection()
    {
        // Test invitation via customer notes
        $mockCustomer = $this->createMockCustomer();
        $mockCustomer->method('get_notes')->willReturn('Some notes with ' . (defined('DTC_CHATDTC_PILOT_INVITATION_TEXT') ? DTC_CHATDTC_PILOT_INVITATION_TEXT : 'pilot invitation') . ' text');
        
        $isInvited = $this->mockIsCustomerInvitedToChatdtcPilot($mockCustomer);
        $this->assertTrue($isInvited, 'Customer should be invited via notes');

        // Test invitation via special pricing (2.99)
        $mockCustomer2 = $this->createMockCustomer();
        $mockCustomer2->method('get_notes')->willReturn('Regular notes without invitation');
        
        $mockMembership = $this->createMockMembership(7);
        $mockMembership->method('get_recurring_amount')->willReturn(2.99);
        $mockCustomer2->method('get_memberships')->willReturn([$mockMembership]);
        
        $isInvited2 = $this->mockIsCustomerInvitedToChatdtcPilot($mockCustomer2);
        $this->assertTrue($isInvited2, 'Customer should be invited via 2.99 pricing');

        // Test invitation via special pricing (29.99)
        $mockCustomer3 = $this->createMockCustomer();
        $mockCustomer3->method('get_notes')->willReturn('Regular notes without invitation');
        
        $mockMembership3 = $this->createMockMembership(7);
        $mockMembership3->method('get_recurring_amount')->willReturn(29.99);
        $mockCustomer3->method('get_memberships')->willReturn([$mockMembership3]);
        
        $isInvited3 = $this->mockIsCustomerInvitedToChatdtcPilot($mockCustomer3);
        $this->assertTrue($isInvited3, 'Customer should be invited via 29.99 pricing');

        // Test no invitation
        $mockCustomer4 = $this->createMockCustomer();
        $mockCustomer4->method('get_notes')->willReturn('Regular notes without invitation');
        
        $mockMembership4 = $this->createMockMembership(7);
        $mockMembership4->method('get_recurring_amount')->willReturn(49.99);
        $mockCustomer4->method('get_memberships')->willReturn([$mockMembership4]);
        
        $isInvited4 = $this->mockIsCustomerInvitedToChatdtcPilot($mockCustomer4);
        $this->assertFalse($isInvited4, 'Customer should not be invited with regular pricing');
    }

    /**
     * Test legacy membership options lost detection
     */
    public function testLegacyMembershipOptionsLostDetection()
    {
        // Test customer with legacy options lost
        $mockCustomer = $this->createMockCustomer();
        $mockCustomer->method('get_notes')->willReturn('Some notes with ' . (defined('DTC_LEGACY_MEMBERSHIP_LOST_NOTE') ? DTC_LEGACY_MEMBERSHIP_LOST_NOTE : 'legacy lost') . ' text');
        
        $isLegacyLost = $this->mockIsLegacyMembershipOptionsLost($mockCustomer);
        $this->assertTrue($isLegacyLost, 'Customer should have legacy options lost');

        // Test customer without legacy options lost
        $mockCustomer2 = $this->createMockCustomer();
        $mockCustomer2->method('get_notes')->willReturn('Regular notes without any special markers');
        
        $isLegacyLost2 = $this->mockIsLegacyMembershipOptionsLost($mockCustomer2);
        $this->assertFalse($isLegacyLost2, 'Customer should not have legacy options lost');
    }

    /**
     * Test membership status validation
     */
    public function testMembershipStatusValidation()
    {
        $validStatuses = ['active', 'cancelled', 'expired', 'pending'];
        
        foreach ($validStatuses as $status) {
            $mockMembership = $this->createMockMembership(7);
            $mockMembership->method('get_status')->willReturn($status);
            
            $this->assertContains($status, $validStatuses, "Status {$status} should be valid");
        }
    }

    /**
     * Test membership expiration date handling
     */
    public function testMembershipExpirationDateHandling()
    {
        // Test future expiration date
        $futureDate = date('Y-m-d H:i:s', strtotime('+30 days'));
        $mockMembership = $this->createMockMembership(7);
        $mockMembership->method('get_expiration_date')->willReturn($futureDate);
        $mockMembership->method('get_status')->willReturn('active');
        
        $this->assertGreaterThan(time(), strtotime($futureDate), 'Future expiration date should be valid');

        // Test past expiration date
        $pastDate = date('Y-m-d H:i:s', strtotime('-30 days'));
        $mockMembership2 = $this->createMockMembership(7);
        $mockMembership2->method('get_expiration_date')->willReturn($pastDate);
        $mockMembership2->method('get_status')->willReturn('expired');
        
        $this->assertLessThan(time(), strtotime($pastDate), 'Past expiration date should be expired');
    }

    /**
     * Test cancelled membership validation
     */
    public function testCancelledMembershipValidation()
    {
        // Test cancelled membership that's still valid
        $futureDate = date('Y-m-d H:i:s', strtotime('+30 days'));
        $mockMembership = $this->createMockMembership(7);
        $mockMembership->method('get_status')->willReturn('cancelled');
        $mockMembership->method('get_expiration_date')->willReturn($futureDate);
        
        $isValid = $this->mockIsCancelledMembershipValid($mockMembership);
        $this->assertTrue($isValid, 'Cancelled membership with future expiration should be valid');

        // Test cancelled membership that's expired
        $pastDate = date('Y-m-d H:i:s', strtotime('-30 days'));
        $mockMembership2 = $this->createMockMembership(7);
        $mockMembership2->method('get_status')->willReturn('cancelled');
        $mockMembership2->method('get_expiration_date')->willReturn($pastDate);
        
        $isValid2 = $this->mockIsCancelledMembershipValid($mockMembership2);
        $this->assertFalse($isValid2, 'Cancelled membership with past expiration should be invalid');
    }

    /**
     * Test membership at specific date functionality
     */
    public function testMembershipAtSpecificDate()
    {
        $testDate = '2024-06-15';
        $activatedDate = '2024-06-01';
        $expirationDate = '2024-06-30';
        
        $mockMembership = $this->createMockMembership(7);
        $mockMembership->method('get_activated_date')->willReturn($activatedDate);
        $mockMembership->method('get_expiration_date')->willReturn($expirationDate);
        
        // Test date within membership period
        $isValidAtDate = ($activatedDate <= $testDate && $expirationDate >= $testDate);
        $this->assertTrue($isValidAtDate, 'Membership should be valid at test date');
        
        // Test date before membership period
        $beforeDate = '2024-05-15';
        $isValidBeforeDate = ($activatedDate <= $beforeDate && $expirationDate >= $beforeDate);
        $this->assertFalse($isValidBeforeDate, 'Membership should not be valid before activation');
        
        // Test date after membership period
        $afterDate = '2024-07-15';
        $isValidAfterDate = ($activatedDate <= $afterDate && $expirationDate >= $afterDate);
        $this->assertFalse($isValidAfterDate, 'Membership should not be valid after expiration');
    }

    // Helper methods for creating mock objects and testing logic

    /**
     * Create a mock membership object
     */
    private function createMockMembership($membershipLevel)
    {
        $mockMembership = $this->getMockBuilder(\stdClass::class)
            ->addMethods(['get_object_id', 'get_id', 'get_status', 'get_expiration_date', 'get_activated_date', 'get_customer', 'get_recurring_amount'])
            ->getMock();
        $mockMembership->method('get_object_id')->willReturn($membershipLevel);
        $mockMembership->method('get_id')->willReturn(rand(1000, 9999));

        return $mockMembership;
    }

    /**
     * Create a mock customer object
     */
    private function createMockCustomer()
    {
        $mockCustomer = $this->getMockBuilder(\stdClass::class)
            ->addMethods(['get_id', 'get_notes', 'get_memberships'])
            ->getMock();
        $mockCustomer->method('get_id')->willReturn(rand(100, 999));

        return $mockCustomer;
    }

    /**
     * Mock the RotoGPT subscription mapping logic
     */
    private function getMockRotoGptSubscription($membershipLevel)
    {
        $mappings = [
            8 => 'free',
            7 => 'standard_50',
            9 => 'standard_100',
            12 => 'standard_200',
            5 => 'admin'
        ];
        
        return $mappings[$membershipLevel] ?? null;
    }

    /**
     * Mock the pilot invitation detection logic
     */
    private function mockIsCustomerInvitedToChatdtcPilot($customer)
    {
        $notes = $customer->get_notes();
        $invitationText = defined('DTC_CHATDTC_PILOT_INVITATION_TEXT') ? DTC_CHATDTC_PILOT_INVITATION_TEXT : 'pilot invitation';
        $isInvited = strpos($notes, $invitationText) !== false;
        
        if (!$isInvited) {
            $memberships = $customer->get_memberships(['status' => 'active']) ?? [];
            $membership = $memberships[0] ?? null;
            if ($membership && ($membership->get_recurring_amount() == 2.99 || $membership->get_recurring_amount() == 29.99)) {
                $isInvited = true;
            }
        }
        
        return $isInvited;
    }

    /**
     * Mock the legacy membership options lost detection logic
     */
    private function mockIsLegacyMembershipOptionsLost($customer)
    {
        $notes = $customer->get_notes();
        $legacyLostText = defined('DTC_LEGACY_MEMBERSHIP_LOST_NOTE') ? DTC_LEGACY_MEMBERSHIP_LOST_NOTE : 'legacy lost';
        
        return strpos($notes, $legacyLostText) !== false;
    }

    /**
     * Mock the cancelled membership validation logic
     */
    private function mockIsCancelledMembershipValid($membership)
    {
        if ($membership->get_status() === 'cancelled') {
            $expirationDate = strtotime($membership->get_expiration_date());
            return $expirationDate >= time();
        }
        
        return true;
    }
}
