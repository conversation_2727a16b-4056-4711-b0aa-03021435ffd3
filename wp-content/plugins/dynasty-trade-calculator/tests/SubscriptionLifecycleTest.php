<?php

use PHPUnit\Framework\TestCase;
use DynastyTradeCalculator\Membership;

/**
 * Comprehensive test class for subscription lifecycle acceptance criteria
 * Tests the complete subscription workflows from signup through upgrades and downgrades
 * Covers all tier transitions and RotoGPT integration scenarios
 */
class SubscriptionLifecycleTest extends TestCase
{
    /**
     * Test new account signup at $2.99 tier (membership level 10) 
     * Should trigger free membership on RotoGPT side with no pending subscription
     */
    public function testNewAccountSignupAt299TierFreeMembership()
    {
        // Test data for $2.99 tier signup
        $signupData = [
            'user_id' => '12345',
            'tier_price' => 2.99,
            'membership_level_id' => 10,
            'expected_rotogpt_subscription' => 'free',
            'expected_pending_subscription' => null
        ];

        // Verify membership level mapping
        $this->assertEquals(10, $signupData['membership_level_id'], 
            '$2.99 tier should map to membership level 10');

        // Verify RotoGPT subscription type mapping
        $this->assertEquals('free', $signupData['expected_rotogpt_subscription'],
            'Membership level 10 should map to free RotoGPT subscription');

        // Test RotoGPT create subscription request for free tier
        $createRequest = [
            'user_id' => $signupData['user_id'],
            'client_id' => 'DTC',
            'subscription_type' => $signupData['expected_rotogpt_subscription']
        ];

        $this->assertIsArray($createRequest);
        $this->assertEquals('free', $createRequest['subscription_type']);
        $this->assertEquals($signupData['user_id'], $createRequest['user_id']);

        // Test expected success response
        $expectedResponse = [
            'status' => 'success',
            'subscription_id' => 'sub_free_' . uniqid(),
            'subscription_type' => 'free',
            'user_id' => $signupData['user_id'],
            'membership_level' => 10,
            'pending_subscription_type' => null,
            'message' => 'Free subscription created successfully'
        ];

        $this->assertEquals('success', $expectedResponse['status']);
        $this->assertEquals('free', $expectedResponse['subscription_type']);
        $this->assertNull($expectedResponse['pending_subscription_type'], 
            'New free membership should have no pending subscription');

        // Test WordPress membership state
        $wpMembershipState = [
            'membership_level_id' => 10,
            'status' => 'active',
            'recurring_amount' => 2.99,
            // TODO: Verify expiration date is set correctly
            // 'expiration_date' => date('Y-m-d H:i:s', strtotime('+1 month')),
            'pending_level_id' => null
        ];

        $this->assertEquals(10, $wpMembershipState['membership_level_id']);
        $this->assertEquals('active', $wpMembershipState['status']);
        $this->assertEquals(2.99, $wpMembershipState['recurring_amount']);
        $this->assertNull($wpMembershipState['pending_level_id'], 
            'No pending membership level should be set for new signup');
    }

    /**
     * Test upgrade from $2.99 tier to $4.99 tier (membership level 7)
     * Should convert to Standard 50 plan with no pending subscription
     */
    public function testUpgradeFrom299To499Tier()
    {
        // Initial state: $2.99 tier (level 10, free subscription)
        $initialState = [
            'user_id' => '12345',
            'current_membership_level' => 10,
            'current_rotogpt_subscription' => 'free',
            'current_tier_price' => 2.99,
            'current_expiration' => date('Y-m-d H:i:s', strtotime('+20 days'))
        ];

        // Upgrade to: $4.99 tier (level 7, standard_50 subscription)
        $upgradeData = [
            'new_membership_level' => 7,
            'new_tier_price' => 4.99,
            'new_rotogpt_subscription' => 'standard_50',
            'is_upgrade' => true,
            'apply_immediately' => true
        ];

        // Verify this is an upgrade
        $this->assertTrue($upgradeData['is_upgrade'], 
            'Moving from level 10 to level 7 should be considered an upgrade');

        // Test RotoGPT update subscription request
        $updateRequest = [
            'user_id' => $initialState['user_id'],
            'client_id' => 'DTC',
            'old_subscription_type' => $initialState['current_rotogpt_subscription'],
            'new_subscription_type' => $upgradeData['new_rotogpt_subscription'],
            'apply_immediately' => $upgradeData['apply_immediately']
        ];

        $this->assertEquals('free', $updateRequest['old_subscription_type']);
        $this->assertEquals('standard_50', $updateRequest['new_subscription_type']);
        $this->assertTrue($updateRequest['apply_immediately'], 
            'Upgrades should be applied immediately');

        // Test expected success response
        $expectedResponse = [
            'status' => 'success',
            'old_subscription_id' => 'sub_free_123',
            'new_subscription_id' => 'sub_standard50_' . uniqid(),
            'subscription_type' => 'standard_50',
            'user_id' => $initialState['user_id'],
            'effective_date' => date('Y-m-d'),
            'pending_subscription_type' => null
        ];

        $this->assertEquals('success', $expectedResponse['status']);
        $this->assertEquals('standard_50', $expectedResponse['subscription_type']);
        $this->assertEquals(date('Y-m-d'), $expectedResponse['effective_date']);
        $this->assertNull($expectedResponse['pending_subscription_type'], 
            'Upgrades should have no pending subscription type');

        // Test WordPress membership state after upgrade
        $wpMembershipStateAfterUpgrade = [
            'membership_level_id' => 7,
            'status' => 'active',
            'recurring_amount' => 4.99,
            // 'expiration_date' => date('Y-m-d H:i:s', strtotime('+1 month')),
            'pending_level_id' => null,
            'upgraded_from' => 10
        ];

        $this->assertEquals(7, $wpMembershipStateAfterUpgrade['membership_level_id']);
        $this->assertEquals('active', $wpMembershipStateAfterUpgrade['status']);
        $this->assertEquals(4.99, $wpMembershipStateAfterUpgrade['recurring_amount']);
        $this->assertNull($wpMembershipStateAfterUpgrade['pending_level_id']);
        $this->assertEquals(10, $wpMembershipStateAfterUpgrade['upgraded_from']);
    }

    /**
     * Test upgrade from $4.99 tier to $6.99 tier (subscription id 9)
     * Should update to Standard 100 plan with no pending subscription
     */
    public function testUpgradeFrom499To699Tier()
    {
        // Initial state: $4.99 tier (level 7, standard_50 subscription)
        $initialState = [
            'user_id' => '12345',
            'current_membership_level' => 7,
            'current_rotogpt_subscription' => 'standard_50',
            'current_tier_price' => 4.99,
            'current_expiration' => date('Y-m-d H:i:s', strtotime('+15 days'))
        ];

        // Upgrade to: $6.99 tier (level 9, standard_100 subscription)
        $upgradeData = [
            'new_membership_level' => 9,
            'new_tier_price' => 6.99,
            'new_rotogpt_subscription' => 'standard_100',
            'is_upgrade' => true,
            'apply_immediately' => true
        ];

        // Verify this is an upgrade (standard_50 to standard_100)
        $subscriptionHierarchy = ['free', 'standard_50', 'standard_100', 'standard_200', 'admin'];
        $oldIndex = array_search($initialState['current_rotogpt_subscription'], $subscriptionHierarchy);
        $newIndex = array_search($upgradeData['new_rotogpt_subscription'], $subscriptionHierarchy);
        
        $this->assertGreaterThan($oldIndex, $newIndex, 
            'standard_100 should be higher than standard_50 in hierarchy');
        $this->assertTrue($upgradeData['is_upgrade']);

        // Test RotoGPT update subscription request
        $updateRequest = [
            'user_id' => $initialState['user_id'],
            'client_id' => 'DTC',
            'old_subscription_type' => $initialState['current_rotogpt_subscription'],
            'new_subscription_type' => $upgradeData['new_rotogpt_subscription'],
            'apply_immediately' => $upgradeData['apply_immediately']
        ];

        $this->assertEquals('standard_50', $updateRequest['old_subscription_type']);
        $this->assertEquals('standard_100', $updateRequest['new_subscription_type']);
        $this->assertTrue($updateRequest['apply_immediately']);

        // Test expected success response
        $expectedResponse = [
            'status' => 'success',
            'old_subscription_id' => 'sub_standard50_123',
            'new_subscription_id' => 'sub_standard100_' . uniqid(),
            'subscription_type' => 'standard_100',
            'user_id' => $initialState['user_id'],
            'effective_date' => date('Y-m-d'),
            'pending_subscription_type' => null
        ];

        $this->assertEquals('success', $expectedResponse['status']);
        $this->assertEquals('standard_100', $expectedResponse['subscription_type']);
        $this->assertNull($expectedResponse['pending_subscription_type']);

        // Test WordPress membership state after upgrade
        $wpMembershipStateAfterUpgrade = [
            'membership_level_id' => 9,
            'status' => 'active',
            'recurring_amount' => 6.99,
            'expiration_date' => date('Y-m-d H:i:s', strtotime('+1 month')),
            'pending_level_id' => null,
            'upgraded_from' => 7
        ];

        $this->assertEquals(9, $wpMembershipStateAfterUpgrade['membership_level_id']);
        $this->assertEquals('active', $wpMembershipStateAfterUpgrade['status']);
        $this->assertEquals(6.99, $wpMembershipStateAfterUpgrade['recurring_amount']);
        $this->assertNull($wpMembershipStateAfterUpgrade['pending_level_id']);
    }

    /**
     * Test upgrade from $6.99 tier to $9.99 tier
     * Should set membership to Standard 200 plan with no pending subscription
     */
    public function testUpgradeFrom699To999Tier()
    {
        // Initial state: $6.99 tier (level 9, standard_100 subscription)
        $initialState = [
            'user_id' => '12345',
            'current_membership_level' => 9,
            'current_rotogpt_subscription' => 'standard_100',
            'current_tier_price' => 6.99,
            'current_expiration' => date('Y-m-d H:i:s', strtotime('+10 days'))
        ];

        // Upgrade to: $9.99 tier (level 12, standard_200 subscription)
        $upgradeData = [
            'new_membership_level' => 12,
            'new_tier_price' => 9.99,
            'new_rotogpt_subscription' => 'standard_200',
            'is_upgrade' => true,
            'apply_immediately' => true
        ];

        // Verify this is an upgrade (standard_100 to standard_200)
        $subscriptionHierarchy = ['free', 'standard_50', 'standard_100', 'standard_200', 'admin'];
        $oldIndex = array_search($initialState['current_rotogpt_subscription'], $subscriptionHierarchy);
        $newIndex = array_search($upgradeData['new_rotogpt_subscription'], $subscriptionHierarchy);
        
        $this->assertGreaterThan($oldIndex, $newIndex, 
            'standard_200 should be higher than standard_100 in hierarchy');

        // Test RotoGPT update subscription request
        $updateRequest = [
            'user_id' => $initialState['user_id'],
            'client_id' => 'DTC',
            'old_subscription_type' => $initialState['current_rotogpt_subscription'],
            'new_subscription_type' => $upgradeData['new_rotogpt_subscription'],
            'apply_immediately' => $upgradeData['apply_immediately']
        ];

        $this->assertEquals('standard_100', $updateRequest['old_subscription_type']);
        $this->assertEquals('standard_200', $updateRequest['new_subscription_type']);
        $this->assertTrue($updateRequest['apply_immediately']);

        // Test expected success response
        $expectedResponse = [
            'status' => 'success',
            'old_subscription_id' => 'sub_standard100_123',
            'new_subscription_id' => 'sub_standard200_' . uniqid(),
            'subscription_type' => 'standard_200',
            'user_id' => $initialState['user_id'],
            'effective_date' => date('Y-m-d'),
            'pending_subscription_type' => null
        ];

        $this->assertEquals('success', $expectedResponse['status']);
        $this->assertEquals('standard_200', $expectedResponse['subscription_type']);
        $this->assertNull($expectedResponse['pending_subscription_type']);

        // Test WordPress membership state after upgrade
        $wpMembershipStateAfterUpgrade = [
            'membership_level_id' => 12,
            'status' => 'active',
            'recurring_amount' => 9.99,
            'expiration_date' => date('Y-m-d H:i:s', strtotime('+1 month')),
            'pending_level_id' => null,
            'upgraded_from' => 9
        ];

        $this->assertEquals(12, $wpMembershipStateAfterUpgrade['membership_level_id']);
        $this->assertEquals('active', $wpMembershipStateAfterUpgrade['status']);
        $this->assertEquals(9.99, $wpMembershipStateAfterUpgrade['recurring_amount']);
        $this->assertNull($wpMembershipStateAfterUpgrade['pending_level_id']);
    }

    /**
     * Test direct signup at $4.99 tier (Standard 50 plan)
     * Should correctly set Standard 50 plan with no pending subscription
     */
    public function testDirectSignupAt499Tier()
    {
        // Test data for direct $4.99 tier signup
        $signupData = [
            'user_id' => '54321',
            'tier_price' => 4.99,
            'membership_level_id' => 7,
            'expected_rotogpt_subscription' => 'standard_50',
            'expected_pending_subscription' => null
        ];

        // Verify membership level mapping
        $this->assertEquals(7, $signupData['membership_level_id'],
            '$4.99 tier should map to membership level 7');

        // Verify RotoGPT subscription type mapping
        $this->assertEquals('standard_50', $signupData['expected_rotogpt_subscription'],
            'Membership level 7 should map to standard_50 RotoGPT subscription');

        // Test RotoGPT create subscription request
        $createRequest = [
            'user_id' => $signupData['user_id'],
            'client_id' => 'DTC',
            'subscription_type' => $signupData['expected_rotogpt_subscription']
        ];

        $this->assertEquals('standard_50', $createRequest['subscription_type']);

        // Test expected success response
        $expectedResponse = [
            'status' => 'success',
            'subscription_id' => 'sub_standard50_' . uniqid(),
            'subscription_type' => 'standard_50',
            'user_id' => $signupData['user_id'],
            'pending_subscription_type' => null
        ];

        $this->assertEquals('success', $expectedResponse['status']);
        $this->assertEquals('standard_50', $expectedResponse['subscription_type']);
        $this->assertNull($expectedResponse['pending_subscription_type']);
    }

    /**
     * Test direct signup at $6.99 tier (Standard 100 plan)
     * Should correctly set Standard 100 plan with no pending subscription
     */
    public function testDirectSignupAt699Tier()
    {
        // Test data for direct $6.99 tier signup
        $signupData = [
            'user_id' => '65432',
            'tier_price' => 6.99,
            'membership_level_id' => 9,
            'expected_rotogpt_subscription' => 'standard_100',
            'expected_pending_subscription' => null
        ];

        // Verify membership level mapping
        $this->assertEquals(9, $signupData['membership_level_id'],
            '$6.99 tier should map to membership level 9');

        // Verify RotoGPT subscription type mapping
        $this->assertEquals('standard_100', $signupData['expected_rotogpt_subscription'],
            'Membership level 9 should map to standard_100 RotoGPT subscription');

        // Test RotoGPT create subscription request
        $createRequest = [
            'user_id' => $signupData['user_id'],
            'client_id' => 'DTC',
            'subscription_type' => $signupData['expected_rotogpt_subscription']
        ];

        $this->assertEquals('standard_100', $createRequest['subscription_type']);

        // Test expected success response
        $expectedResponse = [
            'status' => 'success',
            'subscription_id' => 'sub_standard100_' . uniqid(),
            'subscription_type' => 'standard_100',
            'user_id' => $signupData['user_id'],
            'pending_subscription_type' => null
        ];

        $this->assertEquals('success', $expectedResponse['status']);
        $this->assertEquals('standard_100', $expectedResponse['subscription_type']);
        $this->assertNull($expectedResponse['pending_subscription_type']);
    }

    /**
     * Test direct signup at $9.99 tier (Standard 200 plan)
     * Should correctly set Standard 200 plan with no pending subscription
     */
    public function testDirectSignupAt999Tier()
    {
        // Test data for direct $9.99 tier signup
        $signupData = [
            'user_id' => '76543',
            'tier_price' => 9.99,
            'membership_level_id' => 12,
            'expected_rotogpt_subscription' => 'standard_200',
            'expected_pending_subscription' => null
        ];

        // Verify membership level mapping
        $this->assertEquals(12, $signupData['membership_level_id'],
            '$9.99 tier should map to membership level 12');

        // Verify RotoGPT subscription type mapping
        $this->assertEquals('standard_200', $signupData['expected_rotogpt_subscription'],
            'Membership level 12 should map to standard_200 RotoGPT subscription');

        // Test RotoGPT create subscription request
        $createRequest = [
            'user_id' => $signupData['user_id'],
            'client_id' => 'DTC',
            'subscription_type' => $signupData['expected_rotogpt_subscription']
        ];

        $this->assertEquals('standard_200', $createRequest['subscription_type']);

        // Test expected success response
        $expectedResponse = [
            'status' => 'success',
            'subscription_id' => 'sub_standard200_' . uniqid(),
            'subscription_type' => 'standard_200',
            'user_id' => $signupData['user_id'],
            'pending_subscription_type' => null
        ];

        $this->assertEquals('success', $expectedResponse['status']);
        $this->assertEquals('standard_200', $expectedResponse['subscription_type']);
        $this->assertNull($expectedResponse['pending_subscription_type']);
    }

    /**
     * Test downgrade from $9.99 tier to $2.99 tier
     * CRITICAL: This test demonstrates the bug where membership should stay Standard 200
     * but have pending subscription type 'Free' scheduled for one month later
     */
    public function testDowngradeFrom999To299TierWithScheduling()
    {
        // Initial state: $9.99 tier (level 12, standard_200 subscription)
        $initialState = [
            'user_id' => '12345',
            'current_membership_level' => 12,
            'current_rotogpt_subscription' => 'standard_200',
            'current_tier_price' => 9.99,
            'current_expiration' => date('Y-m-d H:i:s', strtotime('+20 days')),
            'current_status' => 'active'
        ];

        // Downgrade to: $2.99 tier (level 10, free subscription)
        $downgradeData = [
            'new_membership_level' => 10,
            'new_tier_price' => 2.99,
            'new_rotogpt_subscription' => 'free',
            'is_upgrade' => false,
            'apply_immediately' => false,
            'scheduled_start_date' => $initialState['current_expiration']
        ];

        // Verify this is a downgrade
        $subscriptionHierarchy = ['free', 'standard_50', 'standard_100', 'standard_200', 'admin'];
        $oldIndex = array_search($initialState['current_rotogpt_subscription'], $subscriptionHierarchy);
        $newIndex = array_search($downgradeData['new_rotogpt_subscription'], $subscriptionHierarchy);

        $this->assertLessThan($oldIndex, $newIndex,
            'free should be lower than standard_200 in hierarchy');
        $this->assertFalse($downgradeData['is_upgrade']);

        // Test RotoGPT update subscription request (CORRECT BEHAVIOR)
        $rotoGptUpdateRequest = [
            'user_id' => $initialState['user_id'],
            'client_id' => 'DTC',
            'old_subscription_type' => $initialState['current_rotogpt_subscription'],
            'new_subscription_type' => $downgradeData['new_rotogpt_subscription'],
            'apply_immediately' => false,
            'new_subscription_start_date' => date('Y-m-d\TH:i:s\Z', strtotime($initialState['current_expiration']))
        ];

        $this->assertEquals('standard_200', $rotoGptUpdateRequest['old_subscription_type']);
        $this->assertEquals('free', $rotoGptUpdateRequest['new_subscription_type']);
        $this->assertFalse($rotoGptUpdateRequest['apply_immediately'],
            'Downgrades should NOT be applied immediately');
        $this->assertArrayHasKey('new_subscription_start_date', $rotoGptUpdateRequest);

        // Test expected RotoGPT success response (CORRECT BEHAVIOR)
        $expectedRotoGptResponse = [
            'status' => 'success',
            'old_subscription_id' => 'sub_standard200_123',
            'new_subscription_id' => 'sub_free_' . uniqid(),
            'current_subscription_type' => 'standard_200', // STAYS THE SAME
            'pending_subscription_type' => 'free', // SCHEDULED FOR LATER
            'effective_date' => date('Y-m-d', strtotime($initialState['current_expiration'])),
            'user_id' => $initialState['user_id']
        ];

        $this->assertEquals('success', $expectedRotoGptResponse['status']);
        $this->assertEquals('standard_200', $expectedRotoGptResponse['current_subscription_type'],
            'Current subscription should remain standard_200 until expiration');
        $this->assertEquals('free', $expectedRotoGptResponse['pending_subscription_type'],
            'Pending subscription should be set to free');
        $this->assertEquals(date('Y-m-d', strtotime($initialState['current_expiration'])),
            $expectedRotoGptResponse['effective_date']);

        // Test CORRECT WordPress membership state after downgrade
        $correctWpMembershipState = [
            'membership_level_id' => 12, // SHOULD STAY THE SAME
            'status' => 'active',
            'recurring_amount' => 9.99, // SHOULD STAY THE SAME
            'expiration_date' => $initialState['current_expiration'], // SHOULD STAY THE SAME
            'pending_level_id' => 10, // SHOULD BE SET TO NEW LEVEL
            'pending_subscription_type' => 'free',
            'pending_effective_date' => $initialState['current_expiration']
        ];

        $this->assertEquals(12, $correctWpMembershipState['membership_level_id'],
            'CRITICAL: Current membership level should stay 12 until expiration');
        $this->assertEquals('active', $correctWpMembershipState['status']);
        $this->assertEquals(9.99, $correctWpMembershipState['recurring_amount'],
            'CRITICAL: Current recurring amount should stay 9.99 until expiration');
        $this->assertEquals($initialState['current_expiration'],
            $correctWpMembershipState['expiration_date'],
            'CRITICAL: Expiration date should be preserved');
        $this->assertEquals(10, $correctWpMembershipState['pending_level_id'],
            'CRITICAL: Pending level should be set to 10');
        $this->assertEquals('free', $correctWpMembershipState['pending_subscription_type']);

        // Test INCORRECT WordPress behavior (current bug)
        $buggyWpMembershipState = [
            'membership_level_id' => 10, // BUG: Changes immediately
            'status' => 'active',
            'recurring_amount' => 2.99, // BUG: Changes immediately
            'expiration_date' => date('Y-m-d H:i:s', strtotime('+1 month')), // BUG: New expiration
            'pending_level_id' => null, // BUG: No pending level set
            'pending_subscription_type' => null // BUG: No pending subscription
        ];

        // These assertions demonstrate the current buggy behavior
        // TODO: Fix the bug so these assertions fail and the correct ones above pass
        $this->assertEquals(10, $buggyWpMembershipState['membership_level_id'],
            'BUG: Currently changes membership level immediately instead of scheduling');
        $this->assertEquals(2.99, $buggyWpMembershipState['recurring_amount'],
            'BUG: Currently changes recurring amount immediately');
        $this->assertNull($buggyWpMembershipState['pending_level_id'],
            'BUG: Currently does not set pending level');
    }

    /**
     * Test downgrade from $6.99 tier to $2.99 tier
     * Should preserve current Standard 100 membership and schedule Free subscription
     */
    public function testDowngradeFrom699To299Tier()
    {
        // Initial state: $6.99 tier (level 9, standard_100 subscription)
        $initialState = [
            'user_id' => '54321',
            'current_membership_level' => 9,
            'current_rotogpt_subscription' => 'standard_100',
            'current_tier_price' => 6.99,
            'current_expiration' => date('Y-m-d H:i:s', strtotime('+15 days')),
            'current_status' => 'active'
        ];

        // Downgrade to: $2.99 tier (level 10, free subscription)
        $downgradeData = [
            'new_membership_level' => 10,
            'new_tier_price' => 2.99,
            'new_rotogpt_subscription' => 'free',
            'is_upgrade' => false,
            'apply_immediately' => false
        ];

        // Test RotoGPT update subscription request
        $rotoGptUpdateRequest = [
            'user_id' => $initialState['user_id'],
            'client_id' => 'DTC',
            'old_subscription_type' => $initialState['current_rotogpt_subscription'],
            'new_subscription_type' => $downgradeData['new_rotogpt_subscription'],
            'apply_immediately' => false,
            'new_subscription_start_date' => date('Y-m-d\TH:i:s\Z', strtotime($initialState['current_expiration']))
        ];

        $this->assertEquals('standard_100', $rotoGptUpdateRequest['old_subscription_type']);
        $this->assertEquals('free', $rotoGptUpdateRequest['new_subscription_type']);
        $this->assertFalse($rotoGptUpdateRequest['apply_immediately']);

        // Test CORRECT WordPress membership state after downgrade fix
        $correctWpMembershipState = [
            'current_membership_level_id' => 9, // SHOULD STAY THE SAME
            'current_status' => 'active',
            'current_recurring_amount' => 6.99, // SHOULD STAY THE SAME
            'current_expiration_date' => $initialState['current_expiration'], // PRESERVED
            'pending_membership_level_id' => 10, // NEW PENDING MEMBERSHIP
            'pending_status' => 'pending',
            'pending_effective_date' => $initialState['current_expiration']
        ];

        $this->assertEquals(9, $correctWpMembershipState['current_membership_level_id']);
        $this->assertEquals('active', $correctWpMembershipState['current_status']);
        $this->assertEquals(6.99, $correctWpMembershipState['current_recurring_amount']);
        $this->assertEquals(10, $correctWpMembershipState['pending_membership_level_id']);
        $this->assertEquals('pending', $correctWpMembershipState['pending_status']);
    }

    /**
     * Test downgrade from $4.99 tier to free tier
     * Should preserve current Standard 50 membership and schedule Free subscription
     */
    public function testDowngradeFrom499ToFreeTier()
    {
        // Initial state: $4.99 tier (level 7, standard_50 subscription)
        $initialState = [
            'user_id' => '65432',
            'current_membership_level' => 7,
            'current_rotogpt_subscription' => 'standard_50',
            'current_tier_price' => 4.99,
            'current_expiration' => date('Y-m-d H:i:s', strtotime('+25 days')),
            'current_status' => 'active'
        ];

        // Downgrade to: Free tier (level 8, free subscription)
        $downgradeData = [
            'new_membership_level' => 8,
            'new_tier_price' => 0.00,
            'new_rotogpt_subscription' => 'free',
            'is_upgrade' => false,
            'apply_immediately' => false
        ];

        // Test RotoGPT update subscription request
        $rotoGptUpdateRequest = [
            'user_id' => $initialState['user_id'],
            'client_id' => 'DTC',
            'old_subscription_type' => $initialState['current_rotogpt_subscription'],
            'new_subscription_type' => $downgradeData['new_rotogpt_subscription'],
            'apply_immediately' => false,
            'new_subscription_start_date' => date('Y-m-d\TH:i:s\Z', strtotime($initialState['current_expiration']))
        ];

        $this->assertEquals('standard_50', $rotoGptUpdateRequest['old_subscription_type']);
        $this->assertEquals('free', $rotoGptUpdateRequest['new_subscription_type']);
        $this->assertFalse($rotoGptUpdateRequest['apply_immediately']);

        // Test expected RotoGPT success response
        $expectedRotoGptResponse = [
            'status' => 'success',
            'current_subscription_type' => 'standard_50', // STAYS THE SAME
            'pending_subscription_type' => 'free', // SCHEDULED FOR LATER
            'effective_date' => date('Y-m-d', strtotime($initialState['current_expiration'])),
            'user_id' => $initialState['user_id']
        ];

        $this->assertEquals('success', $expectedRotoGptResponse['status']);
        $this->assertEquals('standard_50', $expectedRotoGptResponse['current_subscription_type']);
        $this->assertEquals('free', $expectedRotoGptResponse['pending_subscription_type']);
    }

    /**
     * Test scheduled downgrade activation when membership expires
     * This tests the complete lifecycle of a downgrade from scheduling to activation
     */
    public function testScheduledDowngradeActivationOnExpiration()
    {
        // Initial setup: User has Standard 200 membership expiring today
        $expiringMembershipState = [
            'user_id' => '98765',
            'membership_level_id' => 12,
            'rotogpt_subscription' => 'standard_200',
            'status' => 'active',
            'expiration_date' => date('Y-m-d H:i:s', strtotime('-1 hour')), // Just expired
            'pending_downgrade_to_level' => 10, // Scheduled downgrade to free
            'pending_downgrade_subscription' => 'free'
        ];

        // Test membership expiration handling
        $expirationHandling = [
            'old_status' => 'active',
            'new_status' => 'expired',
            'should_activate_pending' => true
        ];

        $this->assertEquals('expired', $expirationHandling['new_status']);
        $this->assertTrue($expirationHandling['should_activate_pending']);

        // Test the activated downgrade membership state
        $activatedDowngradeMembership = [
            'membership_level_id' => 10,
            'rotogpt_subscription' => 'free',
            'status' => 'active',
            'expiration_date' => date('Y-m-d H:i:s', strtotime('+1 month')),
            'activated_from_scheduled_downgrade' => true
        ];

        $this->assertEquals(10, $activatedDowngradeMembership['membership_level_id']);
        $this->assertEquals('free', $activatedDowngradeMembership['rotogpt_subscription']);
        $this->assertEquals('active', $activatedDowngradeMembership['status']);
        $this->assertTrue($activatedDowngradeMembership['activated_from_scheduled_downgrade']);

        // Test that the old membership is properly expired
        $expiredMembershipState = [
            'membership_level_id' => 12,
            'status' => 'expired',
            'expiration_date' => $expiringMembershipState['expiration_date'],
            'downgrade_activated' => true
        ];

        $this->assertEquals(12, $expiredMembershipState['membership_level_id']);
        $this->assertEquals('expired', $expiredMembershipState['status']);
        $this->assertTrue($expiredMembershipState['downgrade_activated']);
    }
}
