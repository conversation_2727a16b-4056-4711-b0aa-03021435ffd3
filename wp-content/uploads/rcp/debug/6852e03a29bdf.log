2025-6-17 20:22:52 - Starting rcp_check_for_expired_users() cron job.
2025-6-17 20:22:52 - No expired memberships found.
2025-6-17 20:22:52 - Starting rcp_check_for_soon_to_expire_users() cron job.
2025-6-17 20:22:52 - Processing expiration reminder. ID: 0; Period: -1month; Levels: 1, 3, 2.
2025-6-17 20:22:52 - <PERSON>minder is not enabled - exiting.
2025-6-18 10:09:24 - RCP Activate License Key called with License Key: e755a40f7edda5161d0fb0e5b547e8ea
2025-6-18 10:09:27 - Starting rcp_check_member_counts() cron job.
2025-6-18 10:09:27 - Starting rcp_mark_abandoned_payments() cron job.
2025-6-18 10:50:18 - RCP upgraded from version 3.4.4 to 3.5.46.
2025-6-18 10:57:32 - Started new registration for membership level #10 via stripe.
2025-6-18 10:57:32 - Started new registration for membership level #10 via stripe.
2025-6-18 10:57:33 - Adding a new customer. Args: array (
  'date_registered' => '2025-06-18 10:57:33',
  'has_trialed' => 0,
  'user_id' => 25833,
)
2025-6-18 10:57:33 - Created new customer #25771.
2025-6-18 10:57:33 - Registration type: new.
2025-6-18 10:57:33 - Adding new membership. Data: array (
  'customer_id' => '25771',
  'user_id' => '25833',
  'object_id' => 10,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 2.99,
  'recurring_amount' => 2.99,
  'created_date' => '2025-06-18 10:57:33',
  'expiration_date' => '2025-07-18 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '214d0734cb7f6dcb55092c181f013c52',
)
2025-6-18 10:57:34 - New payment inserted. ID: 134064; User ID: 25833; Amount: 2.99; Subscription: Dynasty Trade Calculator (Monthly); Status: pending
2025-6-18 10:57:34 - Registration for user #25833 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34478
2025-6-18 10:57:35 - Updating membership #34478. New data: array (
  'gateway_customer_id' => 'cus_SWRFE0WbiwM1FT',
).
2025-6-18 10:57:36 - Updating payment #134064 with new data: array (
  'status' => 'failed',
)
2025-6-18 10:57:36 - Disabling membership #34478.
2025-6-18 10:57:36 - Updating membership #34478. New data: array (
  'disabled' => 1,
).
2025-6-18 10:57:36 - "Can cancel" status for membership #34478: false. Reason: membership not active.
2025-6-18 10:57:36 - Stripe registration failed for user #25833. Error message: Please make sure that the name field doesn’t contain a card number.
2025-6-18 10:57:59 - Using recovered payment #134064 for registration. Transaction type: new.
2025-6-18 10:57:59 - Started new registration for membership level #10 via stripe.
2025-6-18 10:57:59 - Using recovered payment #134064 for registration. Transaction type: new.
2025-6-18 10:57:59 - Started new registration for membership level #10 via stripe.
2025-6-18 10:57:59 - Updating membership #34478. New data: array (
  'disabled' => 0,
).
2025-6-18 10:57:59 - Using recovered membership #34478 for registration.
2025-6-18 10:57:59 - Registration type: new.
2025-6-18 10:57:59 - Updating membership #34478. New data: array (
  'auto_renew' => true,
  'gateway' => 'stripe',
  'recurring_amount' => 2.99,
  'subscription_key' => 'd36629e56abe043e9bcbefb2ecb245c9',
  'status' => 'pending',
  'expiration_date' => '2025-07-18 23:59:59',
).
2025-6-18 10:57:59 - Using existing membership #34478 for payment.
2025-6-18 10:57:59 - Updating recovered payment #134064 with new data.
2025-6-18 10:57:59 - Updating payment #134064 with new data: array (
  'date' => '2025-06-18 10:57:59',
  'subscription' => 'Dynasty Trade Calculator (Monthly)',
  'object_id' => 10,
  'object_type' => 'subscription',
  'gateway' => 'stripe',
  'subscription_key' => 'd36629e56abe043e9bcbefb2ecb245c9',
  'amount' => 2.99,
  'user_id' => 25833,
  'customer_id' => '25771',
  'membership_id' => '34478',
  'status' => 'pending',
  'subtotal' => 2.99,
  'credits' => 0,
  'fees' => 0,
  'discount_amount' => 0,
  'discount_code' => '',
  'transaction_type' => 'new',
)
2025-6-18 10:58:00 - Registration for user #25833 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34478
2025-6-18 10:58:00 - Updating membership #34478. New data: array (
  'gateway_customer_id' => 'cus_SWRFE0WbiwM1FT',
).
2025-6-18 10:58:04 - Using recovered payment #134064 for registration. Transaction type: new.
2025-6-18 10:58:04 - Registration for user #25833 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34478
2025-6-18 10:58:06 - Updating payment #134064 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3RbOMlDrMO37mudt4XJ1ybZH',
  'status' => 'complete',
)
2025-6-18 10:58:06 - Completing registration for customer #25771 via payment #134064.
2025-6-18 10:58:06 - Activating membership #34478.
2025-6-18 10:58:06 - Disabling all memberships for customer #25771 except: '34478'.
2025-6-18 10:58:06 - Updating membership #34478. New data: array (
  'activated_date' => '2025-06-18 10:58:06',
).
2025-6-18 10:58:06 - Updating membership #34478. New data: array (
  'status' => 'active',
).
2025-6-18 10:58:06 - Removing old role subscriber, adding new role subscriber for membership #34478 (user ID #25833).
2025-6-18 10:58:06 - Active email sent to user #25833 for membership #34478.
2025-6-18 10:58:06 - Active email sent to admin(s) regarding membership #34478.
2025-6-18 10:58:06 - Payment Received email not sent to user #25833 for payment ID #134064 - message is empty or disabled.
2025-6-18 10:58:06 - Payment Received email not sent to admin(s) for payment ID #134064 - message is empty or disabled.
2025-6-18 10:58:06 - Updating membership #34478. New data: array (
  'times_billed' => 1,
).
2025-6-18 10:58:06 - Payment #134064 completed for member #25833 via Stripe gateway.
2025-6-18 10:58:08 - Stripe Gateway: Using newly calculated expiration for subscription start date: 2025-07-18 23:59:59
2025-6-18 10:58:08 - Stripe Gateway: Creating subscription with 1752868688 start date via trial_end.
2025-6-18 10:58:10 - Updating membership #34478. New data: array (
  'gateway_subscription_id' => 'sub_1RbONJDrMO37mudtE61KPJFa',
).
2025-6-18 20:39:47 - Starting rcp_check_for_expired_users() cron job.
2025-6-18 20:39:47 - No expired memberships found.
2025-6-18 20:39:47 - Starting rcp_check_for_soon_to_expire_users() cron job.
2025-6-18 20:39:47 - Processing expiration reminder. ID: 0; Period: -1month; Levels: 1, 3, 2.
2025-6-18 20:39:47 - Reminder is not enabled - exiting.
2025-6-18 21:44:16 - Started new registration for membership level #10 via stripe.
2025-6-18 21:44:16 - Started new registration for membership level #10 via stripe.
2025-6-18 21:44:17 - Adding a new customer. Args: array (
  'date_registered' => '2025-06-18 21:44:17',
  'has_trialed' => 0,
  'user_id' => 25834,
)
2025-6-18 21:44:17 - Created new customer #25772.
2025-6-18 21:44:17 - Registration type: new.
2025-6-18 21:44:17 - Adding new membership. Data: array (
  'customer_id' => '25772',
  'user_id' => '25834',
  'object_id' => 10,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 2.99,
  'recurring_amount' => 2.99,
  'created_date' => '2025-06-18 21:44:17',
  'expiration_date' => '2025-07-18 23:59:59',
  'auto_renew' => 0,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '8c9e726660c9b247ae90e249ae3d588d',
)
2025-6-18 21:44:18 - New payment inserted. ID: 134065; User ID: 25834; Amount: 2.99; Subscription: Dynasty Trade Calculator (Monthly); Status: pending
2025-6-18 21:44:18 - Registration for user #25834 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: false; Trial: false; Subscription Start: ; Membership ID: 34479
2025-6-18 21:44:19 - Updating membership #34479. New data: array (
  'gateway_customer_id' => 'cus_SWbfBSMeIxTTK1',
).
2025-6-18 21:44:22 - Using recovered payment #134065 for registration. Transaction type: new.
2025-6-18 21:44:22 - Registration for user #25834 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: false; Trial: false; Subscription Start: ; Membership ID: 34479
2025-6-18 21:44:23 - Updating payment #134065 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3RbYSdDrMO37mudt0hgBGmeR',
  'status' => 'complete',
)
2025-6-18 21:44:23 - Completing registration for customer #25772 via payment #134065.
2025-6-18 21:44:23 - Activating membership #34479.
2025-6-18 21:44:23 - Disabling all memberships for customer #25772 except: '34479'.
2025-6-18 21:44:23 - Updating membership #34479. New data: array (
  'activated_date' => '2025-06-18 21:44:23',
).
2025-6-18 21:44:23 - Updating membership #34479. New data: array (
  'status' => 'active',
).
2025-6-18 21:44:24 - Active email sent to user #25834 for membership #34479.
2025-6-18 21:44:24 - Active email sent to admin(s) regarding membership #34479.
2025-6-18 21:44:24 - Payment Received email not sent to user #25834 for payment ID #134065 - message is empty or disabled.
2025-6-18 21:44:24 - Payment Received email not sent to admin(s) for payment ID #134065 - message is empty or disabled.
2025-6-18 21:44:24 - Updating membership #34479. New data: array (
  'times_billed' => 1,
).
2025-6-18 21:44:24 - Payment #134065 completed for member #25834 via Stripe gateway.
2025-6-18 23:04:59 - Starting rcp_check_member_counts() cron job.
2025-6-18 23:27:42 - Starting rcp_mark_abandoned_payments() cron job.
2025-6-18 23:34:46 - Started new registration for membership level #10 via stripe.
2025-6-18 23:34:46 - Started new registration for membership level #10 via stripe.
2025-6-18 23:34:47 - Adding a new customer. Args: array (
  'date_registered' => '2025-06-18 23:34:47',
  'has_trialed' => 0,
  'user_id' => 25835,
)
2025-6-18 23:34:47 - Created new customer #25773.
2025-6-18 23:34:47 - Registration type: new.
2025-6-18 23:34:47 - Adding new membership. Data: array (
  'customer_id' => '25773',
  'user_id' => '25835',
  'object_id' => 10,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 2.99,
  'recurring_amount' => 2.99,
  'created_date' => '2025-06-18 23:34:47',
  'expiration_date' => '2025-07-18 23:59:59',
  'auto_renew' => 0,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => 'a98a9c72151b4635b97f23f1221fc024',
)
2025-6-18 23:34:48 - New payment inserted. ID: 134066; User ID: 25835; Amount: 2.99; Subscription: Dynasty Trade Calculator (Monthly); Status: pending
2025-6-18 23:34:48 - Registration for user #25835 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: false; Trial: false; Subscription Start: ; Membership ID: 34480
2025-6-18 23:34:49 - Updating membership #34480. New data: array (
  'gateway_customer_id' => 'cus_SWdSKDXyuIHtoH',
).
2025-6-18 23:34:51 - Using recovered payment #134066 for registration. Transaction type: new.
2025-6-18 23:34:51 - Registration for user #25835 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: false; Trial: false; Subscription Start: ; Membership ID: 34480
2025-6-18 23:34:52 - Updating payment #134066 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3RbaBZDrMO37mudt1iEivRta',
  'status' => 'complete',
)
2025-6-18 23:34:52 - Completing registration for customer #25773 via payment #134066.
2025-6-18 23:34:52 - Activating membership #34480.
2025-6-18 23:34:52 - Disabling all memberships for customer #25773 except: '34480'.
2025-6-18 23:34:52 - Updating membership #34480. New data: array (
  'activated_date' => '2025-06-18 23:34:52',
).
2025-6-18 23:34:52 - Updating membership #34480. New data: array (
  'status' => 'active',
).
2025-6-18 23:34:52 - Active email sent to user #25835 for membership #34480.
2025-6-18 23:34:52 - Active email sent to admin(s) regarding membership #34480.
2025-6-18 23:34:52 - Payment Received email not sent to user #25835 for payment ID #134066 - message is empty or disabled.
2025-6-18 23:34:52 - Payment Received email not sent to admin(s) for payment ID #134066 - message is empty or disabled.
2025-6-18 23:34:52 - Updating membership #34480. New data: array (
  'times_billed' => 1,
).
2025-6-18 23:34:52 - Payment #134066 completed for member #25835 via Stripe gateway.
2025-6-19 00:00:34 - Started new registration for membership level #10 via stripe.
2025-6-19 00:00:35 - Started new registration for membership level #10 via stripe.
2025-6-19 00:00:35 - Adding a new customer. Args: array (
  'date_registered' => '2025-06-19 00:00:35',
  'has_trialed' => 0,
  'user_id' => 25836,
)
2025-6-19 00:00:35 - Created new customer #25774.
2025-6-19 00:00:35 - Registration type: new.
2025-6-19 00:00:35 - Adding new membership. Data: array (
  'customer_id' => '25774',
  'user_id' => '25836',
  'object_id' => 10,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 2.99,
  'recurring_amount' => 2.99,
  'created_date' => '2025-06-19 00:00:35',
  'expiration_date' => '2025-07-19 23:59:59',
  'auto_renew' => 0,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '49150c59f1f68d1c8e7aaf134fb19fa1',
)
2025-6-19 00:00:36 - New payment inserted. ID: 134067; User ID: 25836; Amount: 2.99; Subscription: Dynasty Trade Calculator (Monthly); Status: pending
2025-6-19 00:00:36 - Registration for user #25836 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: false; Trial: false; Subscription Start: ; Membership ID: 34481
2025-6-19 00:00:37 - Updating membership #34481. New data: array (
  'gateway_customer_id' => 'cus_SWds7cGB4p7R6l',
).
2025-6-19 00:00:40 - Using recovered payment #134067 for registration. Transaction type: new.
2025-6-19 00:00:40 - Registration for user #25836 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: false; Trial: false; Subscription Start: ; Membership ID: 34481
2025-6-19 00:00:41 - Updating payment #134067 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3RbaaXDrMO37mudt4cpjanyf',
  'status' => 'complete',
)
2025-6-19 00:00:41 - Completing registration for customer #25774 via payment #134067.
2025-6-19 00:00:41 - Activating membership #34481.
2025-6-19 00:00:41 - Disabling all memberships for customer #25774 except: '34481'.
2025-6-19 00:00:41 - Updating membership #34481. New data: array (
  'activated_date' => '2025-06-19 00:00:41',
).
2025-6-19 00:00:41 - Updating membership #34481. New data: array (
  'status' => 'active',
).
2025-6-19 00:00:41 - Active email sent to user #25836 for membership #34481.
2025-6-19 00:00:41 - Active email sent to admin(s) regarding membership #34481.
2025-6-19 00:00:41 - Payment Received email not sent to user #25836 for payment ID #134067 - message is empty or disabled.
2025-6-19 00:00:41 - Payment Received email not sent to admin(s) for payment ID #134067 - message is empty or disabled.
2025-6-19 00:00:41 - Updating membership #34481. New data: array (
  'times_billed' => 1,
).
2025-6-19 00:00:41 - Payment #134067 completed for member #25836 via Stripe gateway.
2025-6-19 00:06:31 - Started new registration for membership level #10 via stripe.
2025-6-19 00:07:02 - Started new registration for membership level #10 via stripe.
2025-6-19 00:07:02 - Adding a new customer. Args: array (
  'date_registered' => '2025-06-19 00:07:02',
  'has_trialed' => 0,
  'user_id' => 25837,
)
2025-6-19 00:07:02 - Created new customer #25775.
2025-6-19 00:07:02 - Registration type: new.
2025-6-19 00:07:02 - Adding new membership. Data: array (
  'customer_id' => '25775',
  'user_id' => '25837',
  'object_id' => 10,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 2.99,
  'recurring_amount' => 2.99,
  'created_date' => '2025-06-19 00:07:02',
  'expiration_date' => '2025-07-19 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '9fa2def6a62901a11021e2552c6a3473',
)
2025-6-19 00:07:03 - New payment inserted. ID: 134068; User ID: 25837; Amount: 2.99; Subscription: Dynasty Trade Calculator (Monthly); Status: pending
2025-6-19 00:07:03 - Registration for user #25837 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34482
2025-6-19 00:07:04 - Updating membership #34482. New data: array (
  'gateway_customer_id' => 'cus_SWdyuJnOnFeqEn',
).
2025-6-19 00:13:00 - Started new registration for membership level #10 via stripe.
2025-6-19 00:13:00 - Started new registration for membership level #10 via stripe.
2025-6-19 00:13:01 - Adding a new customer. Args: array (
  'date_registered' => '2025-06-19 00:13:01',
  'has_trialed' => 0,
  'user_id' => 25838,
)
2025-6-19 00:13:01 - Created new customer #25776.
2025-6-19 00:13:01 - Registration type: new.
2025-6-19 00:13:01 - Adding new membership. Data: array (
  'customer_id' => '25776',
  'user_id' => '25838',
  'object_id' => 10,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 2.99,
  'recurring_amount' => 2.99,
  'created_date' => '2025-06-19 00:13:01',
  'expiration_date' => '2025-07-19 23:59:59',
  'auto_renew' => 0,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '358dfb25a4429df748ee6b340db23b36',
)
2025-6-19 00:13:02 - New payment inserted. ID: 134069; User ID: 25838; Amount: 2.99; Subscription: Dynasty Trade Calculator (Monthly); Status: pending
2025-6-19 00:13:02 - Registration for user #25838 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: false; Trial: false; Subscription Start: ; Membership ID: 34483
2025-6-19 00:13:03 - Updating membership #34483. New data: array (
  'gateway_customer_id' => 'cus_SWe4WZKt3l8hLo',
).
2025-6-19 00:13:05 - Using recovered payment #134069 for registration. Transaction type: new.
2025-6-19 00:13:05 - Registration for user #25838 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: false; Trial: false; Subscription Start: ; Membership ID: 34483
2025-6-19 00:13:06 - Updating payment #134069 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3RbamZDrMO37mudt2x9jpEsW',
  'status' => 'complete',
)
2025-6-19 00:13:06 - Completing registration for customer #25776 via payment #134069.
2025-6-19 00:13:06 - Activating membership #34483.
2025-6-19 00:13:06 - Disabling all memberships for customer #25776 except: '34483'.
2025-6-19 00:13:06 - Updating membership #34483. New data: array (
  'activated_date' => '2025-06-19 00:13:06',
).
2025-6-19 00:13:06 - Updating membership #34483. New data: array (
  'status' => 'active',
).
2025-6-19 00:13:07 - Active email sent to user #25838 for membership #34483.
2025-6-19 00:13:07 - Active email sent to admin(s) regarding membership #34483.
2025-6-19 00:13:07 - Payment Received email not sent to user #25838 for payment ID #134069 - message is empty or disabled.
2025-6-19 00:13:07 - Payment Received email not sent to admin(s) for payment ID #134069 - message is empty or disabled.
2025-6-19 00:13:07 - Updating membership #34483. New data: array (
  'times_billed' => 1,
).
2025-6-19 00:13:07 - Payment #134069 completed for member #25838 via Stripe gateway.
2025-6-19 00:44:34 - Started new registration for membership level #10 via stripe.
2025-6-19 00:44:34 - Started new registration for membership level #10 via stripe.
2025-6-19 00:44:34 - Adding a new customer. Args: array (
  'date_registered' => '2025-06-19 00:44:34',
  'has_trialed' => 0,
  'user_id' => 25839,
)
2025-6-19 00:44:34 - Created new customer #25777.
2025-6-19 00:44:34 - Registration type: new.
2025-6-19 00:44:34 - Adding new membership. Data: array (
  'customer_id' => '25777',
  'user_id' => '25839',
  'object_id' => 10,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 2.99,
  'recurring_amount' => 2.99,
  'created_date' => '2025-06-19 00:44:34',
  'expiration_date' => '2025-07-19 23:59:59',
  'auto_renew' => 0,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '********************************',
)
2025-6-19 00:44:35 - New payment inserted. ID: 134070; User ID: 25839; Amount: 2.99; Subscription: Dynasty Trade Calculator (Monthly); Status: pending
2025-6-19 00:44:36 - Registration for user #25839 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: false; Trial: false; Subscription Start: ; Membership ID: 34484
2025-6-19 00:44:36 - Updating membership #34484. New data: array (
  'gateway_customer_id' => 'cus_SWeaUHrU4bHWZL',
).
2025-6-19 00:44:39 - Using recovered payment #134070 for registration. Transaction type: new.
2025-6-19 00:44:39 - Registration for user #25839 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: false; Trial: false; Subscription Start: ; Membership ID: 34484
2025-6-19 00:44:40 - Updating payment #134070 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3RbbH6DrMO37mudt1KABu6iH',
  'status' => 'complete',
)
2025-6-19 00:44:40 - Completing registration for customer #25777 via payment #134070.
2025-6-19 00:44:40 - Activating membership #34484.
2025-6-19 00:44:40 - Disabling all memberships for customer #25777 except: '34484'.
2025-6-19 00:44:40 - Updating membership #34484. New data: array (
  'activated_date' => '2025-06-19 00:44:40',
).
2025-6-19 00:44:40 - Updating membership #34484. New data: array (
  'status' => 'active',
).
2025-6-19 00:44:40 - Active email sent to user #25839 for membership #34484.
2025-6-19 00:44:40 - Active email sent to admin(s) regarding membership #34484.
2025-6-19 00:44:40 - Payment Received email not sent to user #25839 for payment ID #134070 - message is empty or disabled.
2025-6-19 00:44:40 - Payment Received email not sent to admin(s) for payment ID #134070 - message is empty or disabled.
2025-6-19 00:44:40 - Updating membership #34484. New data: array (
  'times_billed' => 1,
).
2025-6-19 00:44:40 - Payment #134070 completed for member #25839 via Stripe gateway.
2025-6-19 01:21:02 - Started new registration for membership level #10 via stripe.
2025-6-19 01:21:03 - Started new registration for membership level #10 via stripe.
2025-6-19 01:21:03 - Adding a new customer. Args: array (
  'date_registered' => '2025-06-19 01:21:03',
  'has_trialed' => 0,
  'user_id' => 25840,
)
2025-6-19 01:21:03 - Created new customer #25778.
2025-6-19 01:21:03 - Registration type: new.
2025-6-19 01:21:03 - Adding new membership. Data: array (
  'customer_id' => '25778',
  'user_id' => '25840',
  'object_id' => 10,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 2.99,
  'recurring_amount' => 2.99,
  'created_date' => '2025-06-19 01:21:03',
  'expiration_date' => '2025-07-19 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '851696bc678e0a9f402ecce4e5aea95c',
)
2025-6-19 01:21:04 - New payment inserted. ID: 134071; User ID: 25840; Amount: 2.99; Subscription: Dynasty Trade Calculator (Monthly); Status: pending
2025-6-19 01:21:04 - Registration for user #25840 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34485
2025-6-19 01:21:04 - Updating membership #34485. New data: array (
  'gateway_customer_id' => 'cus_SWfAoekEP7EeG9',
).
2025-6-19 01:21:07 - Using recovered payment #134071 for registration. Transaction type: new.
2025-6-19 01:21:07 - Registration for user #25840 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34485
2025-6-19 01:21:08 - Updating payment #134071 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3RbbqPDrMO37mudt4W5S5nYN',
  'status' => 'complete',
)
2025-6-19 01:21:08 - Completing registration for customer #25778 via payment #134071.
2025-6-19 01:21:08 - Activating membership #34485.
2025-6-19 01:21:08 - Disabling all memberships for customer #25778 except: '34485'.
2025-6-19 01:21:08 - Updating membership #34485. New data: array (
  'activated_date' => '2025-06-19 01:21:08',
).
2025-6-19 01:21:08 - Updating membership #34485. New data: array (
  'status' => 'active',
).
2025-6-19 01:21:08 - Active email sent to user #25840 for membership #34485.
2025-6-19 01:21:08 - Active email sent to admin(s) regarding membership #34485.
2025-6-19 01:21:08 - Payment Received email not sent to user #25840 for payment ID #134071 - message is empty or disabled.
2025-6-19 01:21:08 - Payment Received email not sent to admin(s) for payment ID #134071 - message is empty or disabled.
2025-6-19 01:21:08 - Updating membership #34485. New data: array (
  'times_billed' => 1,
).
2025-6-19 01:21:08 - Payment #134071 completed for member #25840 via Stripe gateway.
2025-6-19 01:21:10 - Stripe Gateway: Using newly calculated expiration for subscription start date: 2025-07-19 23:59:59
2025-6-19 01:21:10 - Stripe Gateway: Creating subscription with 1752920470 start date via trial_end.
2025-6-19 01:21:11 - Updating membership #34485. New data: array (
  'gateway_subscription_id' => 'sub_1RbbqUDrMO37mudtjosscDSF',
).
2025-6-19 01:26:30 - Started new registration for membership level #11 via stripe.
2025-6-19 01:26:30 - Registration cancelled with the following errors: The discount you entered is invalid.
2025-6-19 01:26:51 - Started new registration for membership level #10 via stripe.
2025-6-19 01:26:52 - Started new registration for membership level #10 via stripe.
2025-6-19 01:26:52 - Adding a new customer. Args: array (
  'date_registered' => '2025-06-19 01:26:52',
  'has_trialed' => 0,
  'user_id' => 25841,
)
2025-6-19 01:26:52 - Created new customer #25779.
2025-6-19 01:26:52 - Registration type: new.
2025-6-19 01:26:52 - Adding new membership. Data: array (
  'customer_id' => '25779',
  'user_id' => '25841',
  'object_id' => 10,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 2.99,
  'recurring_amount' => 2.99,
  'created_date' => '2025-06-19 01:26:52',
  'expiration_date' => '2025-07-19 23:59:59',
  'auto_renew' => 0,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => 'fb0ed3d70014170b4644b89e9d5ef798',
)
2025-6-19 01:26:53 - New payment inserted. ID: 134072; User ID: 25841; Amount: 2.99; Subscription: Dynasty Trade Calculator (Monthly); Status: pending
2025-6-19 01:26:53 - Registration for user #25841 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: false; Trial: false; Subscription Start: ; Membership ID: 34486
2025-6-19 01:26:54 - Updating membership #34486. New data: array (
  'gateway_customer_id' => 'cus_SWfGsZ0r5AEsbX',
).
2025-6-19 01:26:56 - Using recovered payment #134072 for registration. Transaction type: new.
2025-6-19 01:26:57 - Registration for user #25841 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: false; Trial: false; Subscription Start: ; Membership ID: 34486
2025-6-19 01:26:58 - Updating payment #134072 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3Rbbw2DrMO37mudt1H9qSrgW',
  'status' => 'complete',
)
2025-6-19 01:26:58 - Completing registration for customer #25779 via payment #134072.
2025-6-19 01:26:58 - Activating membership #34486.
2025-6-19 01:26:58 - Disabling all memberships for customer #25779 except: '34486'.
2025-6-19 01:26:58 - Updating membership #34486. New data: array (
  'activated_date' => '2025-06-19 01:26:58',
).
2025-6-19 01:26:58 - Updating membership #34486. New data: array (
  'status' => 'active',
).
2025-6-19 01:26:58 - Active email sent to user #25841 for membership #34486.
2025-6-19 01:26:58 - Active email sent to admin(s) regarding membership #34486.
2025-6-19 01:26:58 - Payment Received email not sent to user #25841 for payment ID #134072 - message is empty or disabled.
2025-6-19 01:26:58 - Payment Received email not sent to admin(s) for payment ID #134072 - message is empty or disabled.
2025-6-19 01:26:58 - Updating membership #34486. New data: array (
  'times_billed' => 1,
).
2025-6-19 01:26:58 - Payment #134072 completed for member #25841 via Stripe gateway.
2025-6-19 09:42:54 - Started new registration for membership level #10 via stripe.
2025-6-19 09:42:55 - Started new registration for membership level #10 via stripe.
2025-6-19 09:42:55 - Adding a new customer. Args: array (
  'date_registered' => '2025-06-19 09:42:55',
  'has_trialed' => 0,
  'user_id' => 25842,
)
2025-6-19 09:42:55 - Created new customer #25780.
2025-6-19 09:42:55 - Registration type: new.
2025-6-19 09:42:55 - Adding new membership. Data: array (
  'customer_id' => '25780',
  'user_id' => '25842',
  'object_id' => 10,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 2.99,
  'recurring_amount' => 2.99,
  'created_date' => '2025-06-19 09:42:55',
  'expiration_date' => '2025-07-19 23:59:59',
  'auto_renew' => 0,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => 'f3d66f54c8de04a2516094b1ef3c8812',
)
2025-6-19 09:42:56 - New payment inserted. ID: 134073; User ID: 25842; Amount: 2.99; Subscription: Dynasty Trade Calculator (Monthly); Status: pending
2025-6-19 09:42:56 - Registration for user #25842 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: false; Trial: false; Subscription Start: ; Membership ID: 34487
2025-6-19 09:42:57 - Updating membership #34487. New data: array (
  'gateway_customer_id' => 'cus_SWnGZjOVQJySdC',
).
2025-6-19 09:43:00 - Using recovered payment #134073 for registration. Transaction type: new.
2025-6-19 09:43:00 - Registration for user #25842 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: false; Trial: false; Subscription Start: ; Membership ID: 34487
2025-6-19 09:43:01 - Updating payment #134073 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3Rbjg5DrMO37mudt14nmoAoX',
  'status' => 'complete',
)
2025-6-19 09:43:01 - Completing registration for customer #25780 via payment #134073.
2025-6-19 09:43:01 - Activating membership #34487.
2025-6-19 09:43:01 - Disabling all memberships for customer #25780 except: '34487'.
2025-6-19 09:43:01 - Updating membership #34487. New data: array (
  'activated_date' => '2025-06-19 09:43:01',
).
2025-6-19 09:43:01 - Updating membership #34487. New data: array (
  'status' => 'active',
).
2025-6-19 09:43:01 - Active email sent to user #25842 for membership #34487.
2025-6-19 09:43:01 - Active email sent to admin(s) regarding membership #34487.
2025-6-19 09:43:01 - Payment Received email not sent to user #25842 for payment ID #134073 - message is empty or disabled.
2025-6-19 09:43:01 - Payment Received email not sent to admin(s) for payment ID #134073 - message is empty or disabled.
2025-6-19 09:43:01 - Updating membership #34487. New data: array (
  'times_billed' => 1,
).
2025-6-19 09:43:01 - Payment #134073 completed for member #25842 via Stripe gateway.
2025-6-19 10:42:50 - Started new registration for membership level #10 via stripe.
2025-6-19 10:42:51 - Started new registration for membership level #10 via stripe.
2025-6-19 10:42:51 - Adding a new customer. Args: array (
  'date_registered' => '2025-06-19 10:42:51',
  'has_trialed' => 0,
  'user_id' => 25843,
)
2025-6-19 10:42:51 - Created new customer #25781.
2025-6-19 10:42:51 - Registration type: new.
2025-6-19 10:42:51 - Adding new membership. Data: array (
  'customer_id' => '25781',
  'user_id' => '25843',
  'object_id' => 10,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 2.99,
  'recurring_amount' => 2.99,
  'created_date' => '2025-06-19 10:42:51',
  'expiration_date' => '2025-07-19 23:59:59',
  'auto_renew' => 0,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => 'b80434b4113fe4ac7ad478321c4e52ea',
)
2025-6-19 10:42:51 - New payment inserted. ID: 134074; User ID: 25843; Amount: 2.99; Subscription: Dynasty Trade Calculator (Monthly); Status: pending
2025-6-19 10:42:52 - Registration for user #25843 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: false; Trial: false; Subscription Start: ; Membership ID: 34488
2025-6-19 10:42:52 - Updating membership #34488. New data: array (
  'gateway_customer_id' => 'cus_SWoE7uWdlodBQt',
).
2025-6-19 10:42:55 - Using recovered payment #134074 for registration. Transaction type: new.
2025-6-19 10:42:55 - Registration for user #25843 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: false; Trial: false; Subscription Start: ; Membership ID: 34488
2025-6-19 10:42:57 - Updating payment #134074 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3Rbkc5DrMO37mudt3jlkFCH1',
  'status' => 'complete',
)
2025-6-19 10:42:57 - Completing registration for customer #25781 via payment #134074.
2025-6-19 10:42:57 - Activating membership #34488.
2025-6-19 10:42:57 - Disabling all memberships for customer #25781 except: '34488'.
2025-6-19 10:42:57 - Updating membership #34488. New data: array (
  'activated_date' => '2025-06-19 10:42:57',
).
2025-6-19 10:42:57 - Updating membership #34488. New data: array (
  'status' => 'active',
).
2025-6-19 10:42:57 - Active email sent to user #25843 for membership #34488.
2025-6-19 10:42:57 - Active email sent to admin(s) regarding membership #34488.
2025-6-19 10:42:57 - Payment Received email not sent to user #25843 for payment ID #134074 - message is empty or disabled.
2025-6-19 10:42:57 - Payment Received email not sent to admin(s) for payment ID #134074 - message is empty or disabled.
2025-6-19 10:42:57 - Updating membership #34488. New data: array (
  'times_billed' => 1,
).
2025-6-19 10:42:57 - Payment #134074 completed for member #25843 via Stripe gateway.
2025-6-19 10:43:19 - "Can cancel" status for membership #34488: false. Reason: membership not recurring.
2025-6-19 10:43:19 - "Can cancel" status for membership #34488: false. Reason: membership not recurring.
2025-6-19 10:43:27 - Adding 2.99 proration credits to registration for user #25843.
2025-6-19 10:43:28 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.3282135523614 (ID #12)
2025-6-19 10:43:28 - Adding 2.99 proration credits to registration for user #25843.
2025-6-19 10:43:28 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.3282135523614 (ID #12)
2025-6-19 10:43:28 - Adding 2.99 proration credits to registration for user #25843.
2025-6-19 10:43:46 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.082108145106092 (ID #11)
2025-6-19 10:43:46 - Adding 2.99 proration credits to registration for user #25843.
2025-6-19 10:43:46 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.082108145106092 (ID #11)
2025-6-19 10:43:46 - Adding 2.99 proration credits to registration for user #25843.
2025-6-19 10:44:12 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.082108145106092 (ID #11)
2025-6-19 10:44:12 - Adding 2.99 proration credits to registration for user #25843.
2025-6-19 10:44:12 - Started new registration for membership level #11 via stripe.
2025-6-19 10:44:12 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.082108145106092 (ID #11)
2025-6-19 10:44:12 - Adding 2.99 proration credits to registration for user #25843.
2025-6-19 10:44:12 - Started new registration for membership level #11 via stripe.
2025-6-19 10:44:12 - Registration type: downgrade.
2025-6-19 10:44:12 - Adding new membership. Data: array (
  'customer_id' => '25781',
  'user_id' => '25843',
  'object_id' => 11,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 27.0,
  'recurring_amount' => 29.99,
  'created_date' => '2025-06-19 10:44:12',
  'expiration_date' => '2026-06-19 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => 'b284ba4c2110589a9a82f14b5d234753',
  'upgraded_from' => '34488',
)
2025-6-19 10:44:12 - New payment inserted. ID: 134075; User ID: 25843; Amount: 27.00; Subscription: Dynasty Trade Calculator (Yearly); Status: pending
2025-6-19 10:44:13 - Registration for user #25843 sent to gateway. Level ID: 11; Initial Amount: 27.00; Recurring Amount: 29.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34489
2025-6-19 10:44:13 - Updating membership #34489. New data: array (
  'gateway_customer_id' => 'cus_SWoE7uWdlodBQt',
).
2025-6-19 10:44:16 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.082108145106092 (ID #11)
2025-6-19 10:44:16 - Using recovered payment #134075 for registration. Transaction type: downgrade.
2025-6-19 10:44:16 - Adding 2.99 proration credits to registration for user #25843.
2025-6-19 10:44:16 - Registration for user #25843 sent to gateway. Level ID: 11; Initial Amount: 27.00; Recurring Amount: 29.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34489
2025-6-19 10:44:18 - Updating payment #134075 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3RbkdNDrMO37mudt39sXC1u8',
  'status' => 'complete',
)
2025-6-19 10:44:18 - Completing registration for customer #25781 via payment #134075.
2025-6-19 10:44:18 - Activating membership #34489.
2025-6-19 10:44:18 - Disabling all memberships for customer #25781 except: '34489'.
2025-6-19 10:44:18 - Disabling membership #34488.
2025-6-19 10:44:18 - Updating membership #34488. New data: array (
  'disabled' => 1,
).
2025-6-19 10:44:18 - "Can cancel" status for membership #34488: false. Reason: membership not recurring.
2025-6-19 10:44:18 - Updating membership #34489. New data: array (
  'activated_date' => '2025-06-19 10:44:18',
).
2025-6-19 10:44:18 - Updating membership #34489. New data: array (
  'status' => 'active',
).
2025-6-19 10:44:18 - Removing old role subscriber, adding new role subscriber for membership #34489 (user ID #25843).
2025-6-19 10:44:18 - Active email sent to user #25843 for membership #34489.
2025-6-19 10:44:18 - Active email sent to admin(s) regarding membership #34489.
2025-6-19 10:44:18 - Payment Received email not sent to user #25843 for payment ID #134075 - message is empty or disabled.
2025-6-19 10:44:18 - Payment Received email not sent to admin(s) for payment ID #134075 - message is empty or disabled.
2025-6-19 10:44:18 - Updating membership #34489. New data: array (
  'times_billed' => 1,
).
2025-6-19 10:44:18 - Payment #134075 completed for member #25843 via Stripe gateway.
2025-6-19 10:44:20 - Stripe Gateway: Using newly calculated expiration for subscription start date: 2026-06-19 23:59:59
2025-6-19 10:44:20 - Stripe Gateway: Creating subscription with 1781898260 start date via trial_end.
2025-6-19 10:44:22 - Updating membership #34489. New data: array (
  'gateway_subscription_id' => 'sub_1RbkdVDrMO37mudtIu7RLugd',
).
2025-6-19 10:53:33 - "Can cancel" status for membership #34489: true.
2025-6-19 10:53:33 - "Can cancel" status for membership #34489: true.
2025-6-19 10:53:41 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 10:53:43 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.3282135523614 (ID #12)
2025-6-19 10:53:43 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 10:53:43 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.3282135523614 (ID #12)
2025-6-19 10:53:43 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 10:53:48 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.0982340862423 (ID #10)
2025-6-19 10:53:48 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 10:53:48 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.0982340862423 (ID #10)
2025-6-19 10:53:48 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 10:54:00 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.0982340862423 (ID #10)
2025-6-19 10:54:00 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 10:54:01 - Started new registration for membership level #10 via stripe.
2025-6-19 10:54:01 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.0982340862423 (ID #10)
2025-6-19 10:54:01 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 10:54:01 - Started new registration for membership level #10 via stripe.
2025-6-19 10:54:01 - Registration type: upgrade.
2025-6-19 10:54:01 - Adding new membership. Data: array (
  'customer_id' => '25781',
  'user_id' => '25843',
  'object_id' => 10,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => '0.00',
  'recurring_amount' => 2.99,
  'created_date' => '2025-06-19 10:54:01',
  'expiration_date' => '2026-05-19 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '90d559517ee6c6d59adf67f0ba7a00e6',
  'upgraded_from' => '34489',
)
2025-6-19 10:54:01 - New payment inserted. ID: 134076; User ID: 25843; Amount: 0.00; Subscription: Dynasty Trade Calculator (Monthly); Status: pending
2025-6-19 10:54:02 - Registration for user #25843 sent to gateway. Level ID: 10; Initial Amount: 0.00; Recurring Amount: 2.99; Auto Renew: true; Trial: false; Subscription Start: 2026-05-19 10:54:02; Membership ID: 34490
2025-6-19 10:54:02 - Updating membership #34490. New data: array (
  'gateway_customer_id' => 'cus_SWoE7uWdlodBQt',
).
2025-6-19 10:54:04 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.0982340862423 (ID #10)
2025-6-19 10:54:04 - Using recovered payment #134076 for registration. Transaction type: upgrade.
2025-6-19 10:54:04 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 10:54:04 - Registration for user #25843 sent to gateway. Level ID: 10; Initial Amount: 0.00; Recurring Amount: 2.99; Auto Renew: true; Trial: false; Subscription Start: 2026-05-19 10:54:04; Membership ID: 34490
2025-6-19 10:54:05 - Updating payment #134076 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => '',
  'status' => 'complete',
)
2025-6-19 10:54:06 - Completing registration for customer #25781 via payment #134076.
2025-6-19 10:54:06 - Activating membership #34490.
2025-6-19 10:54:06 - Disabling all memberships for customer #25781 except: '34490'.
2025-6-19 10:54:06 - Disabling membership #34489.
2025-6-19 10:54:06 - Updating membership #34489. New data: array (
  'disabled' => 1,
).
2025-6-19 10:54:06 - "Can cancel" status for membership #34489: true.
2025-6-19 10:54:06 - "Can cancel" status for membership #34489: true.
2025-6-19 10:54:07 - Failed to cancel Stripe payment profile sub_1RbkdVDrMO37mudtIu7RLugd. Error code: resource_missing; Error Message: No such subscription: 'sub_1RbkdVDrMO37mudtIu7RLugd'.
2025-6-19 10:54:07 - Updating membership #34489. New data: array (
  'status' => 'cancelled',
  'cancellation_date' => '2025-06-19 10:54:07',
).
2025-6-19 10:54:07 - Updating recurring status for membership #34489. Customer ID: 25781; Previous: true; New: false
2025-6-19 10:54:07 - Updating membership #34489. New data: array (
  'auto_renew' => 0,
).
2025-6-19 10:54:07 - Payment profile successfully cancelled for membership #34489.
2025-6-19 10:54:07 - Updating membership #34490. New data: array (
  'activated_date' => '2025-06-19 10:54:07',
).
2025-6-19 10:54:07 - Updating membership #34490. New data: array (
  'status' => 'active',
).
2025-6-19 10:54:07 - Removing old role subscriber, adding new role subscriber for membership #34490 (user ID #25843).
2025-6-19 10:54:07 - Active email sent to user #25843 for membership #34490.
2025-6-19 10:54:07 - Active email sent to admin(s) regarding membership #34490.
2025-6-19 10:54:07 - Payment Received email not sent to user #25843 - payment amount is 0.
2025-6-19 10:54:07 - Updating membership #34490. New data: array (
  'times_billed' => 1,
).
2025-6-19 10:54:07 - Payment #134076 completed for member #25843 via Stripe gateway.
2025-6-19 10:54:09 - Stripe Gateway: Using subscription start date for subscription: 2026-05-19 10:54:04
2025-6-19 10:54:09 - Stripe Gateway: Creating subscription with 1779188044 start date via trial_end.
2025-6-19 10:54:10 - Updating membership #34490. New data: array (
  'gateway_subscription_id' => 'sub_1RbkmzDrMO37mudt2hbAVbBV',
).
2025-6-19 10:54:10 - Updating payment #134076 with new data: array (
  'transaction_id' => 'sub_1RbkmzDrMO37mudt2hbAVbBV',
)
2025-6-19 10:59:30 - "Can cancel" status for membership #34490: true.
2025-6-19 10:59:30 - "Can cancel" status for membership #34490: true.
2025-6-19 10:59:37 - Adding 2.99 proration credits to registration for user #25843.
2025-6-19 10:59:38 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.3282135523614 (ID #12)
2025-6-19 10:59:38 - Adding 2.99 proration credits to registration for user #25843.
2025-6-19 10:59:38 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.3282135523614 (ID #12)
2025-6-19 10:59:38 - Adding 2.99 proration credits to registration for user #25843.
2025-6-19 10:59:45 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.082108145106092 (ID #11)
2025-6-19 10:59:45 - Adding 2.99 proration credits to registration for user #25843.
2025-6-19 10:59:45 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.082108145106092 (ID #11)
2025-6-19 10:59:45 - Adding 2.99 proration credits to registration for user #25843.
2025-6-19 10:59:53 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.082108145106092 (ID #11)
2025-6-19 10:59:53 - Adding 2.99 proration credits to registration for user #25843.
2025-6-19 10:59:53 - Started new registration for membership level #11 via stripe.
2025-6-19 10:59:54 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.082108145106092 (ID #11)
2025-6-19 10:59:54 - Adding 2.99 proration credits to registration for user #25843.
2025-6-19 10:59:54 - Started new registration for membership level #11 via stripe.
2025-6-19 10:59:54 - Registration type: downgrade.
2025-6-19 10:59:54 - Adding new membership. Data: array (
  'customer_id' => '25781',
  'user_id' => '25843',
  'object_id' => 11,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 27.0,
  'recurring_amount' => 29.99,
  'created_date' => '2025-06-19 10:59:54',
  'expiration_date' => '2026-06-19 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '339795105304603a2ef9e8b7438de39a',
  'upgraded_from' => '34490',
)
2025-6-19 10:59:55 - New payment inserted. ID: 134077; User ID: 25843; Amount: 27.00; Subscription: Dynasty Trade Calculator (Yearly); Status: pending
2025-6-19 10:59:56 - Registration for user #25843 sent to gateway. Level ID: 11; Initial Amount: 27.00; Recurring Amount: 29.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34491
2025-6-19 10:59:56 - Updating membership #34491. New data: array (
  'gateway_customer_id' => 'cus_SWoE7uWdlodBQt',
).
2025-6-19 10:59:59 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.082108145106092 (ID #11)
2025-6-19 10:59:59 - Using recovered payment #134077 for registration. Transaction type: downgrade.
2025-6-19 10:59:59 - Adding 2.99 proration credits to registration for user #25843.
2025-6-19 10:59:59 - Registration for user #25843 sent to gateway. Level ID: 11; Initial Amount: 27.00; Recurring Amount: 29.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34491
2025-6-19 11:00:01 - Updating payment #134077 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3RbksbDrMO37mudt29mTTkav',
  'status' => 'complete',
)
2025-6-19 11:00:01 - Completing registration for customer #25781 via payment #134077.
2025-6-19 11:00:01 - Activating membership #34491.
2025-6-19 11:00:01 - Disabling all memberships for customer #25781 except: '34491'.
2025-6-19 11:00:01 - Disabling membership #34490.
2025-6-19 11:00:01 - Updating membership #34490. New data: array (
  'disabled' => 1,
).
2025-6-19 11:00:01 - "Can cancel" status for membership #34490: true.
2025-6-19 11:00:01 - "Can cancel" status for membership #34490: true.
2025-6-19 11:00:03 - Failed to cancel Stripe payment profile sub_1RbkmzDrMO37mudt2hbAVbBV. Error code: resource_missing; Error Message: No such subscription: 'sub_1RbkmzDrMO37mudt2hbAVbBV'.
2025-6-19 11:00:03 - Updating membership #34490. New data: array (
  'status' => 'cancelled',
  'cancellation_date' => '2025-06-19 11:00:03',
).
2025-6-19 11:00:03 - Updating recurring status for membership #34490. Customer ID: 25781; Previous: true; New: false
2025-6-19 11:00:03 - Updating membership #34490. New data: array (
  'auto_renew' => 0,
).
2025-6-19 11:00:03 - Payment profile successfully cancelled for membership #34490.
2025-6-19 11:00:03 - Updating membership #34491. New data: array (
  'activated_date' => '2025-06-19 11:00:03',
).
2025-6-19 11:00:03 - Updating membership #34491. New data: array (
  'status' => 'active',
).
2025-6-19 11:00:03 - Removing old role subscriber, adding new role subscriber for membership #34491 (user ID #25843).
2025-6-19 11:00:03 - Active email sent to user #25843 for membership #34491.
2025-6-19 11:00:03 - Active email sent to admin(s) regarding membership #34491.
2025-6-19 11:00:03 - Payment Received email not sent to user #25843 for payment ID #134077 - message is empty or disabled.
2025-6-19 11:00:03 - Payment Received email not sent to admin(s) for payment ID #134077 - message is empty or disabled.
2025-6-19 11:00:03 - Updating membership #34491. New data: array (
  'times_billed' => 1,
).
2025-6-19 11:00:03 - Payment #134077 completed for member #25843 via Stripe gateway.
2025-6-19 11:00:05 - Stripe Gateway: Using newly calculated expiration for subscription start date: 2026-06-19 23:59:59
2025-6-19 11:00:05 - Stripe Gateway: Creating subscription with 1781899205 start date via trial_end.
2025-6-19 11:00:06 - Updating membership #34491. New data: array (
  'gateway_subscription_id' => 'sub_1RbksjDrMO37mudtDlsb3Xz5',
).
2025-6-19 14:31:39 - Starting rcp_check_for_expired_users() cron job.
2025-6-19 14:31:39 - No expired memberships found.
2025-6-19 14:31:39 - Starting rcp_check_for_soon_to_expire_users() cron job.
2025-6-19 14:31:39 - Processing expiration reminder. ID: 0; Period: -1month; Levels: 1, 3, 2.
2025-6-19 14:31:39 - Reminder is not enabled - exiting.
2025-6-19 14:41:18 - "Can cancel" status for membership #34458: false. Reason: membership not recurring.
2025-6-19 14:41:18 - "Can cancel" status for membership #34458: false. Reason: membership not recurring.
2025-6-19 14:41:29 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.0982340862423 (ID #10)
2025-6-19 14:41:29 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 14:41:29 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.0982340862423 (ID #10)
2025-6-19 14:41:29 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 14:42:18 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.3282135523614 (ID #12)
2025-6-19 14:42:18 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 14:42:18 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.3282135523614 (ID #12)
2025-6-19 14:42:18 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 14:42:35 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.0982340862423 (ID #10)
2025-6-19 14:42:35 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 14:42:35 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.0982340862423 (ID #10)
2025-6-19 14:42:36 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 14:42:43 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.0982340862423 (ID #10)
2025-6-19 14:42:43 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 14:42:43 - Started new registration for membership level #10 via stripe.
2025-6-19 14:42:43 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.0982340862423 (ID #10)
2025-6-19 14:42:43 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 14:42:43 - Started new registration for membership level #10 via stripe.
2025-6-19 14:42:43 - Registration type: upgrade.
2025-6-19 14:42:43 - Adding new membership. Data: array (
  'customer_id' => '25781',
  'user_id' => '25843',
  'object_id' => 10,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => '0.00',
  'recurring_amount' => 2.99,
  'created_date' => '2025-06-19 14:42:43',
  'expiration_date' => '2026-05-19 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '973c6717f41fb6e9fa297ae15ecb8e19',
  'upgraded_from' => '34491',
)
2025-6-19 14:42:45 - New payment inserted. ID: 134078; User ID: 25843; Amount: 0.00; Subscription: Dynasty Trade Calculator (Monthly); Status: pending
2025-6-19 14:42:45 - Registration for user #25843 sent to gateway. Level ID: 10; Initial Amount: 0.00; Recurring Amount: 2.99; Auto Renew: true; Trial: false; Subscription Start: 2026-05-19 14:42:45; Membership ID: 34492
2025-6-19 14:42:45 - Updating membership #34492. New data: array (
  'gateway_customer_id' => 'cus_SWoE7uWdlodBQt',
).
2025-6-19 14:42:47 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.0982340862423 (ID #10)
2025-6-19 14:42:47 - Using recovered payment #134078 for registration. Transaction type: upgrade.
2025-6-19 14:42:47 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 14:42:47 - Registration for user #25843 sent to gateway. Level ID: 10; Initial Amount: 0.00; Recurring Amount: 2.99; Auto Renew: true; Trial: false; Subscription Start: 2026-05-19 14:42:47; Membership ID: 34492
2025-6-19 14:42:49 - Updating payment #134078 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => '',
  'status' => 'complete',
)
2025-6-19 14:42:49 - Completing registration for customer #25781 via payment #134078.
2025-6-19 14:42:49 - Activating membership #34492.
2025-6-19 14:42:49 - Disabling all memberships for customer #25781 except: '34492'.
2025-6-19 14:42:49 - Disabling membership #34491.
2025-6-19 14:42:49 - Updating membership #34491. New data: array (
  'disabled' => 1,
).
2025-6-19 14:42:49 - "Can cancel" status for membership #34491: true.
2025-6-19 14:42:49 - "Can cancel" status for membership #34491: true.
2025-6-19 14:42:50 - Failed to cancel Stripe payment profile sub_1RbksjDrMO37mudtDlsb3Xz5. Error code: resource_missing; Error Message: No such subscription: 'sub_1RbksjDrMO37mudtDlsb3Xz5'.
2025-6-19 14:42:50 - Updating membership #34491. New data: array (
  'status' => 'cancelled',
  'cancellation_date' => '2025-06-19 14:42:50',
).
2025-6-19 14:42:50 - Updating recurring status for membership #34491. Customer ID: 25781; Previous: true; New: false
2025-6-19 14:42:50 - Updating membership #34491. New data: array (
  'auto_renew' => 0,
).
2025-6-19 14:42:50 - Payment profile successfully cancelled for membership #34491.
2025-6-19 14:42:50 - Updating membership #34492. New data: array (
  'activated_date' => '2025-06-19 14:42:50',
).
2025-6-19 14:42:50 - Updating membership #34492. New data: array (
  'status' => 'active',
).
2025-6-19 14:42:50 - Removing old role subscriber, adding new role subscriber for membership #34492 (user ID #25843).
2025-6-19 14:42:50 - Active email sent to user #25843 for membership #34492.
2025-6-19 14:42:50 - Active email sent to admin(s) regarding membership #34492.
2025-6-19 14:42:50 - Payment Received email not sent to user #25843 - payment amount is 0.
2025-6-19 14:42:50 - Updating membership #34492. New data: array (
  'times_billed' => 1,
).
2025-6-19 14:42:50 - Payment #134078 completed for member #25843 via Stripe gateway.
2025-6-19 14:42:52 - Stripe Gateway: Using subscription start date for subscription: 2026-05-19 14:42:47
2025-6-19 14:42:52 - Stripe Gateway: Creating subscription with 1779201767 start date via trial_end.
2025-6-19 14:42:53 - Updating membership #34492. New data: array (
  'gateway_subscription_id' => 'sub_1RboMKDrMO37mudt2R8vnj7o',
).
2025-6-19 14:42:53 - Updating payment #134078 with new data: array (
  'transaction_id' => 'sub_1RboMKDrMO37mudt2R8vnj7o',
)
2025-6-19 14:43:06 - "Can cancel" status for membership #34492: true.
2025-6-19 14:43:06 - "Can cancel" status for membership #34492: true.
2025-6-19 14:43:14 - Adding 2.99 proration credits to registration for user #25843.
2025-6-19 14:43:15 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.3282135523614 (ID #12)
2025-6-19 14:43:15 - Adding 2.99 proration credits to registration for user #25843.
2025-6-19 14:43:15 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.3282135523614 (ID #12)
2025-6-19 14:43:15 - Adding 2.99 proration credits to registration for user #25843.
2025-6-19 14:43:22 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.082108145106092 (ID #11)
2025-6-19 14:43:22 - Adding 2.99 proration credits to registration for user #25843.
2025-6-19 14:43:22 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.082108145106092 (ID #11)
2025-6-19 14:43:22 - Adding 2.99 proration credits to registration for user #25843.
2025-6-19 14:43:35 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.082108145106092 (ID #11)
2025-6-19 14:43:35 - Adding 2.99 proration credits to registration for user #25843.
2025-6-19 14:43:35 - Started new registration for membership level #11 via stripe.
2025-6-19 14:43:36 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.082108145106092 (ID #11)
2025-6-19 14:43:36 - Adding 2.99 proration credits to registration for user #25843.
2025-6-19 14:43:36 - Started new registration for membership level #11 via stripe.
2025-6-19 14:43:36 - Registration type: downgrade.
2025-6-19 14:43:36 - Adding new membership. Data: array (
  'customer_id' => '25781',
  'user_id' => '25843',
  'object_id' => 11,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 27.0,
  'recurring_amount' => 29.99,
  'created_date' => '2025-06-19 14:43:36',
  'expiration_date' => '2026-06-19 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '4e9aaa5e274a77acd6c3bffb49126d5c',
  'upgraded_from' => '34492',
)
2025-6-19 14:43:37 - New payment inserted. ID: 134079; User ID: 25843; Amount: 27.00; Subscription: Dynasty Trade Calculator (Yearly); Status: pending
2025-6-19 14:43:37 - Registration for user #25843 sent to gateway. Level ID: 11; Initial Amount: 27.00; Recurring Amount: 29.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34493
2025-6-19 14:43:38 - Updating membership #34493. New data: array (
  'gateway_customer_id' => 'cus_SWoE7uWdlodBQt',
).
2025-6-19 14:43:40 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.082108145106092 (ID #11)
2025-6-19 14:43:40 - Using recovered payment #134079 for registration. Transaction type: downgrade.
2025-6-19 14:43:40 - Adding 2.99 proration credits to registration for user #25843.
2025-6-19 14:43:40 - Registration for user #25843 sent to gateway. Level ID: 11; Initial Amount: 27.00; Recurring Amount: 29.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34493
2025-6-19 14:43:42 - Updating payment #134079 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3RboN4DrMO37mudt2GDanV4U',
  'status' => 'complete',
)
2025-6-19 14:43:42 - Completing registration for customer #25781 via payment #134079.
2025-6-19 14:43:42 - Activating membership #34493.
2025-6-19 14:43:42 - Disabling all memberships for customer #25781 except: '34493'.
2025-6-19 14:43:42 - Disabling membership #34492.
2025-6-19 14:43:42 - Updating membership #34492. New data: array (
  'disabled' => 1,
).
2025-6-19 14:43:42 - "Can cancel" status for membership #34492: true.
2025-6-19 14:43:42 - "Can cancel" status for membership #34492: true.
2025-6-19 14:43:43 - Failed to cancel Stripe payment profile sub_1RboMKDrMO37mudt2R8vnj7o. Error code: resource_missing; Error Message: No such subscription: 'sub_1RboMKDrMO37mudt2R8vnj7o'.
2025-6-19 14:43:43 - Updating membership #34492. New data: array (
  'status' => 'cancelled',
  'cancellation_date' => '2025-06-19 14:43:43',
).
2025-6-19 14:43:43 - Updating recurring status for membership #34492. Customer ID: 25781; Previous: true; New: false
2025-6-19 14:43:43 - Updating membership #34492. New data: array (
  'auto_renew' => 0,
).
2025-6-19 14:43:43 - Payment profile successfully cancelled for membership #34492.
2025-6-19 14:43:43 - Updating membership #34493. New data: array (
  'activated_date' => '2025-06-19 14:43:43',
).
2025-6-19 14:43:43 - Updating membership #34493. New data: array (
  'status' => 'active',
).
2025-6-19 14:43:43 - Removing old role subscriber, adding new role subscriber for membership #34493 (user ID #25843).
2025-6-19 14:43:43 - Active email sent to user #25843 for membership #34493.
2025-6-19 14:43:43 - Active email sent to admin(s) regarding membership #34493.
2025-6-19 14:43:43 - Payment Received email not sent to user #25843 for payment ID #134079 - message is empty or disabled.
2025-6-19 14:43:43 - Payment Received email not sent to admin(s) for payment ID #134079 - message is empty or disabled.
2025-6-19 14:43:43 - Updating membership #34493. New data: array (
  'times_billed' => 1,
).
2025-6-19 14:43:43 - Payment #134079 completed for member #25843 via Stripe gateway.
2025-6-19 14:43:45 - Stripe Gateway: Using newly calculated expiration for subscription start date: 2026-06-19 23:59:59
2025-6-19 14:43:45 - Stripe Gateway: Creating subscription with 1781912625 start date via trial_end.
2025-6-19 14:43:46 - Updating membership #34493. New data: array (
  'gateway_subscription_id' => 'sub_1RboNBDrMO37mudtwHauEPvV',
).
2025-6-19 14:44:07 - "Can cancel" status for membership #34493: true.
2025-6-19 14:44:07 - "Can cancel" status for membership #34493: true.
2025-6-19 14:44:11 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 14:44:13 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.3282135523614 (ID #12)
2025-6-19 14:44:13 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 14:44:13 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.3282135523614 (ID #12)
2025-6-19 14:44:13 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 14:44:38 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.16394250513347 (ID #7)
2025-6-19 14:44:38 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 14:44:38 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.16394250513347 (ID #7)
2025-6-19 14:44:38 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 14:44:44 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.16394250513347 (ID #7)
2025-6-19 14:44:44 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 14:44:44 - Started new registration for membership level #7 via stripe.
2025-6-19 14:44:45 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.16394250513347 (ID #7)
2025-6-19 14:44:45 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 14:44:45 - Started new registration for membership level #7 via stripe.
2025-6-19 14:44:45 - Registration type: upgrade.
2025-6-19 14:44:45 - Adding new membership. Data: array (
  'customer_id' => '25781',
  'user_id' => '25843',
  'object_id' => 7,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => '0.00',
  'recurring_amount' => 4.99,
  'created_date' => '2025-06-19 14:44:45',
  'expiration_date' => '2026-01-19 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '7ea9a2d8dc6750a5a039881daa932ba7',
  'upgraded_from' => '34493',
)
2025-6-19 14:44:46 - New payment inserted. ID: 134080; User ID: 25843; Amount: 0.00; Subscription: Calculator with ChatDTC (50 Credits); Status: pending
2025-6-19 14:44:46 - Registration for user #25843 sent to gateway. Level ID: 7; Initial Amount: 0.00; Recurring Amount: 4.99; Auto Renew: true; Trial: false; Subscription Start: 2026-01-19 14:44:46; Membership ID: 34494
2025-6-19 14:44:46 - Updating membership #34494. New data: array (
  'gateway_customer_id' => 'cus_SWoE7uWdlodBQt',
).
2025-6-19 14:44:48 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.16394250513347 (ID #7)
2025-6-19 14:44:48 - Using recovered payment #134080 for registration. Transaction type: upgrade.
2025-6-19 14:44:48 - Adding 29.99 proration credits to registration for user #25843.
2025-6-19 14:44:48 - Registration for user #25843 sent to gateway. Level ID: 7; Initial Amount: 0.00; Recurring Amount: 4.99; Auto Renew: true; Trial: false; Subscription Start: 2026-01-19 14:44:48; Membership ID: 34494
2025-6-19 14:44:50 - Updating payment #134080 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => '',
  'status' => 'complete',
)
2025-6-19 14:44:50 - Completing registration for customer #25781 via payment #134080.
2025-6-19 14:44:50 - Activating membership #34494.
2025-6-19 14:44:50 - Disabling all memberships for customer #25781 except: '34494'.
2025-6-19 14:44:50 - Disabling membership #34493.
2025-6-19 14:44:50 - Updating membership #34493. New data: array (
  'disabled' => 1,
).
2025-6-19 14:44:50 - "Can cancel" status for membership #34493: true.
2025-6-19 14:44:50 - "Can cancel" status for membership #34493: true.
2025-6-19 14:44:52 - Failed to cancel Stripe payment profile sub_1RboNBDrMO37mudtwHauEPvV. Error code: resource_missing; Error Message: No such subscription: 'sub_1RboNBDrMO37mudtwHauEPvV'.
2025-6-19 14:44:52 - Updating membership #34493. New data: array (
  'status' => 'cancelled',
  'cancellation_date' => '2025-06-19 14:44:52',
).
2025-6-19 14:44:52 - Updating recurring status for membership #34493. Customer ID: 25781; Previous: true; New: false
2025-6-19 14:44:52 - Updating membership #34493. New data: array (
  'auto_renew' => 0,
).
2025-6-19 14:44:52 - Payment profile successfully cancelled for membership #34493.
2025-6-19 14:44:52 - Updating membership #34494. New data: array (
  'activated_date' => '2025-06-19 14:44:52',
).
2025-6-19 14:44:52 - Updating membership #34494. New data: array (
  'status' => 'active',
).
2025-6-19 14:44:52 - Removing old role subscriber, adding new role subscriber for membership #34494 (user ID #25843).
2025-6-19 14:44:52 - Active email sent to user #25843 for membership #34494.
2025-6-19 14:44:52 - Active email sent to admin(s) regarding membership #34494.
2025-6-19 14:44:52 - Payment Received email not sent to user #25843 - payment amount is 0.
2025-6-19 14:44:52 - Updating membership #34494. New data: array (
  'times_billed' => 1,
).
2025-6-19 14:44:52 - Payment #134080 completed for member #25843 via Stripe gateway.
2025-6-19 14:44:53 - Stripe Gateway: Using subscription start date for subscription: 2026-01-19 14:44:48
2025-6-19 14:44:53 - Stripe Gateway: Creating subscription with 1768833888 start date via trial_end.
2025-6-19 14:44:55 - Updating membership #34494. New data: array (
  'gateway_subscription_id' => 'sub_1RboOIDrMO37mudtTlVI7TSK',
).
2025-6-19 14:44:55 - Updating payment #134080 with new data: array (
  'transaction_id' => 'sub_1RboOIDrMO37mudtTlVI7TSK',
)
2025-6-19 14:46:11 - "Can cancel" status for membership #34494: true.
2025-6-19 14:46:11 - "Can cancel" status for membership #34494: true.
2025-6-19 14:46:40 - "Can cancel" status for membership #34494: true.
2025-6-19 14:46:40 - "Can cancel" status for membership #34494: true.
2025-6-20 00:18:29 - Starting rcp_check_member_counts() cron job.
2025-6-20 00:18:29 - Starting rcp_mark_abandoned_payments() cron job.
2025-6-20 00:18:29 - Updating payment #134061 with new data: array (
  'status' => 'abandoned',
)
2025-6-20 00:18:29 - Updating payment #134060 with new data: array (
  'status' => 'abandoned',
)
2025-6-20 00:18:29 - Updating payment #134059 with new data: array (
  'status' => 'abandoned',
)
2025-6-20 01:19:52 - "Can cancel" status for membership #34458: false. Reason: membership not recurring.
2025-6-20 01:19:52 - "Can cancel" status for membership #34458: false. Reason: membership not recurring.
2025-6-20 01:20:03 - Using recovered payment #134061 for registration. Transaction type: upgrade.
2025-6-20 01:20:05 - Old price per day: 0.22965092402464 (ID #9); New price per day: 0.3282135523614 (ID #12)
2025-6-20 01:20:05 - Old price per day: 0.22965092402464 (ID #9); New price per day: 0.3282135523614 (ID #12)
2025-6-20 01:20:08 - Old price per day: 0.22965092402464 (ID #9); New price per day: 0.0982340862423 (ID #10)
2025-6-20 01:20:09 - Old price per day: 0.22965092402464 (ID #9); New price per day: 0.0982340862423 (ID #10)
2025-6-20 01:20:33 - Old price per day: 0.22965092402464 (ID #9); New price per day: 0.0982340862423 (ID #10)
2025-6-20 01:20:33 - Started new registration for membership level #10 via stripe.
2025-6-20 01:20:34 - Old price per day: 0.22965092402464 (ID #9); New price per day: 0.0982340862423 (ID #10)
2025-6-20 01:20:34 - Started new registration for membership level #10 via stripe.
2025-6-20 01:20:34 - Registration type: downgrade.
2025-6-20 01:20:34 - Adding new membership. Data: array (
  'customer_id' => '25758',
  'user_id' => '25056',
  'object_id' => 10,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 2.99,
  'recurring_amount' => 2.99,
  'created_date' => '2025-06-20 01:20:34',
  'expiration_date' => '2025-07-20 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '9eddee76a8a353b69e48eab4af4ffc79',
  'upgraded_from' => '34475',
)
2025-6-20 01:20:34 - New payment inserted. ID: 134081; User ID: 25056; Amount: 2.99; Subscription: Dynasty Trade Calculator (Monthly); Status: pending
2025-6-20 01:20:35 - Registration for user #25056 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34495
2025-6-20 01:20:35 - Updating membership #34495. New data: array (
  'gateway_customer_id' => 'cus_SX2ORt0zDfntvP',
).
2025-6-20 01:20:39 - Old price per day: 0.22965092402464 (ID #9); New price per day: 0.0982340862423 (ID #10)
2025-6-20 01:20:39 - Using recovered payment #134081 for registration. Transaction type: downgrade.
2025-6-20 01:20:39 - Registration for user #25056 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34495
2025-6-20 01:20:40 - Updating payment #134081 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3RbyJUDrMO37mudt2Qz6ls8h',
  'status' => 'complete',
)
2025-6-20 01:20:40 - Completing registration for customer #25758 via payment #134081.
2025-6-20 01:20:40 - Activating membership #34495.
2025-6-20 01:20:40 - Disabling all memberships for customer #25758 except: '34495'.
2025-6-20 01:20:40 - Disabling membership #34475.
2025-6-20 01:20:40 - Updating membership #34475. New data: array (
  'disabled' => 1,
).
2025-6-20 01:20:40 - "Can cancel" status for membership #34475: false. Reason: membership not active.
2025-6-20 01:20:40 - Disabling membership #34474.
2025-6-20 01:20:40 - Updating membership #34474. New data: array (
  'disabled' => 1,
).
2025-6-20 01:20:40 - "Can cancel" status for membership #34474: false. Reason: membership not active.
2025-6-20 01:20:40 - Disabling membership #34473.
2025-6-20 01:20:40 - Updating membership #34473. New data: array (
  'disabled' => 1,
).
2025-6-20 01:20:40 - "Can cancel" status for membership #34473: false. Reason: membership not active.
2025-6-20 01:20:40 - Disabling membership #34458.
2025-6-20 01:20:40 - Updating membership #34458. New data: array (
  'disabled' => 1,
).
2025-6-20 01:20:40 - "Can cancel" status for membership #34458: false. Reason: membership not recurring.
2025-6-20 01:20:40 - Updating membership #34495. New data: array (
  'activated_date' => '2025-06-20 01:20:40',
).
2025-6-20 01:20:40 - Updating membership #34495. New data: array (
  'status' => 'active',
).
2025-6-20 01:20:40 - Removing old role subscriber, adding new role subscriber for membership #34495 (user ID #25056).
2025-6-20 01:20:40 - Active email sent to user #25056 for membership #34495.
2025-6-20 01:20:40 - Active email sent to admin(s) regarding membership #34495.
2025-6-20 01:20:40 - Payment Received email not sent to user #25056 for payment ID #134081 - message is empty or disabled.
2025-6-20 01:20:40 - Payment Received email not sent to admin(s) for payment ID #134081 - message is empty or disabled.
2025-6-20 01:20:40 - Updating membership #34495. New data: array (
  'times_billed' => 1,
).
2025-6-20 01:20:40 - Payment #134081 completed for member #25056 via Stripe gateway.
2025-6-20 01:20:42 - Stripe Gateway: Using newly calculated expiration for subscription start date: 2025-07-20 23:59:59
2025-6-20 01:20:42 - Stripe Gateway: Creating subscription with 1753006842 start date via trial_end.
2025-6-20 01:20:43 - Updating membership #34495. New data: array (
  'gateway_subscription_id' => 'sub_1RbyJaDrMO37mudt6ilSl6Fe',
).
2025-6-20 01:21:07 - "Can cancel" status for membership #34495: true.
2025-6-20 01:21:07 - "Can cancel" status for membership #34495: true.
2025-6-20 01:21:59 - AbidHasan is performing the  bulk action on customers: 0.
2025-6-20 01:22:07 - AbidHasan is deleting customer #25758.
2025-6-20 01:22:07 - Beginning deletion for customer #25758.
2025-6-20 01:22:07 - Disabling all memberships for customer #25758 except: 0.
2025-6-20 01:22:07 - Disabling membership #34495.
2025-6-20 01:22:07 - Updating membership #34495. New data: array (
  'disabled' => 1,
).
2025-6-20 01:22:07 - "Can cancel" status for membership #34495: true.
2025-6-20 01:22:07 - "Can cancel" status for membership #34495: true.
2025-6-20 01:22:09 - Failed to cancel Stripe payment profile sub_1RbyJaDrMO37mudt6ilSl6Fe. Error code: resource_missing; Error Message: No such subscription: 'sub_1RbyJaDrMO37mudt6ilSl6Fe'.
2025-6-20 01:22:09 - Updating membership #34495. New data: array (
  'status' => 'cancelled',
  'cancellation_date' => '2025-06-20 01:22:09',
).
2025-6-20 01:22:09 - Updating recurring status for membership #34495. Customer ID: 25758; Previous: true; New: false
2025-6-20 01:22:09 - Updating membership #34495. New data: array (
  'auto_renew' => 0,
).
2025-6-20 01:22:09 - Payment profile successfully cancelled for membership #34495.
2025-6-20 01:22:09 - Successfully deleted customer #25758.
2025-6-20 03:57:28 - Started new registration for membership level #10 via stripe.
2025-6-20 03:57:28 - Started new registration for membership level #10 via stripe.
2025-6-20 03:57:29 - Adding a new customer. Args: array (
  'date_registered' => '2025-06-20 03:57:29',
  'has_trialed' => 0,
  'user_id' => 25844,
)
2025-6-20 03:57:29 - Created new customer #25782.
2025-6-20 03:57:29 - Registration type: new.
2025-6-20 03:57:29 - Adding new membership. Data: array (
  'customer_id' => '25782',
  'user_id' => '25844',
  'object_id' => 10,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 2.99,
  'recurring_amount' => 2.99,
  'created_date' => '2025-06-20 03:57:29',
  'expiration_date' => '2025-07-20 23:59:59',
  'auto_renew' => 0,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '0ed03f0dcf7074e117f65de07881f0bc',
)
2025-6-20 03:57:30 - New payment inserted. ID: 134082; User ID: 25844; Amount: 2.99; Subscription: Dynasty Trade Calculator (Monthly); Status: pending
2025-6-20 03:57:30 - Registration for user #25844 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: false; Trial: false; Subscription Start: ; Membership ID: 34496
2025-6-20 03:57:31 - Updating membership #34496. New data: array (
  'gateway_customer_id' => 'cus_SX4vEdmrMO33Yi',
).
2025-6-20 03:57:34 - Using recovered payment #134082 for registration. Transaction type: new.
2025-6-20 03:57:34 - Registration for user #25844 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: false; Trial: false; Subscription Start: ; Membership ID: 34496
2025-6-20 03:57:35 - Updating payment #134082 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3Rc0lLDrMO37mudt4DwHtycn',
  'status' => 'complete',
)
2025-6-20 03:57:35 - Completing registration for customer #25782 via payment #134082.
2025-6-20 03:57:35 - Activating membership #34496.
2025-6-20 03:57:35 - Disabling all memberships for customer #25782 except: '34496'.
2025-6-20 03:57:35 - Updating membership #34496. New data: array (
  'activated_date' => '2025-06-20 03:57:35',
).
2025-6-20 03:57:35 - Updating membership #34496. New data: array (
  'status' => 'active',
).
2025-6-20 03:57:35 - Active email sent to user #25844 for membership #34496.
2025-6-20 03:57:35 - Active email sent to admin(s) regarding membership #34496.
2025-6-20 03:57:35 - Payment Received email not sent to user #25844 for payment ID #134082 - message is empty or disabled.
2025-6-20 03:57:35 - Payment Received email not sent to admin(s) for payment ID #134082 - message is empty or disabled.
2025-6-20 03:57:35 - Updating membership #34496. New data: array (
  'times_billed' => 1,
).
2025-6-20 03:57:35 - Payment #134082 completed for member #25844 via Stripe gateway.
2025-6-20 03:59:45 - "Can cancel" status for membership #34496: false. Reason: membership not recurring.
2025-6-20 03:59:45 - "Can cancel" status for membership #34496: false. Reason: membership not recurring.
2025-6-20 03:59:57 - "Can cancel" status for membership #34496: false. Reason: membership not recurring.
2025-6-20 03:59:57 - "Can cancel" status for membership #34496: false. Reason: membership not recurring.
2025-6-20 04:00:05 - Adding 2.99 proration credits to registration for user #25844.
2025-6-20 04:00:06 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.3282135523614 (ID #12)
2025-6-20 04:00:06 - Adding 2.99 proration credits to registration for user #25844.
2025-6-20 04:00:06 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.3282135523614 (ID #12)
2025-6-20 04:00:06 - Adding 2.99 proration credits to registration for user #25844.
2025-6-20 04:00:54 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.082108145106092 (ID #11)
2025-6-20 04:00:54 - Adding 2.99 proration credits to registration for user #25844.
2025-6-20 04:00:54 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.082108145106092 (ID #11)
2025-6-20 04:00:54 - Adding 2.99 proration credits to registration for user #25844.
2025-6-20 04:01:15 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.082108145106092 (ID #11)
2025-6-20 04:01:15 - Adding 2.99 proration credits to registration for user #25844.
2025-6-20 04:01:15 - Started new registration for membership level #11 via stripe.
2025-6-20 04:01:16 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.082108145106092 (ID #11)
2025-6-20 04:01:16 - Adding 2.99 proration credits to registration for user #25844.
2025-6-20 04:01:16 - Started new registration for membership level #11 via stripe.
2025-6-20 04:01:16 - Registration type: downgrade.
2025-6-20 04:01:16 - Adding new membership. Data: array (
  'customer_id' => '25782',
  'user_id' => '25844',
  'object_id' => 11,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 27.0,
  'recurring_amount' => 29.99,
  'created_date' => '2025-06-20 04:01:16',
  'expiration_date' => '2026-06-20 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '325f5dc9d5b11be12f903669f0662a68',
  'upgraded_from' => '34496',
)
2025-6-20 04:01:17 - New payment inserted. ID: 134083; User ID: 25844; Amount: 27.00; Subscription: Dynasty Trade Calculator (Yearly); Status: pending
2025-6-20 04:01:17 - Registration for user #25844 sent to gateway. Level ID: 11; Initial Amount: 27.00; Recurring Amount: 29.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34497
2025-6-20 04:01:17 - Updating membership #34497. New data: array (
  'gateway_customer_id' => 'cus_SX4vEdmrMO33Yi',
).
2025-6-20 04:01:20 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.082108145106092 (ID #11)
2025-6-20 04:01:20 - Using recovered payment #134083 for registration. Transaction type: downgrade.
2025-6-20 04:01:20 - Adding 2.99 proration credits to registration for user #25844.
2025-6-20 04:01:20 - Registration for user #25844 sent to gateway. Level ID: 11; Initial Amount: 27.00; Recurring Amount: 29.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34497
2025-6-20 04:01:22 - Updating payment #134083 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3Rc0p0DrMO37mudt0cFaZzxs',
  'status' => 'complete',
)
2025-6-20 04:01:22 - Completing registration for customer #25782 via payment #134083.
2025-6-20 04:01:22 - Activating membership #34497.
2025-6-20 04:01:22 - Disabling all memberships for customer #25782 except: '34497'.
2025-6-20 04:01:22 - Disabling membership #34496.
2025-6-20 04:01:22 - Updating membership #34496. New data: array (
  'disabled' => 1,
).
2025-6-20 04:01:22 - "Can cancel" status for membership #34496: false. Reason: membership not recurring.
2025-6-20 04:01:22 - Updating membership #34497. New data: array (
  'activated_date' => '2025-06-20 04:01:22',
).
2025-6-20 04:01:22 - Updating membership #34497. New data: array (
  'status' => 'active',
).
2025-6-20 04:01:22 - Removing old role subscriber, adding new role subscriber for membership #34497 (user ID #25844).
2025-6-20 04:01:22 - Active email sent to user #25844 for membership #34497.
2025-6-20 04:01:22 - Active email sent to admin(s) regarding membership #34497.
2025-6-20 04:01:22 - Payment Received email not sent to user #25844 for payment ID #134083 - message is empty or disabled.
2025-6-20 04:01:22 - Payment Received email not sent to admin(s) for payment ID #134083 - message is empty or disabled.
2025-6-20 04:01:22 - Updating membership #34497. New data: array (
  'times_billed' => 1,
).
2025-6-20 04:01:22 - Payment #134083 completed for member #25844 via Stripe gateway.
2025-6-20 04:01:23 - Stripe Gateway: Using newly calculated expiration for subscription start date: 2026-06-20 23:59:59
2025-6-20 04:01:23 - Stripe Gateway: Creating subscription with 1781960483 start date via trial_end.
2025-6-20 04:01:25 - Updating membership #34497. New data: array (
  'gateway_subscription_id' => 'sub_1Rc0p6DrMO37mudtxCdDJjeS',
).
2025-6-20 04:02:45 - "Can cancel" status for membership #34497: true.
2025-6-20 04:02:45 - "Can cancel" status for membership #34497: true.
2025-6-20 04:02:51 - Adding 29.99 proration credits to registration for user #25844.
2025-6-20 04:02:52 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.3282135523614 (ID #12)
2025-6-20 04:02:52 - Adding 29.99 proration credits to registration for user #25844.
2025-6-20 04:02:52 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.3282135523614 (ID #12)
2025-6-20 04:02:52 - Adding 29.99 proration credits to registration for user #25844.
2025-6-20 04:02:56 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.16394250513347 (ID #7)
2025-6-20 04:02:56 - Adding 29.99 proration credits to registration for user #25844.
2025-6-20 04:02:57 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.16394250513347 (ID #7)
2025-6-20 04:02:57 - Adding 29.99 proration credits to registration for user #25844.
2025-6-20 04:03:12 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.16394250513347 (ID #7)
2025-6-20 04:03:12 - Adding 29.99 proration credits to registration for user #25844.
2025-6-20 04:03:12 - Started new registration for membership level #7 via stripe.
2025-6-20 04:03:13 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.16394250513347 (ID #7)
2025-6-20 04:03:13 - Adding 29.99 proration credits to registration for user #25844.
2025-6-20 04:03:13 - Started new registration for membership level #7 via stripe.
2025-6-20 04:03:13 - Registration type: upgrade.
2025-6-20 04:03:13 - Adding new membership. Data: array (
  'customer_id' => '25782',
  'user_id' => '25844',
  'object_id' => 7,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => '0.00',
  'recurring_amount' => 4.99,
  'created_date' => '2025-06-20 04:03:13',
  'expiration_date' => '2026-01-20 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => 'b4077482e60d9e28388f6979c315e9c1',
  'upgraded_from' => '34497',
)
2025-6-20 04:03:14 - New payment inserted. ID: 134084; User ID: 25844; Amount: 0.00; Subscription: Calculator with ChatDTC (50 Credits); Status: pending
2025-6-20 04:03:14 - Registration for user #25844 sent to gateway. Level ID: 7; Initial Amount: 0.00; Recurring Amount: 4.99; Auto Renew: true; Trial: false; Subscription Start: 2026-01-20 04:03:14; Membership ID: 34498
2025-6-20 04:03:15 - Updating membership #34498. New data: array (
  'gateway_customer_id' => 'cus_SX4vEdmrMO33Yi',
).
2025-6-20 04:03:17 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.16394250513347 (ID #7)
2025-6-20 04:03:17 - Using recovered payment #134084 for registration. Transaction type: upgrade.
2025-6-20 04:03:17 - Adding 29.99 proration credits to registration for user #25844.
2025-6-20 04:03:17 - Registration for user #25844 sent to gateway. Level ID: 7; Initial Amount: 0.00; Recurring Amount: 4.99; Auto Renew: true; Trial: false; Subscription Start: 2026-01-20 04:03:17; Membership ID: 34498
2025-6-20 04:03:18 - Updating payment #134084 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => '',
  'status' => 'complete',
)
2025-6-20 04:03:18 - Completing registration for customer #25782 via payment #134084.
2025-6-20 04:03:18 - Activating membership #34498.
2025-6-20 04:03:18 - Disabling all memberships for customer #25782 except: '34498'.
2025-6-20 04:03:18 - Disabling membership #34497.
2025-6-20 04:03:18 - Updating membership #34497. New data: array (
  'disabled' => 1,
).
2025-6-20 04:03:18 - "Can cancel" status for membership #34497: true.
2025-6-20 04:03:18 - "Can cancel" status for membership #34497: true.
2025-6-20 04:03:20 - Failed to cancel Stripe payment profile sub_1Rc0p6DrMO37mudtxCdDJjeS. Error code: resource_missing; Error Message: No such subscription: 'sub_1Rc0p6DrMO37mudtxCdDJjeS'.
2025-6-20 04:03:20 - Updating membership #34497. New data: array (
  'status' => 'cancelled',
  'cancellation_date' => '2025-06-20 04:03:20',
).
2025-6-20 04:03:20 - Updating recurring status for membership #34497. Customer ID: 25782; Previous: true; New: false
2025-6-20 04:03:20 - Updating membership #34497. New data: array (
  'auto_renew' => 0,
).
2025-6-20 04:03:20 - Payment profile successfully cancelled for membership #34497.
2025-6-20 04:03:20 - Updating membership #34498. New data: array (
  'activated_date' => '2025-06-20 04:03:20',
).
2025-6-20 04:03:20 - Updating membership #34498. New data: array (
  'status' => 'active',
).
2025-6-20 04:03:20 - Removing old role subscriber, adding new role subscriber for membership #34498 (user ID #25844).
2025-6-20 04:03:20 - Active email sent to user #25844 for membership #34498.
2025-6-20 04:03:20 - Active email sent to admin(s) regarding membership #34498.
2025-6-20 04:03:20 - Payment Received email not sent to user #25844 - payment amount is 0.
2025-6-20 04:03:20 - Updating membership #34498. New data: array (
  'times_billed' => 1,
).
2025-6-20 04:03:20 - Payment #134084 completed for member #25844 via Stripe gateway.
2025-6-20 04:03:22 - Stripe Gateway: Using subscription start date for subscription: 2026-01-20 04:03:17
2025-6-20 04:03:22 - Stripe Gateway: Creating subscription with 1768881797 start date via trial_end.
2025-6-20 04:03:23 - Updating membership #34498. New data: array (
  'gateway_subscription_id' => 'sub_1Rc0r0DrMO37mudt5Im4L01y',
).
2025-6-20 04:03:23 - Updating payment #134084 with new data: array (
  'transaction_id' => 'sub_1Rc0r0DrMO37mudt5Im4L01y',
)
2025-6-20 04:15:58 - Started new registration for membership level #9 via stripe.
2025-6-20 04:15:58 - Registration cancelled with the following errors: The discount you entered is invalid.
2025-6-20 04:16:17 - Started new registration for membership level #7 via stripe.
2025-6-20 04:16:18 - Started new registration for membership level #7 via stripe.
2025-6-20 04:16:18 - Adding a new customer. Args: array (
  'date_registered' => '2025-06-20 04:16:18',
  'has_trialed' => 0,
  'user_id' => 25845,
)
2025-6-20 04:16:18 - Created new customer #25783.
2025-6-20 04:16:18 - Registration type: new.
2025-6-20 04:16:18 - Adding new membership. Data: array (
  'customer_id' => '25783',
  'user_id' => '25845',
  'object_id' => 7,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 4.99,
  'recurring_amount' => 4.99,
  'created_date' => '2025-06-20 04:16:18',
  'expiration_date' => '2025-07-20 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '05226bafbd2608f0e2d4a3ace135a082',
)
2025-6-20 04:16:19 - New payment inserted. ID: 134085; User ID: 25845; Amount: 4.99; Subscription: Calculator with ChatDTC (50 Credits); Status: pending
2025-6-20 04:16:19 - Registration for user #25845 sent to gateway. Level ID: 7; Initial Amount: 4.99; Recurring Amount: 4.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34499
2025-6-20 04:16:20 - Updating membership #34499. New data: array (
  'gateway_customer_id' => 'cus_SX5D1foYjmoBt4',
).
2025-6-20 04:16:23 - Using recovered payment #134085 for registration. Transaction type: new.
2025-6-20 04:16:23 - Registration for user #25845 sent to gateway. Level ID: 7; Initial Amount: 4.99; Recurring Amount: 4.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34499
2025-6-20 04:16:24 - Updating payment #134085 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3Rc13YDrMO37mudt0Pf12yLV',
  'status' => 'complete',
)
2025-6-20 04:16:24 - Completing registration for customer #25783 via payment #134085.
2025-6-20 04:16:24 - Activating membership #34499.
2025-6-20 04:16:24 - Disabling all memberships for customer #25783 except: '34499'.
2025-6-20 04:16:24 - Updating membership #34499. New data: array (
  'activated_date' => '2025-06-20 04:16:24',
).
2025-6-20 04:16:24 - Updating membership #34499. New data: array (
  'status' => 'active',
).
2025-6-20 04:16:24 - Active email sent to user #25845 for membership #34499.
2025-6-20 04:16:24 - Active email sent to admin(s) regarding membership #34499.
2025-6-20 04:16:24 - Payment Received email not sent to user #25845 for payment ID #134085 - message is empty or disabled.
2025-6-20 04:16:24 - Payment Received email not sent to admin(s) for payment ID #134085 - message is empty or disabled.
2025-6-20 04:16:24 - Updating membership #34499. New data: array (
  'times_billed' => 1,
).
2025-6-20 04:16:24 - Payment #134085 completed for member #25845 via Stripe gateway.
2025-6-20 04:16:26 - Stripe Gateway: Using newly calculated expiration for subscription start date: 2025-07-20 23:59:59
2025-6-20 04:16:26 - Stripe Gateway: Creating subscription with 1753017386 start date via trial_end.
2025-6-20 04:16:27 - Updating membership #34499. New data: array (
  'gateway_subscription_id' => 'sub_1Rc13eDrMO37mudtcjh7ofn7',
).
2025-6-20 04:17:49 - "Can cancel" status for membership #34499: true.
2025-6-20 04:17:49 - "Can cancel" status for membership #34499: true.
2025-6-20 04:19:05 - "Can cancel" status for membership #34499: true.
2025-6-20 04:19:05 - "Can cancel" status for membership #34498: true.
2025-6-20 04:19:05 - "Can cancel" status for membership #34494: true.
2025-6-20 04:19:05 - "Can cancel" status for membership #34487: false. Reason: membership not recurring.
2025-6-20 04:19:05 - "Can cancel" status for membership #34486: false. Reason: membership not recurring.
2025-6-20 04:19:05 - "Can cancel" status for membership #34485: true.
2025-6-20 04:19:05 - "Can cancel" status for membership #34484: false. Reason: membership not recurring.
2025-6-20 04:19:05 - "Can cancel" status for membership #34483: false. Reason: membership not recurring.
2025-6-20 04:19:05 - "Can cancel" status for membership #34482: false. Reason: membership not active.
2025-6-20 04:19:05 - "Can cancel" status for membership #34481: false. Reason: membership not recurring.
2025-6-20 12:15:57 - Started new registration for membership level #10 via stripe.
2025-6-20 12:15:57 - Registration cancelled with the following errors: The discount you entered is invalid.
2025-6-20 12:16:19 - Started new registration for membership level #10 via stripe.
2025-6-20 12:16:20 - Started new registration for membership level #10 via stripe.
2025-6-20 12:16:21 - Adding a new customer. Args: array (
  'date_registered' => '2025-06-20 12:16:21',
  'has_trialed' => 0,
  'user_id' => 25846,
)
2025-6-20 12:16:21 - Created new customer #25784.
2025-6-20 12:16:21 - Registration type: new.
2025-6-20 12:16:21 - Adding new membership. Data: array (
  'customer_id' => '25784',
  'user_id' => '25846',
  'object_id' => 10,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 2.99,
  'recurring_amount' => 2.99,
  'created_date' => '2025-06-20 12:16:21',
  'expiration_date' => '2025-07-20 23:59:59',
  'auto_renew' => 0,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => 'a0cb346ea5484847a32dd10815550f2a',
)
2025-6-20 12:37:15 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.16394250513347 (ID #7)
2025-6-20 12:37:15 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.16394250513347 (ID #7)
2025-6-20 12:37:21 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.22965092402464 (ID #9)
2025-6-20 12:37:21 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.22965092402464 (ID #9)
2025-6-20 12:37:22 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.22965092402464 (ID #9)
2025-6-20 12:37:22 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.22965092402464 (ID #9)
2025-6-20 12:37:22 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.22965092402464 (ID #9)
2025-6-20 12:37:23 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.22965092402464 (ID #9)
2025-6-20 12:37:23 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.22965092402464 (ID #9)
2025-6-20 12:37:23 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.22965092402464 (ID #9)
2025-6-20 12:37:23 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.22965092402464 (ID #9)
2025-6-20 12:37:23 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.22965092402464 (ID #9)
2025-6-20 12:37:24 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.22965092402464 (ID #9)
2025-6-20 12:37:24 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.22965092402464 (ID #9)
2025-6-20 12:37:25 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.16394250513347 (ID #7)
2025-6-20 12:37:25 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.16394250513347 (ID #7)
2025-6-20 12:37:50 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.16394250513347 (ID #7)
2025-6-20 12:37:50 - Started new registration for membership level #7 via stripe.
2025-6-20 12:37:51 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.16394250513347 (ID #7)
2025-6-20 12:37:51 - Started new registration for membership level #7 via stripe.
2025-6-20 12:37:51 - Registration type: upgrade.
2025-6-20 12:37:51 - Adding new membership. Data: array (
  'customer_id' => '25784',
  'user_id' => '25846',
  'object_id' => 7,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 4.99,
  'recurring_amount' => 4.99,
  'created_date' => '2025-06-20 12:37:51',
  'expiration_date' => '2025-07-20 23:59:59',
  'auto_renew' => 0,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => 'fbe08af3d02f997edf2e8bdf9d893fee',
  'upgraded_from' => '34500',
)
2025-6-20 12:37:51 - New payment inserted. ID: 134086; User ID: 25846; Amount: 4.99; Subscription: Calculator with ChatDTC (50 Credits); Status: pending
2025-6-20 12:37:52 - Registration for user #25846 sent to gateway. Level ID: 7; Initial Amount: 4.99; Recurring Amount: 4.99; Auto Renew: false; Trial: false; Subscription Start: ; Membership ID: 34501
2025-6-20 12:37:52 - Updating membership #34501. New data: array (
  'gateway_customer_id' => 'cus_SXDJr75TSpZ6EI',
).
2025-6-20 12:37:55 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.16394250513347 (ID #7)
2025-6-20 12:37:55 - Using recovered payment #134086 for registration. Transaction type: upgrade.
2025-6-20 12:37:55 - Registration for user #25846 sent to gateway. Level ID: 7; Initial Amount: 4.99; Recurring Amount: 4.99; Auto Renew: false; Trial: false; Subscription Start: ; Membership ID: 34501
2025-6-20 12:37:57 - Updating payment #134086 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3Rc8svDrMO37mudt0HYa9ydy',
  'status' => 'complete',
)
2025-6-20 12:37:57 - Completing registration for customer #25784 via payment #134086.
2025-6-20 12:37:57 - Activating membership #34501.
2025-6-20 12:37:57 - Disabling all memberships for customer #25784 except: '34501'.
2025-6-20 12:37:57 - Disabling membership #34500.
2025-6-20 12:37:57 - Updating membership #34500. New data: array (
  'disabled' => 1,
).
2025-6-20 12:37:57 - "Can cancel" status for membership #34500: false. Reason: membership not recurring.
2025-6-20 12:37:57 - Updating membership #34501. New data: array (
  'activated_date' => '2025-06-20 12:37:57',
).
2025-6-20 12:37:57 - Updating membership #34501. New data: array (
  'status' => 'active',
).
2025-6-20 12:37:57 - Removing old role subscriber, adding new role subscriber for membership #34501 (user ID #25846).
2025-6-20 12:37:57 - Active email sent to user #25846 for membership #34501.
2025-6-20 12:37:57 - Active email sent to admin(s) regarding membership #34501.
2025-6-20 12:37:57 - Payment Received email not sent to user #25846 for payment ID #134086 - message is empty or disabled.
2025-6-20 12:37:57 - Payment Received email not sent to admin(s) for payment ID #134086 - message is empty or disabled.
2025-6-20 12:37:57 - Updating membership #34501. New data: array (
  'times_billed' => 1,
).
2025-6-20 12:37:57 - Payment #134086 completed for member #25846 via Stripe gateway.
2025-6-20 12:41:48 - Started new registration for membership level #7 via stripe.
2025-6-20 12:41:48 - Registration cancelled with the following errors: The card holder name you have entered is invalid.
2025-6-20 12:41:55 - Started new registration for membership level #7 via stripe.
2025-6-20 12:41:56 - Started new registration for membership level #7 via stripe.
2025-6-20 12:41:56 - Adding a new customer. Args: array (
  'date_registered' => '2025-06-20 12:41:56',
  'has_trialed' => 0,
  'user_id' => 25847,
)
2025-6-20 12:41:56 - Created new customer #25785.
2025-6-20 12:41:56 - Registration type: new.
2025-6-20 12:41:56 - Adding new membership. Data: array (
  'customer_id' => '25785',
  'user_id' => '25847',
  'object_id' => 7,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 4.99,
  'recurring_amount' => 4.99,
  'created_date' => '2025-06-20 12:41:56',
  'expiration_date' => '2025-07-20 23:59:59',
  'auto_renew' => 0,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => 'a351766e1ea6828120bfda9cb3d9bb3f',
)
2025-6-20 12:41:57 - New payment inserted. ID: 134087; User ID: 25847; Amount: 4.99; Subscription: Calculator with ChatDTC (50 Credits); Status: pending
2025-6-20 12:41:57 - Registration for user #25847 sent to gateway. Level ID: 7; Initial Amount: 4.99; Recurring Amount: 4.99; Auto Renew: false; Trial: false; Subscription Start: ; Membership ID: 34502
2025-6-20 12:41:58 - Updating membership #34502. New data: array (
  'gateway_customer_id' => 'cus_SXDN9xpeeuX4IJ',
).
2025-6-20 12:42:01 - Using recovered payment #134087 for registration. Transaction type: new.
2025-6-20 12:42:01 - Registration for user #25847 sent to gateway. Level ID: 7; Initial Amount: 4.99; Recurring Amount: 4.99; Auto Renew: false; Trial: false; Subscription Start: ; Membership ID: 34502
2025-6-20 12:42:02 - Updating payment #134087 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3Rc8wsDrMO37mudt2NyZEy3Y',
  'status' => 'complete',
)
2025-6-20 12:42:02 - Completing registration for customer #25785 via payment #134087.
2025-6-20 12:42:02 - Activating membership #34502.
2025-6-20 12:42:02 - Disabling all memberships for customer #25785 except: '34502'.
2025-6-20 12:42:02 - Updating membership #34502. New data: array (
  'activated_date' => '2025-06-20 12:42:02',
).
2025-6-20 12:42:02 - Updating membership #34502. New data: array (
  'status' => 'active',
).
2025-6-20 12:42:02 - Active email sent to user #25847 for membership #34502.
2025-6-20 12:42:02 - Active email sent to admin(s) regarding membership #34502.
2025-6-20 12:42:02 - Payment Received email not sent to user #25847 for payment ID #134087 - message is empty or disabled.
2025-6-20 12:42:02 - Payment Received email not sent to admin(s) for payment ID #134087 - message is empty or disabled.
2025-6-20 12:42:02 - Updating membership #34502. New data: array (
  'times_billed' => 1,
).
2025-6-20 12:42:02 - Payment #134087 completed for member #25847 via Stripe gateway.
2025-6-20 12:43:40 - "Can cancel" status for membership #34502: false. Reason: membership not recurring.
2025-6-20 12:43:40 - "Can cancel" status for membership #34502: false. Reason: membership not recurring.
2025-6-20 12:48:04 - Adding 4.99 proration credits to registration for user #25847.
2025-6-20 12:48:05 - Old price per day: 0.16394250513347 (ID #7); New price per day: 0.3282135523614 (ID #12)
2025-6-20 12:48:05 - Adding 4.99 proration credits to registration for user #25847.
2025-6-20 12:48:05 - Old price per day: 0.16394250513347 (ID #7); New price per day: 0.3282135523614 (ID #12)
2025-6-20 12:48:05 - Adding 4.99 proration credits to registration for user #25847.
2025-6-20 12:48:10 - Old price per day: 0.16394250513347 (ID #7); New price per day: 0.0982340862423 (ID #10)
2025-6-20 12:48:10 - Adding 4.99 proration credits to registration for user #25847.
2025-6-20 12:48:10 - Old price per day: 0.16394250513347 (ID #7); New price per day: 0.0982340862423 (ID #10)
2025-6-20 12:48:10 - Adding 4.99 proration credits to registration for user #25847.
2025-6-20 12:48:19 - Old price per day: 0.16394250513347 (ID #7); New price per day: 0.0982340862423 (ID #10)
2025-6-20 12:48:19 - Adding 4.99 proration credits to registration for user #25847.
2025-6-20 12:48:19 - Started new registration for membership level #10 via stripe.
2025-6-20 12:48:20 - Old price per day: 0.16394250513347 (ID #7); New price per day: 0.0982340862423 (ID #10)
2025-6-20 12:48:20 - Adding 4.99 proration credits to registration for user #25847.
2025-6-20 12:48:20 - Started new registration for membership level #10 via stripe.
2025-6-20 12:48:20 - Registration type: downgrade.
2025-6-20 12:48:20 - Adding new membership. Data: array (
  'customer_id' => '25785',
  'user_id' => '25847',
  'object_id' => 10,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => '0.00',
  'recurring_amount' => 2.99,
  'created_date' => '2025-06-20 12:48:20',
  'expiration_date' => '2025-08-20 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '9c7a0d8fe5b106980dd5b0a39582c335',
  'upgraded_from' => '34502',
)
2025-6-20 12:48:21 - New payment inserted. ID: 134088; User ID: 25847; Amount: 0.00; Subscription: Dynasty Trade Calculator (Monthly); Status: pending
2025-6-20 12:48:21 - Registration for user #25847 sent to gateway. Level ID: 10; Initial Amount: 0.00; Recurring Amount: 2.99; Auto Renew: true; Trial: false; Subscription Start: 2025-07-20 12:48:21; Membership ID: 34503
2025-6-20 12:48:21 - Updating membership #34503. New data: array (
  'gateway_customer_id' => 'cus_SXDN9xpeeuX4IJ',
).
2025-6-20 12:48:23 - Old price per day: 0.16394250513347 (ID #7); New price per day: 0.0982340862423 (ID #10)
2025-6-20 12:48:23 - Using recovered payment #134088 for registration. Transaction type: downgrade.
2025-6-20 12:48:23 - Adding 4.99 proration credits to registration for user #25847.
2025-6-20 12:48:23 - Registration for user #25847 sent to gateway. Level ID: 10; Initial Amount: 0.00; Recurring Amount: 2.99; Auto Renew: true; Trial: false; Subscription Start: 2025-07-20 12:48:23; Membership ID: 34503
2025-6-20 12:48:24 - Updating payment #134088 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => '',
  'status' => 'complete',
)
2025-6-20 12:48:24 - Completing registration for customer #25785 via payment #134088.
2025-6-20 12:48:24 - Activating membership #34503.
2025-6-20 12:48:24 - Disabling all memberships for customer #25785 except: '34503'.
2025-6-20 12:48:24 - Disabling membership #34502.
2025-6-20 12:48:24 - Updating membership #34502. New data: array (
  'disabled' => 1,
).
2025-6-20 12:48:24 - "Can cancel" status for membership #34502: false. Reason: membership not recurring.
2025-6-20 12:48:24 - Updating membership #34503. New data: array (
  'activated_date' => '2025-06-20 12:48:24',
).
2025-6-20 12:48:24 - Updating membership #34503. New data: array (
  'status' => 'active',
).
2025-6-20 12:48:25 - Removing old role subscriber, adding new role subscriber for membership #34503 (user ID #25847).
2025-6-20 12:48:25 - Active email sent to user #25847 for membership #34503.
2025-6-20 12:48:25 - Active email sent to admin(s) regarding membership #34503.
2025-6-20 12:48:25 - Payment Received email not sent to user #25847 - payment amount is 0.
2025-6-20 12:48:25 - Updating membership #34503. New data: array (
  'times_billed' => 1,
).
2025-6-20 12:48:25 - Payment #134088 completed for member #25847 via Stripe gateway.
2025-6-20 12:48:26 - Stripe Gateway: Using subscription start date for subscription: 2025-07-20 12:48:23
2025-6-20 12:48:26 - Stripe Gateway: Creating subscription with 1753015703 start date via billing_cycle_anchor.
2025-6-20 12:48:27 - Updating membership #34503. New data: array (
  'gateway_subscription_id' => 'sub_1Rc939DrMO37mudt39l1alII',
).
2025-6-20 12:48:27 - Updating payment #134088 with new data: array (
  'transaction_id' => 'sub_1Rc939DrMO37mudt39l1alII',
)
2025-6-20 12:48:38 - "Can cancel" status for membership #34503: true.
2025-6-20 12:48:38 - "Can cancel" status for membership #34503: true.
2025-6-20 12:49:15 - Adding 2.99 proration credits to registration for user #25847.
2025-6-20 12:49:17 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.3282135523614 (ID #12)
2025-6-20 12:49:17 - Adding 2.99 proration credits to registration for user #25847.
2025-6-20 12:49:17 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.3282135523614 (ID #12)
2025-6-20 12:49:17 - Adding 2.99 proration credits to registration for user #25847.
2025-6-20 12:49:40 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.16394250513347 (ID #7)
2025-6-20 12:49:40 - Adding 2.99 proration credits to registration for user #25847.
2025-6-20 12:49:41 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.16394250513347 (ID #7)
2025-6-20 12:49:41 - Adding 2.99 proration credits to registration for user #25847.
2025-6-20 12:49:48 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.16394250513347 (ID #7)
2025-6-20 12:49:48 - Adding 2.99 proration credits to registration for user #25847.
2025-6-20 12:49:48 - Started new registration for membership level #7 via stripe.
2025-6-20 12:50:18 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.16394250513347 (ID #7)
2025-6-20 12:50:18 - Adding 2.99 proration credits to registration for user #25847.
2025-6-20 12:50:18 - Started new registration for membership level #7 via stripe.
2025-6-20 12:50:18 - Registration type: upgrade.
2025-6-20 12:50:18 - Adding new membership. Data: array (
  'customer_id' => '25785',
  'user_id' => '25847',
  'object_id' => 7,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 2.0,
  'recurring_amount' => 4.99,
  'created_date' => '2025-06-20 12:50:18',
  'expiration_date' => '2025-07-20 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '74bb50b86ec539e802617e70a9b446b2',
  'upgraded_from' => '34503',
)
2025-6-20 12:50:19 - New payment inserted. ID: 134089; User ID: 25847; Amount: 2.00; Subscription: Calculator with ChatDTC (50 Credits); Status: pending
2025-6-20 12:50:20 - Registration for user #25847 sent to gateway. Level ID: 7; Initial Amount: 2.00; Recurring Amount: 4.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34504
2025-6-20 12:50:21 - Updating membership #34504. New data: array (
  'gateway_customer_id' => 'cus_SXDN9xpeeuX4IJ',
).
2025-6-20 12:50:24 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.16394250513347 (ID #7)
2025-6-20 12:50:24 - Using recovered payment #134089 for registration. Transaction type: upgrade.
2025-6-20 12:50:24 - Adding 2.99 proration credits to registration for user #25847.
2025-6-20 12:50:24 - Registration for user #25847 sent to gateway. Level ID: 7; Initial Amount: 2.00; Recurring Amount: 4.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34504
2025-6-20 12:50:25 - Updating payment #134089 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3Rc94zDrMO37mudt0wLw4rga',
  'status' => 'complete',
)
2025-6-20 12:50:25 - Completing registration for customer #25785 via payment #134089.
2025-6-20 12:50:25 - Activating membership #34504.
2025-6-20 12:50:25 - Disabling all memberships for customer #25785 except: '34504'.
2025-6-20 12:50:25 - Disabling membership #34503.
2025-6-20 12:50:25 - Updating membership #34503. New data: array (
  'disabled' => 1,
).
2025-6-20 12:50:25 - "Can cancel" status for membership #34503: true.
2025-6-20 12:50:25 - "Can cancel" status for membership #34503: true.
2025-6-20 12:50:26 - Failed to cancel Stripe payment profile sub_1Rc939DrMO37mudt39l1alII. Error code: resource_missing; Error Message: No such subscription: 'sub_1Rc939DrMO37mudt39l1alII'.
2025-6-20 12:50:26 - Updating membership #34503. New data: array (
  'status' => 'cancelled',
  'cancellation_date' => '2025-06-20 12:50:26',
).
2025-6-20 12:50:26 - Updating recurring status for membership #34503. Customer ID: 25785; Previous: true; New: false
2025-6-20 12:50:26 - Updating membership #34503. New data: array (
  'auto_renew' => 0,
).
2025-6-20 12:50:26 - Payment profile successfully cancelled for membership #34503.
2025-6-20 12:50:26 - Updating membership #34504. New data: array (
  'activated_date' => '2025-06-20 12:50:26',
).
2025-6-20 12:50:26 - Updating membership #34504. New data: array (
  'status' => 'active',
).
2025-6-20 12:50:26 - Removing old role subscriber, adding new role subscriber for membership #34504 (user ID #25847).
2025-6-20 12:50:27 - Active email sent to user #25847 for membership #34504.
2025-6-20 12:50:27 - Active email sent to admin(s) regarding membership #34504.
2025-6-20 12:50:27 - Payment Received email not sent to user #25847 for payment ID #134089 - message is empty or disabled.
2025-6-20 12:50:27 - Payment Received email not sent to admin(s) for payment ID #134089 - message is empty or disabled.
2025-6-20 12:50:27 - Updating membership #34504. New data: array (
  'times_billed' => 1,
).
2025-6-20 12:50:27 - Payment #134089 completed for member #25847 via Stripe gateway.
2025-6-20 12:50:28 - Stripe Gateway: Using newly calculated expiration for subscription start date: 2025-07-20 23:59:59
2025-6-20 12:50:28 - Stripe Gateway: Creating subscription with 1753048228 start date via trial_end.
2025-6-20 12:50:30 - Updating membership #34504. New data: array (
  'gateway_subscription_id' => 'sub_1Rc956DrMO37mudt2YBlmBVR',
).
2025-6-20 12:51:01 - "Can cancel" status for membership #34504: true.
2025-6-20 12:51:01 - "Can cancel" status for membership #34504: true.
2025-6-20 12:55:05 - Started new registration for membership level #10 via stripe.
2025-6-20 12:55:06 - Started new registration for membership level #10 via stripe.
2025-6-20 12:55:06 - Adding a new customer. Args: array (
  'date_registered' => '2025-06-20 12:55:06',
  'has_trialed' => 0,
  'user_id' => 25848,
)
2025-6-20 12:55:06 - Created new customer #25786.
2025-6-20 12:55:06 - Registration type: new.
2025-6-20 12:55:06 - Adding new membership. Data: array (
  'customer_id' => '25786',
  'user_id' => '25848',
  'object_id' => 10,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 2.99,
  'recurring_amount' => 2.99,
  'created_date' => '2025-06-20 12:55:06',
  'expiration_date' => '2025-07-20 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '9cde27dde0478681552fd21c0b7611ad',
)
2025-6-20 12:55:06 - New payment inserted. ID: 134090; User ID: 25848; Amount: 2.99; Subscription: Dynasty Trade Calculator (Monthly); Status: pending
2025-6-20 12:55:07 - Registration for user #25848 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34505
2025-6-20 12:55:07 - Updating membership #34505. New data: array (
  'gateway_customer_id' => 'cus_SXDaAGtRctxGdW',
).
2025-6-20 12:55:10 - Using recovered payment #134090 for registration. Transaction type: new.
2025-6-20 12:55:10 - Registration for user #25848 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34505
2025-6-20 12:55:11 - Updating payment #134090 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3Rc99cDrMO37mudt1o3Labz2',
  'status' => 'complete',
)
2025-6-20 12:55:11 - Completing registration for customer #25786 via payment #134090.
2025-6-20 12:55:11 - Activating membership #34505.
2025-6-20 12:55:11 - Disabling all memberships for customer #25786 except: '34505'.
2025-6-20 12:55:11 - Updating membership #34505. New data: array (
  'activated_date' => '2025-06-20 12:55:11',
).
2025-6-20 12:55:11 - Updating membership #34505. New data: array (
  'status' => 'active',
).
2025-6-20 12:55:11 - Active email sent to user #25848 for membership #34505.
2025-6-20 12:55:11 - Active email sent to admin(s) regarding membership #34505.
2025-6-20 12:55:11 - Payment Received email not sent to user #25848 for payment ID #134090 - message is empty or disabled.
2025-6-20 12:55:11 - Payment Received email not sent to admin(s) for payment ID #134090 - message is empty or disabled.
2025-6-20 12:55:11 - Updating membership #34505. New data: array (
  'times_billed' => 1,
).
2025-6-20 12:55:11 - Payment #134090 completed for member #25848 via Stripe gateway.
2025-6-20 12:55:13 - Stripe Gateway: Using newly calculated expiration for subscription start date: 2025-07-20 23:59:59
2025-6-20 12:55:13 - Stripe Gateway: Creating subscription with 1753048513 start date via trial_end.
2025-6-20 12:55:14 - Updating membership #34505. New data: array (
  'gateway_subscription_id' => 'sub_1Rc99hDrMO37mudtMyBAtyF9',
).
2025-6-20 12:56:14 - "Can cancel" status for membership #34505: true.
2025-6-20 12:56:14 - "Can cancel" status for membership #34505: true.
2025-6-20 12:56:21 - Adding 2.99 proration credits to registration for user #25848.
2025-6-20 12:56:23 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.3282135523614 (ID #12)
2025-6-20 12:56:23 - Adding 2.99 proration credits to registration for user #25848.
2025-6-20 12:56:23 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.3282135523614 (ID #12)
2025-6-20 12:56:23 - Adding 2.99 proration credits to registration for user #25848.
2025-6-20 13:11:41 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.16394250513347 (ID #7)
2025-6-20 13:11:41 - Adding 2.99 proration credits to registration for user #25848.
2025-6-20 13:11:42 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.16394250513347 (ID #7)
2025-6-20 13:11:42 - Adding 2.99 proration credits to registration for user #25848.
2025-6-20 13:11:54 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.16394250513347 (ID #7)
2025-6-20 13:11:54 - Adding 2.99 proration credits to registration for user #25848.
2025-6-20 13:11:54 - Started new registration for membership level #7 via stripe.
2025-6-20 13:11:54 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.16394250513347 (ID #7)
2025-6-20 13:11:55 - Adding 2.99 proration credits to registration for user #25848.
2025-6-20 13:11:55 - Started new registration for membership level #7 via stripe.
2025-6-20 13:11:55 - Registration type: upgrade.
2025-6-20 13:11:55 - Adding new membership. Data: array (
  'customer_id' => '25786',
  'user_id' => '25848',
  'object_id' => 7,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 2.0,
  'recurring_amount' => 4.99,
  'created_date' => '2025-06-20 13:11:55',
  'expiration_date' => '2025-07-20 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => 'd906411ddb3188c4624a2901705b38c7',
  'upgraded_from' => '34505',
)
2025-6-20 13:11:56 - New payment inserted. ID: 134091; User ID: 25848; Amount: 2.00; Subscription: Calculator with ChatDTC (50 Credits); Status: pending
2025-6-20 13:11:56 - Registration for user #25848 sent to gateway. Level ID: 7; Initial Amount: 2.00; Recurring Amount: 4.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34506
2025-6-20 13:11:56 - Updating membership #34506. New data: array (
  'gateway_customer_id' => 'cus_SXDaAGtRctxGdW',
).
2025-6-20 13:11:59 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.16394250513347 (ID #7)
2025-6-20 13:11:59 - Using recovered payment #134091 for registration. Transaction type: upgrade.
2025-6-20 13:11:59 - Adding 2.99 proration credits to registration for user #25848.
2025-6-20 13:11:59 - Registration for user #25848 sent to gateway. Level ID: 7; Initial Amount: 2.00; Recurring Amount: 4.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34506
2025-6-20 13:12:01 - Updating payment #134091 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3Rc9PtDrMO37mudt2sEmwqpe',
  'status' => 'complete',
)
2025-6-20 13:12:01 - Completing registration for customer #25786 via payment #134091.
2025-6-20 13:12:01 - Activating membership #34506.
2025-6-20 13:12:01 - Disabling all memberships for customer #25786 except: '34506'.
2025-6-20 13:12:01 - Disabling membership #34505.
2025-6-20 13:12:01 - Updating membership #34505. New data: array (
  'disabled' => 1,
).
2025-6-20 13:12:01 - "Can cancel" status for membership #34505: true.
2025-6-20 13:12:01 - "Can cancel" status for membership #34505: true.
2025-6-20 13:12:02 - Failed to cancel Stripe payment profile sub_1Rc99hDrMO37mudtMyBAtyF9. Error code: resource_missing; Error Message: No such subscription: 'sub_1Rc99hDrMO37mudtMyBAtyF9'.
2025-6-20 13:12:02 - Updating membership #34505. New data: array (
  'status' => 'cancelled',
  'cancellation_date' => '2025-06-20 13:12:02',
).
2025-6-20 13:12:02 - Updating recurring status for membership #34505. Customer ID: 25786; Previous: true; New: false
2025-6-20 13:12:02 - Updating membership #34505. New data: array (
  'auto_renew' => 0,
).
2025-6-20 13:12:02 - Payment profile successfully cancelled for membership #34505.
2025-6-20 13:12:02 - Updating membership #34506. New data: array (
  'activated_date' => '2025-06-20 13:12:02',
).
2025-6-20 13:12:02 - Updating membership #34506. New data: array (
  'status' => 'active',
).
2025-6-20 13:12:02 - Removing old role subscriber, adding new role subscriber for membership #34506 (user ID #25848).
2025-6-20 13:12:02 - Active email sent to user #25848 for membership #34506.
2025-6-20 13:12:02 - Active email sent to admin(s) regarding membership #34506.
2025-6-20 13:12:02 - Payment Received email not sent to user #25848 for payment ID #134091 - message is empty or disabled.
2025-6-20 13:12:02 - Payment Received email not sent to admin(s) for payment ID #134091 - message is empty or disabled.
2025-6-20 13:12:02 - Updating membership #34506. New data: array (
  'times_billed' => 1,
).
2025-6-20 13:12:02 - Payment #134091 completed for member #25848 via Stripe gateway.
2025-6-20 13:12:04 - Stripe Gateway: Using newly calculated expiration for subscription start date: 2025-07-20 23:59:59
2025-6-20 13:12:04 - Stripe Gateway: Creating subscription with 1753049524 start date via trial_end.
2025-6-20 13:12:06 - Updating membership #34506. New data: array (
  'gateway_subscription_id' => 'sub_1Rc9Q1DrMO37mudtAXxq4P8N',
).
2025-6-20 13:12:17 - "Can cancel" status for membership #34506: true.
2025-6-20 13:12:17 - "Can cancel" status for membership #34506: true.
2025-6-20 13:12:46 - "Can cancel" status for membership #34506: true.
2025-6-20 13:12:46 - "Can cancel" status for membership #34504: true.
2025-6-20 13:12:46 - "Can cancel" status for membership #34501: false. Reason: membership not recurring.
2025-6-20 13:12:46 - "Can cancel" status for membership #34499: true.
2025-6-20 13:12:46 - "Can cancel" status for membership #34498: true.
2025-6-20 13:12:46 - "Can cancel" status for membership #34494: true.
2025-6-20 13:12:46 - "Can cancel" status for membership #34487: false. Reason: membership not recurring.
2025-6-20 13:12:46 - "Can cancel" status for membership #34486: false. Reason: membership not recurring.
2025-6-20 13:12:46 - "Can cancel" status for membership #34485: true.
2025-6-20 13:12:46 - "Can cancel" status for membership #34484: false. Reason: membership not recurring.
2025-6-20 13:13:20 - "Can cancel" status for membership #34506: true.
2025-6-20 13:13:20 - "Can cancel" status for membership #34504: true.
2025-6-20 13:13:20 - "Can cancel" status for membership #34501: false. Reason: membership not recurring.
2025-6-20 13:13:20 - "Can cancel" status for membership #34499: true.
2025-6-20 13:13:20 - "Can cancel" status for membership #34498: true.
2025-6-20 13:13:20 - "Can cancel" status for membership #34494: true.
2025-6-20 13:13:20 - "Can cancel" status for membership #34487: false. Reason: membership not recurring.
2025-6-20 13:13:20 - "Can cancel" status for membership #34486: false. Reason: membership not recurring.
2025-6-20 13:13:20 - "Can cancel" status for membership #34485: true.
2025-6-20 13:13:20 - "Can cancel" status for membership #34484: false. Reason: membership not recurring.
2025-6-20 13:13:25 - "Can cancel" status for membership #34506: true.
2025-6-20 13:13:25 - "Can cancel" status for membership #34506: true.
2025-6-20 13:14:30 - Old price per day: 0.16394250513347 (ID #7); New price per day: 0.22965092402464 (ID #9)
2025-6-20 13:14:30 - Adding 4.99 proration credits to registration for user #25848.
2025-6-20 13:14:30 - Old price per day: 0.16394250513347 (ID #7); New price per day: 0.22965092402464 (ID #9)
2025-6-20 13:14:30 - Adding 4.99 proration credits to registration for user #25848.
2025-6-20 13:14:41 - Old price per day: 0.16394250513347 (ID #7); New price per day: 0.22965092402464 (ID #9)
2025-6-20 13:14:41 - Adding 4.99 proration credits to registration for user #25848.
2025-6-20 13:14:41 - Started new registration for membership level #9 via stripe.
2025-6-20 13:14:42 - Old price per day: 0.16394250513347 (ID #7); New price per day: 0.22965092402464 (ID #9)
2025-6-20 13:14:42 - Adding 4.99 proration credits to registration for user #25848.
2025-6-20 13:14:42 - Started new registration for membership level #9 via stripe.
2025-6-20 13:14:42 - Registration type: upgrade.
2025-6-20 13:14:42 - Adding new membership. Data: array (
  'customer_id' => '25786',
  'user_id' => '25848',
  'object_id' => 9,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 2.0,
  'recurring_amount' => 6.99,
  'created_date' => '2025-06-20 13:14:42',
  'expiration_date' => '2025-07-20 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '0dccb31197e0afe31d3298db10be4043',
  'upgraded_from' => '34506',
)
2025-6-20 13:14:43 - New payment inserted. ID: 134092; User ID: 25848; Amount: 2.00; Subscription: Calculator with ChatDTC (100 Credits); Status: pending
2025-6-20 13:14:43 - Registration for user #25848 sent to gateway. Level ID: 9; Initial Amount: 2.00; Recurring Amount: 6.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34507
2025-6-20 13:14:44 - Updating membership #34507. New data: array (
  'gateway_customer_id' => 'cus_SXDaAGtRctxGdW',
).
2025-6-20 13:14:46 - Old price per day: 0.16394250513347 (ID #7); New price per day: 0.22965092402464 (ID #9)
2025-6-20 13:14:46 - Using recovered payment #134092 for registration. Transaction type: upgrade.
2025-6-20 13:14:46 - Adding 4.99 proration credits to registration for user #25848.
2025-6-20 13:14:46 - Registration for user #25848 sent to gateway. Level ID: 9; Initial Amount: 2.00; Recurring Amount: 6.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34507
2025-6-20 13:14:48 - Updating payment #134092 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3Rc9SaDrMO37mudt4xDx8J6M',
  'status' => 'complete',
)
2025-6-20 13:14:48 - Completing registration for customer #25786 via payment #134092.
2025-6-20 13:14:48 - Activating membership #34507.
2025-6-20 13:14:48 - Disabling all memberships for customer #25786 except: '34507'.
2025-6-20 13:14:48 - Disabling membership #34506.
2025-6-20 13:14:48 - Updating membership #34506. New data: array (
  'disabled' => 1,
).
2025-6-20 13:14:48 - "Can cancel" status for membership #34506: true.
2025-6-20 13:14:48 - "Can cancel" status for membership #34506: true.
2025-6-20 13:14:49 - Failed to cancel Stripe payment profile sub_1Rc9Q1DrMO37mudtAXxq4P8N. Error code: resource_missing; Error Message: No such subscription: 'sub_1Rc9Q1DrMO37mudtAXxq4P8N'.
2025-6-20 13:14:49 - Updating membership #34506. New data: array (
  'status' => 'cancelled',
  'cancellation_date' => '2025-06-20 13:14:49',
).
2025-6-20 13:14:49 - Updating recurring status for membership #34506. Customer ID: 25786; Previous: true; New: false
2025-6-20 13:14:49 - Updating membership #34506. New data: array (
  'auto_renew' => 0,
).
2025-6-20 13:14:49 - Payment profile successfully cancelled for membership #34506.
2025-6-20 13:14:49 - Updating membership #34507. New data: array (
  'activated_date' => '2025-06-20 13:14:49',
).
2025-6-20 13:14:49 - Updating membership #34507. New data: array (
  'status' => 'active',
).
2025-6-20 13:14:49 - Removing old role subscriber, adding new role subscriber for membership #34507 (user ID #25848).
2025-6-20 13:14:49 - Active email sent to user #25848 for membership #34507.
2025-6-20 13:14:49 - Active email sent to admin(s) regarding membership #34507.
2025-6-20 13:14:49 - Payment Received email not sent to user #25848 for payment ID #134092 - message is empty or disabled.
2025-6-20 13:14:49 - Payment Received email not sent to admin(s) for payment ID #134092 - message is empty or disabled.
2025-6-20 13:14:49 - Updating membership #34507. New data: array (
  'times_billed' => 1,
).
2025-6-20 13:14:49 - Payment #134092 completed for member #25848 via Stripe gateway.
2025-6-20 13:14:51 - Stripe Gateway: Using newly calculated expiration for subscription start date: 2025-07-20 23:59:59
2025-6-20 13:14:51 - Stripe Gateway: Creating subscription with 1753049691 start date via trial_end.
2025-6-20 13:14:52 - Updating membership #34507. New data: array (
  'gateway_subscription_id' => 'sub_1Rc9ShDrMO37mudtYn33cRN9',
).
2025-6-20 13:15:04 - "Can cancel" status for membership #34507: true.
2025-6-20 13:15:04 - "Can cancel" status for membership #34507: true.
2025-6-20 13:16:28 - Adding 6.99 proration credits to registration for user #25848.
2025-6-20 13:16:29 - Old price per day: 0.22965092402464 (ID #9); New price per day: 0.3282135523614 (ID #12)
2025-6-20 13:16:29 - Adding 6.99 proration credits to registration for user #25848.
2025-6-20 13:16:29 - Old price per day: 0.22965092402464 (ID #9); New price per day: 0.3282135523614 (ID #12)
2025-6-20 13:16:29 - Adding 6.99 proration credits to registration for user #25848.
2025-6-20 13:17:41 - Old price per day: 0.22965092402464 (ID #9); New price per day: 0.3282135523614 (ID #12)
2025-6-20 13:17:41 - Adding 6.99 proration credits to registration for user #25848.
2025-6-20 13:17:41 - Old price per day: 0.22965092402464 (ID #9); New price per day: 0.3282135523614 (ID #12)
2025-6-20 13:17:41 - Adding 6.99 proration credits to registration for user #25848.
2025-6-20 13:17:48 - Old price per day: 0.22965092402464 (ID #9); New price per day: 0.3282135523614 (ID #12)
2025-6-20 13:17:48 - Adding 6.99 proration credits to registration for user #25848.
2025-6-20 13:17:48 - Started new registration for membership level #12 via stripe.
2025-6-20 13:17:48 - Old price per day: 0.22965092402464 (ID #9); New price per day: 0.3282135523614 (ID #12)
2025-6-20 13:17:48 - Adding 6.99 proration credits to registration for user #25848.
2025-6-20 13:17:48 - Started new registration for membership level #12 via stripe.
2025-6-20 13:17:48 - Registration type: upgrade.
2025-6-20 13:17:48 - Adding new membership. Data: array (
  'customer_id' => '25786',
  'user_id' => '25848',
  'object_id' => 12,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 3.0,
  'recurring_amount' => 9.99,
  'created_date' => '2025-06-20 13:17:48',
  'expiration_date' => '2025-07-20 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => 'aa2494a3d958144d788e844f914318fd',
  'upgraded_from' => '34507',
)
2025-6-20 13:17:49 - New payment inserted. ID: 134093; User ID: 25848; Amount: 3.00; Subscription: Calculator with ChatDTC (Unlimited Credits); Status: pending
2025-6-20 13:17:49 - Registration for user #25848 sent to gateway. Level ID: 12; Initial Amount: 3.00; Recurring Amount: 9.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34508
2025-6-20 13:17:50 - Updating membership #34508. New data: array (
  'gateway_customer_id' => 'cus_SXDaAGtRctxGdW',
).
2025-6-20 13:17:52 - Old price per day: 0.22965092402464 (ID #9); New price per day: 0.3282135523614 (ID #12)
2025-6-20 13:17:52 - Using recovered payment #134093 for registration. Transaction type: upgrade.
2025-6-20 13:17:52 - Adding 6.99 proration credits to registration for user #25848.
2025-6-20 13:17:53 - Registration for user #25848 sent to gateway. Level ID: 12; Initial Amount: 3.00; Recurring Amount: 9.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34508
2025-6-20 13:17:54 - Updating payment #134093 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3Rc9VaDrMO37mudt28ehr8SH',
  'status' => 'complete',
)
2025-6-20 13:17:54 - Completing registration for customer #25786 via payment #134093.
2025-6-20 13:17:54 - Activating membership #34508.
2025-6-20 13:17:54 - Disabling all memberships for customer #25786 except: '34508'.
2025-6-20 13:17:54 - Disabling membership #34507.
2025-6-20 13:17:54 - Updating membership #34507. New data: array (
  'disabled' => 1,
).
2025-6-20 13:17:54 - "Can cancel" status for membership #34507: true.
2025-6-20 13:17:54 - "Can cancel" status for membership #34507: true.
2025-6-20 13:17:55 - Failed to cancel Stripe payment profile sub_1Rc9ShDrMO37mudtYn33cRN9. Error code: resource_missing; Error Message: No such subscription: 'sub_1Rc9ShDrMO37mudtYn33cRN9'.
2025-6-20 13:17:55 - Updating membership #34507. New data: array (
  'status' => 'cancelled',
  'cancellation_date' => '2025-06-20 13:17:55',
).
2025-6-20 13:17:55 - Updating recurring status for membership #34507. Customer ID: 25786; Previous: true; New: false
2025-6-20 13:17:55 - Updating membership #34507. New data: array (
  'auto_renew' => 0,
).
2025-6-20 13:17:55 - Payment profile successfully cancelled for membership #34507.
2025-6-20 13:17:56 - Updating membership #34508. New data: array (
  'activated_date' => '2025-06-20 13:17:56',
).
2025-6-20 13:17:56 - Updating membership #34508. New data: array (
  'status' => 'active',
).
2025-6-20 13:17:56 - Removing old role subscriber, adding new role subscriber for membership #34508 (user ID #25848).
2025-6-20 13:17:56 - Active email sent to user #25848 for membership #34508.
2025-6-20 13:17:56 - Active email sent to admin(s) regarding membership #34508.
2025-6-20 13:17:56 - Payment Received email not sent to user #25848 for payment ID #134093 - message is empty or disabled.
2025-6-20 13:17:56 - Payment Received email not sent to admin(s) for payment ID #134093 - message is empty or disabled.
2025-6-20 13:17:56 - Updating membership #34508. New data: array (
  'times_billed' => 1,
).
2025-6-20 13:17:56 - Payment #134093 completed for member #25848 via Stripe gateway.
2025-6-20 13:17:57 - Stripe Gateway: Using newly calculated expiration for subscription start date: 2025-07-20 23:59:59
2025-6-20 13:17:57 - Stripe Gateway: Creating subscription with 1753049877 start date via trial_end.
2025-6-20 13:17:58 - Updating membership #34508. New data: array (
  'gateway_subscription_id' => 'sub_1Rc9ViDrMO37mudt1KmMrvgS',
).
2025-6-20 13:18:11 - "Can cancel" status for membership #34508: true.
2025-6-20 13:18:11 - "Can cancel" status for membership #34508: true.
2025-6-20 13:19:04 - Adding 9.99 proration credits to registration for user #25848.
2025-6-20 13:19:06 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.22965092402464 (ID #9)
2025-6-20 13:19:06 - Adding 9.99 proration credits to registration for user #25848.
2025-6-20 13:19:06 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.22965092402464 (ID #9)
2025-6-20 13:19:06 - Adding 9.99 proration credits to registration for user #25848.
2025-6-20 13:19:10 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.082108145106092 (ID #11)
2025-6-20 13:19:10 - Adding 9.99 proration credits to registration for user #25848.
2025-6-20 13:19:10 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.082108145106092 (ID #11)
2025-6-20 13:19:10 - Adding 9.99 proration credits to registration for user #25848.
2025-6-20 13:19:34 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.082108145106092 (ID #11)
2025-6-20 13:19:34 - Adding 9.99 proration credits to registration for user #25848.
2025-6-20 13:19:34 - Started new registration for membership level #11 via stripe.
2025-6-20 13:19:34 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.082108145106092 (ID #11)
2025-6-20 13:19:34 - Adding 9.99 proration credits to registration for user #25848.
2025-6-20 13:19:35 - Started new registration for membership level #11 via stripe.
2025-6-20 13:19:35 - Registration type: downgrade.
2025-6-20 13:19:35 - Adding new membership. Data: array (
  'customer_id' => '25786',
  'user_id' => '25848',
  'object_id' => 11,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 20.0,
  'recurring_amount' => 29.99,
  'created_date' => '2025-06-20 13:19:35',
  'expiration_date' => '2026-06-20 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '********************************',
  'upgraded_from' => '34508',
)
2025-6-20 13:19:35 - New payment inserted. ID: 134094; User ID: 25848; Amount: 20.00; Subscription: Dynasty Trade Calculator (Yearly); Status: pending
2025-6-20 13:19:36 - Registration for user #25848 sent to gateway. Level ID: 11; Initial Amount: 20.00; Recurring Amount: 29.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34509
2025-6-20 13:19:36 - Updating membership #34509. New data: array (
  'gateway_customer_id' => 'cus_SXDaAGtRctxGdW',
).
2025-6-20 13:19:39 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.082108145106092 (ID #11)
2025-6-20 13:19:39 - Using recovered payment #134094 for registration. Transaction type: downgrade.
2025-6-20 13:19:39 - Adding 9.99 proration credits to registration for user #25848.
2025-6-20 13:19:39 - Registration for user #25848 sent to gateway. Level ID: 11; Initial Amount: 20.00; Recurring Amount: 29.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34509
2025-6-20 13:19:40 - Updating payment #134094 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3Rc9XJDrMO37mudt4AhIqa28',
  'status' => 'complete',
)
2025-6-20 13:19:40 - Completing registration for customer #25786 via payment #134094.
2025-6-20 13:19:40 - Activating membership #34509.
2025-6-20 13:19:40 - Disabling all memberships for customer #25786 except: '34509'.
2025-6-20 13:19:40 - Disabling membership #34508.
2025-6-20 13:19:40 - Updating membership #34508. New data: array (
  'disabled' => 1,
).
2025-6-20 13:19:40 - "Can cancel" status for membership #34508: true.
2025-6-20 13:19:40 - "Can cancel" status for membership #34508: true.
2025-6-20 13:19:42 - Failed to cancel Stripe payment profile sub_1Rc9ViDrMO37mudt1KmMrvgS. Error code: resource_missing; Error Message: No such subscription: 'sub_1Rc9ViDrMO37mudt1KmMrvgS'.
2025-6-20 13:19:42 - Updating membership #34508. New data: array (
  'status' => 'cancelled',
  'cancellation_date' => '2025-06-20 13:19:42',
).
2025-6-20 13:19:42 - Updating recurring status for membership #34508. Customer ID: 25786; Previous: true; New: false
2025-6-20 13:19:42 - Updating membership #34508. New data: array (
  'auto_renew' => 0,
).
2025-6-20 13:19:42 - Payment profile successfully cancelled for membership #34508.
2025-6-20 13:19:42 - Updating membership #34509. New data: array (
  'activated_date' => '2025-06-20 13:19:42',
).
2025-6-20 13:19:42 - Updating membership #34509. New data: array (
  'status' => 'active',
).
2025-6-20 13:19:42 - Removing old role subscriber, adding new role subscriber for membership #34509 (user ID #25848).
2025-6-20 13:19:42 - Active email sent to user #25848 for membership #34509.
2025-6-20 13:19:42 - Active email sent to admin(s) regarding membership #34509.
2025-6-20 13:19:42 - Payment Received email not sent to user #25848 for payment ID #134094 - message is empty or disabled.
2025-6-20 13:19:42 - Payment Received email not sent to admin(s) for payment ID #134094 - message is empty or disabled.
2025-6-20 13:19:42 - Updating membership #34509. New data: array (
  'times_billed' => 1,
).
2025-6-20 13:19:42 - Payment #134094 completed for member #25848 via Stripe gateway.
2025-6-20 13:19:44 - Stripe Gateway: Using newly calculated expiration for subscription start date: 2026-06-20 23:59:59
2025-6-20 13:19:44 - Stripe Gateway: Creating subscription with 1781993984 start date via trial_end.
2025-6-20 13:19:45 - Updating membership #34509. New data: array (
  'gateway_subscription_id' => 'sub_1Rc9XQDrMO37mudtRCxmJIU5',
).
2025-6-20 13:19:57 - "Can cancel" status for membership #34509: true.
2025-6-20 13:19:57 - "Can cancel" status for membership #34509: true.
2025-6-20 13:31:36 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.3282135523614 (ID #12)
2025-6-20 13:31:36 - Adding 29.99 proration credits to registration for user #25848.
2025-6-20 13:31:37 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.3282135523614 (ID #12)
2025-6-20 13:31:37 - Adding 29.99 proration credits to registration for user #25848.
2025-6-20 13:32:05 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.3282135523614 (ID #12)
2025-6-20 13:32:05 - Adding 29.99 proration credits to registration for user #25848.
2025-6-20 13:32:05 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.3282135523614 (ID #12)
2025-6-20 13:32:05 - Adding 29.99 proration credits to registration for user #25848.
2025-6-20 13:32:59 - "Can cancel" status for membership #34509: true.
2025-6-20 13:32:59 - "Can cancel" status for membership #34509: true.
2025-6-20 14:20:48 - Starting rcp_check_for_expired_users() cron job.
2025-6-20 14:20:48 - No expired memberships found.
2025-6-20 14:20:48 - Starting rcp_check_for_soon_to_expire_users() cron job.
2025-6-20 14:20:48 - Processing expiration reminder. ID: 0; Period: -1month; Levels: 1, 3, 2.
2025-6-20 14:20:48 - Reminder is not enabled - exiting.
2025-6-20 14:51:46 - Adding 29.99 proration credits to registration for user #25848.
2025-6-20 14:51:47 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.3282135523614 (ID #12)
2025-6-20 14:51:47 - Adding 29.99 proration credits to registration for user #25848.
2025-6-20 14:51:48 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.3282135523614 (ID #12)
2025-6-20 14:51:48 - Adding 29.99 proration credits to registration for user #25848.
2025-6-20 14:51:56 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.3282135523614 (ID #12)
2025-6-20 14:51:56 - Adding 29.99 proration credits to registration for user #25848.
2025-6-20 14:51:56 - Started new registration for membership level #12 via stripe.
2025-6-20 14:51:56 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.3282135523614 (ID #12)
2025-6-20 14:51:56 - Adding 29.99 proration credits to registration for user #25848.
2025-6-20 14:51:56 - Started new registration for membership level #12 via stripe.
2025-6-20 14:51:56 - Registration type: upgrade.
2025-6-20 14:51:56 - Adding new membership. Data: array (
  'customer_id' => '25786',
  'user_id' => '25848',
  'object_id' => 12,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => '0.00',
  'recurring_amount' => 9.99,
  'created_date' => '2025-06-20 14:51:56',
  'expiration_date' => '2025-10-20 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => 'f614cece3c2f54941ac771e82ee3546d',
  'upgraded_from' => '34509',
)
2025-6-20 14:51:57 - New payment inserted. ID: 134095; User ID: 25848; Amount: 0.00; Subscription: Calculator with ChatDTC (Unlimited Credits); Status: pending
2025-6-20 14:51:58 - Registration for user #25848 sent to gateway. Level ID: 12; Initial Amount: 0.00; Recurring Amount: 9.99; Auto Renew: true; Trial: false; Subscription Start: 2025-10-20 14:51:58; Membership ID: 34510
2025-6-20 14:51:58 - Updating membership #34510. New data: array (
  'gateway_customer_id' => 'cus_SXDaAGtRctxGdW',
).
2025-6-20 14:52:00 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.3282135523614 (ID #12)
2025-6-20 14:52:00 - Using recovered payment #134095 for registration. Transaction type: upgrade.
2025-6-20 14:52:00 - Adding 29.99 proration credits to registration for user #25848.
2025-6-20 14:52:00 - Registration for user #25848 sent to gateway. Level ID: 12; Initial Amount: 0.00; Recurring Amount: 9.99; Auto Renew: true; Trial: false; Subscription Start: 2025-10-20 14:52:00; Membership ID: 34510
2025-6-20 14:52:01 - Updating payment #134095 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => '',
  'status' => 'complete',
)
2025-6-20 14:52:01 - Completing registration for customer #25786 via payment #134095.
2025-6-20 14:52:01 - Activating membership #34510.
2025-6-20 14:52:01 - Disabling all memberships for customer #25786 except: '34510'.
2025-6-20 14:52:01 - Disabling membership #34509.
2025-6-20 14:52:01 - Updating membership #34509. New data: array (
  'disabled' => 1,
).
2025-6-20 14:52:01 - "Can cancel" status for membership #34509: true.
2025-6-20 14:52:01 - "Can cancel" status for membership #34509: true.
2025-6-20 14:52:03 - Failed to cancel Stripe payment profile sub_1Rc9XQDrMO37mudtRCxmJIU5. Error code: resource_missing; Error Message: No such subscription: 'sub_1Rc9XQDrMO37mudtRCxmJIU5'.
2025-6-20 14:52:03 - Updating membership #34509. New data: array (
  'status' => 'cancelled',
  'cancellation_date' => '2025-06-20 14:52:03',
).
2025-6-20 14:52:03 - Updating recurring status for membership #34509. Customer ID: 25786; Previous: true; New: false
2025-6-20 14:52:03 - Updating membership #34509. New data: array (
  'auto_renew' => 0,
).
2025-6-20 14:52:03 - Payment profile successfully cancelled for membership #34509.
2025-6-20 14:52:03 - Updating membership #34510. New data: array (
  'activated_date' => '2025-06-20 14:52:03',
).
2025-6-20 14:52:03 - Updating membership #34510. New data: array (
  'status' => 'active',
).
2025-6-20 14:52:03 - Removing old role subscriber, adding new role subscriber for membership #34510 (user ID #25848).
2025-6-20 14:52:03 - Active email sent to user #25848 for membership #34510.
2025-6-20 14:52:03 - Active email sent to admin(s) regarding membership #34510.
2025-6-20 14:52:03 - Payment Received email not sent to user #25848 - payment amount is 0.
2025-6-20 14:52:03 - Updating membership #34510. New data: array (
  'times_billed' => 1,
).
2025-6-20 14:52:03 - Payment #134095 completed for member #25848 via Stripe gateway.
2025-6-20 14:52:05 - Stripe Gateway: Using subscription start date for subscription: 2025-10-20 14:52:00
2025-6-20 14:52:05 - Stripe Gateway: Creating subscription with 1760971920 start date via trial_end.
2025-6-20 14:52:06 - Updating membership #34510. New data: array (
  'gateway_subscription_id' => 'sub_1RcAynDrMO37mudt8dsU56Hh',
).
2025-6-20 14:52:06 - Updating payment #134095 with new data: array (
  'transaction_id' => 'sub_1RcAynDrMO37mudt8dsU56Hh',
)
2025-6-20 14:52:17 - "Can cancel" status for membership #34510: true.
2025-6-20 14:52:17 - "Can cancel" status for membership #34510: true.
2025-6-20 14:52:27 - Adding 9.99 proration credits to registration for user #25848.
2025-6-20 14:52:28 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.22965092402464 (ID #9)
2025-6-20 14:52:28 - Adding 9.99 proration credits to registration for user #25848.
2025-6-20 14:52:28 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.22965092402464 (ID #9)
2025-6-20 14:52:28 - Adding 9.99 proration credits to registration for user #25848.
2025-6-20 14:52:32 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.082108145106092 (ID #11)
2025-6-20 14:52:32 - Adding 9.99 proration credits to registration for user #25848.
2025-6-20 14:52:32 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.082108145106092 (ID #11)
2025-6-20 14:52:32 - Adding 9.99 proration credits to registration for user #25848.
2025-6-20 14:52:38 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.082108145106092 (ID #11)
2025-6-20 14:52:38 - Adding 9.99 proration credits to registration for user #25848.
2025-6-20 14:52:38 - Started new registration for membership level #11 via stripe.
2025-6-20 14:52:39 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.082108145106092 (ID #11)
2025-6-20 14:52:39 - Adding 9.99 proration credits to registration for user #25848.
2025-6-20 14:52:39 - Started new registration for membership level #11 via stripe.
2025-6-20 14:52:39 - Registration type: downgrade.
2025-6-20 14:52:39 - Adding new membership. Data: array (
  'customer_id' => '25786',
  'user_id' => '25848',
  'object_id' => 11,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 20.0,
  'recurring_amount' => 29.99,
  'created_date' => '2025-06-20 14:52:39',
  'expiration_date' => '2026-06-20 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '1b5e63cfb1e07c83e8f6014b1b78a2e2',
  'upgraded_from' => '34510',
)
2025-6-20 14:52:39 - Updating membership #34510. New data: array (
  'status' => 'active',
).
2025-6-20 14:52:39 - Updating membership #34511. New data: array (
  'status' => 'pending',
  'created_date' => 'October 20, 2025',
  'activated_date' => 'October 20, 2025',
).
2025-6-20 14:52:40 - New payment inserted. ID: 134096; User ID: 25848; Amount: 20.00; Subscription: Dynasty Trade Calculator (Yearly); Status: pending
2025-6-20 14:52:40 - Registration for user #25848 sent to gateway. Level ID: 11; Initial Amount: 20.00; Recurring Amount: 29.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34511
2025-6-20 14:52:41 - Updating membership #34511. New data: array (
  'gateway_customer_id' => 'cus_SXDaAGtRctxGdW',
).
2025-6-20 14:52:43 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.082108145106092 (ID #11)
2025-6-20 14:52:43 - Using recovered payment #134096 for registration. Transaction type: downgrade.
2025-6-20 14:52:43 - Adding 9.99 proration credits to registration for user #25848.
2025-6-20 14:52:43 - Registration for user #25848 sent to gateway. Level ID: 11; Initial Amount: 20.00; Recurring Amount: 29.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34511
2025-6-20 14:52:44 - Updating payment #134096 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3RcAzNDrMO37mudt2resbtxP',
  'status' => 'complete',
)
2025-6-20 14:52:44 - Completing registration for customer #25786 via payment #134096.
2025-6-20 14:52:44 - Activating membership #34511.
2025-6-20 14:52:44 - Disabling all memberships for customer #25786 except: '34511'.
2025-6-20 14:52:44 - Disabling membership #34510.
2025-6-20 14:52:44 - Updating membership #34510. New data: array (
  'disabled' => 1,
).
2025-6-20 14:52:44 - "Can cancel" status for membership #34510: true.
2025-6-20 14:52:44 - "Can cancel" status for membership #34510: true.
2025-6-20 14:52:46 - Failed to cancel Stripe payment profile sub_1RcAynDrMO37mudt8dsU56Hh. Error code: resource_missing; Error Message: No such subscription: 'sub_1RcAynDrMO37mudt8dsU56Hh'.
2025-6-20 14:52:46 - Updating membership #34510. New data: array (
  'status' => 'cancelled',
  'cancellation_date' => '2025-06-20 14:52:46',
).
2025-6-20 14:52:46 - Updating recurring status for membership #34510. Customer ID: 25786; Previous: true; New: false
2025-6-20 14:52:46 - Updating membership #34510. New data: array (
  'auto_renew' => 0,
).
2025-6-20 14:52:46 - Payment profile successfully cancelled for membership #34510.
2025-6-20 14:52:46 - Updating membership #34511. New data: array (
  'status' => 'active',
).
2025-6-20 14:52:46 - Removing old role subscriber, adding new role subscriber for membership #34511 (user ID #25848).
2025-6-20 14:52:46 - Active email sent to user #25848 for membership #34511.
2025-6-20 14:52:46 - Active email sent to admin(s) regarding membership #34511.
2025-6-20 14:52:46 - Payment Received email not sent to user #25848 for payment ID #134096 - message is empty or disabled.
2025-6-20 14:52:46 - Payment Received email not sent to admin(s) for payment ID #134096 - message is empty or disabled.
2025-6-20 14:52:46 - Updating membership #34511. New data: array (
  'times_billed' => 1,
).
2025-6-20 14:52:46 - Payment #134096 completed for member #25848 via Stripe gateway.
2025-6-20 14:52:48 - Stripe Gateway: Using newly calculated expiration for subscription start date: 2026-06-20 23:59:59
2025-6-20 14:52:48 - Stripe Gateway: Creating subscription with 1781999568 start date via trial_end.
2025-6-20 14:52:49 - Updating membership #34511. New data: array (
  'gateway_subscription_id' => 'sub_1RcAzUDrMO37mudt0ot4iei7',
).
2025-6-20 14:53:00 - "Can cancel" status for membership #34511: true.
2025-6-20 14:53:00 - "Can cancel" status for membership #34511: true.
2025-6-20 14:53:53 - Adding 29.99 proration credits to registration for user #25848.
2025-6-20 14:53:54 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.3282135523614 (ID #12)
2025-6-20 14:53:54 - Adding 29.99 proration credits to registration for user #25848.
2025-6-20 14:53:54 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.3282135523614 (ID #12)
2025-6-20 14:53:54 - Adding 29.99 proration credits to registration for user #25848.
2025-6-20 14:53:58 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.3282135523614 (ID #12)
2025-6-20 14:53:58 - Adding 29.99 proration credits to registration for user #25848.
2025-6-20 14:53:58 - Started new registration for membership level #12 via stripe.
2025-6-20 14:53:59 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.3282135523614 (ID #12)
2025-6-20 14:53:59 - Adding 29.99 proration credits to registration for user #25848.
2025-6-20 14:53:59 - Started new registration for membership level #12 via stripe.
2025-6-20 14:53:59 - Registration type: upgrade.
2025-6-20 14:53:59 - Adding new membership. Data: array (
  'customer_id' => '25786',
  'user_id' => '25848',
  'object_id' => 12,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => '0.00',
  'recurring_amount' => 9.99,
  'created_date' => '2025-06-20 14:53:59',
  'expiration_date' => '2025-10-20 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '19744edd38b99ba1e8f79fee6a6e10ca',
  'upgraded_from' => '34511',
)
2025-6-20 14:54:00 - New payment inserted. ID: 134097; User ID: 25848; Amount: 0.00; Subscription: Calculator with ChatDTC (Unlimited Credits); Status: pending
2025-6-20 14:54:00 - Registration for user #25848 sent to gateway. Level ID: 12; Initial Amount: 0.00; Recurring Amount: 9.99; Auto Renew: true; Trial: false; Subscription Start: 2025-10-20 14:54:00; Membership ID: 34512
2025-6-20 14:54:00 - Updating membership #34512. New data: array (
  'gateway_customer_id' => 'cus_SXDaAGtRctxGdW',
).
2025-6-20 14:54:02 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.3282135523614 (ID #12)
2025-6-20 14:54:02 - Using recovered payment #134097 for registration. Transaction type: upgrade.
2025-6-20 14:54:02 - Adding 29.99 proration credits to registration for user #25848.
2025-6-20 14:54:02 - Registration for user #25848 sent to gateway. Level ID: 12; Initial Amount: 0.00; Recurring Amount: 9.99; Auto Renew: true; Trial: false; Subscription Start: 2025-10-20 14:54:02; Membership ID: 34512
2025-6-20 14:54:04 - Updating payment #134097 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => '',
  'status' => 'complete',
)
2025-6-20 14:54:04 - Completing registration for customer #25786 via payment #134097.
2025-6-20 14:54:04 - Activating membership #34512.
2025-6-20 14:54:04 - Disabling all memberships for customer #25786 except: '34512'.
2025-6-20 14:54:04 - Disabling membership #34511.
2025-6-20 14:54:04 - Updating membership #34511. New data: array (
  'disabled' => 1,
).
2025-6-20 14:54:04 - "Can cancel" status for membership #34511: true.
2025-6-20 14:54:04 - "Can cancel" status for membership #34511: true.
2025-6-20 14:54:05 - Failed to cancel Stripe payment profile sub_1RcAzUDrMO37mudt0ot4iei7. Error code: resource_missing; Error Message: No such subscription: 'sub_1RcAzUDrMO37mudt0ot4iei7'.
2025-6-20 14:54:05 - Updating membership #34511. New data: array (
  'status' => 'cancelled',
  'cancellation_date' => '2025-06-20 14:54:05',
).
2025-6-20 14:54:05 - Updating recurring status for membership #34511. Customer ID: 25786; Previous: true; New: false
2025-6-20 14:54:05 - Updating membership #34511. New data: array (
  'auto_renew' => 0,
).
2025-6-20 14:54:05 - Payment profile successfully cancelled for membership #34511.
2025-6-20 14:54:05 - Updating membership #34512. New data: array (
  'activated_date' => '2025-06-20 14:54:05',
).
2025-6-20 14:54:05 - Updating membership #34512. New data: array (
  'status' => 'active',
).
2025-6-20 14:54:06 - Removing old role subscriber, adding new role subscriber for membership #34512 (user ID #25848).
2025-6-20 14:54:06 - Active email sent to user #25848 for membership #34512.
2025-6-20 14:54:06 - Active email sent to admin(s) regarding membership #34512.
2025-6-20 14:54:06 - Payment Received email not sent to user #25848 - payment amount is 0.
2025-6-20 14:54:06 - Updating membership #34512. New data: array (
  'times_billed' => 1,
).
2025-6-20 14:54:06 - Payment #134097 completed for member #25848 via Stripe gateway.
2025-6-20 14:54:07 - Stripe Gateway: Using subscription start date for subscription: 2025-10-20 14:54:02
2025-6-20 14:54:07 - Stripe Gateway: Creating subscription with 1760972042 start date via trial_end.
2025-6-20 14:54:08 - Updating membership #34512. New data: array (
  'gateway_subscription_id' => 'sub_1RcB0lDrMO37mudtDjQBniTF',
).
2025-6-20 14:54:09 - Updating payment #134097 with new data: array (
  'transaction_id' => 'sub_1RcB0lDrMO37mudtDjQBniTF',
)
2025-6-20 14:54:19 - "Can cancel" status for membership #34512: true.
2025-6-20 14:54:19 - "Can cancel" status for membership #34512: true.
2025-6-20 14:54:26 - Adding 9.99 proration credits to registration for user #25848.
2025-6-20 14:54:27 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.22965092402464 (ID #9)
2025-6-20 14:54:27 - Adding 9.99 proration credits to registration for user #25848.
2025-6-20 14:54:27 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.22965092402464 (ID #9)
2025-6-20 14:54:27 - Adding 9.99 proration credits to registration for user #25848.
2025-6-20 14:54:31 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.082108145106092 (ID #11)
2025-6-20 14:54:31 - Adding 9.99 proration credits to registration for user #25848.
2025-6-20 14:54:31 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.082108145106092 (ID #11)
2025-6-20 14:54:31 - Adding 9.99 proration credits to registration for user #25848.
2025-6-20 14:54:53 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.082108145106092 (ID #11)
2025-6-20 14:54:53 - Adding 9.99 proration credits to registration for user #25848.
2025-6-20 14:54:54 - Started new registration for membership level #11 via stripe.
2025-6-20 14:54:54 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.082108145106092 (ID #11)
2025-6-20 14:54:54 - Adding 9.99 proration credits to registration for user #25848.
2025-6-20 14:54:54 - Started new registration for membership level #11 via stripe.
2025-6-20 14:54:54 - Registration type: downgrade.
2025-6-20 14:54:54 - Adding new membership. Data: array (
  'customer_id' => '25786',
  'user_id' => '25848',
  'object_id' => 11,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 20.0,
  'recurring_amount' => 29.99,
  'created_date' => '2025-06-20 14:54:54',
  'expiration_date' => '2026-06-20 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '85599eeafdd3de4e13fdad66d24635ef',
  'upgraded_from' => '34512',
)
2025-6-20 14:54:54 - Updating membership #34512. New data: array (
  'status' => 'active',
).
2025-6-20 14:54:54 - Updating membership #34513. New data: array (
  'status' => 'pending',
  'created_date' => 'October 20, 2025',
  'activated_date' => 'October 20, 2025',
).
2025-6-20 14:54:55 - New payment inserted. ID: 134098; User ID: 25848; Amount: 20.00; Subscription: Dynasty Trade Calculator (Yearly); Status: pending
2025-6-20 14:54:55 - Registration for user #25848 sent to gateway. Level ID: 11; Initial Amount: 20.00; Recurring Amount: 29.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34513
2025-6-20 14:54:56 - Updating membership #34513. New data: array (
  'gateway_customer_id' => 'cus_SXDaAGtRctxGdW',
).
2025-6-20 14:54:58 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.082108145106092 (ID #11)
2025-6-20 14:54:58 - Using recovered payment #134098 for registration. Transaction type: downgrade.
2025-6-20 14:54:58 - Adding 9.99 proration credits to registration for user #25848.
2025-6-20 14:54:58 - Registration for user #25848 sent to gateway. Level ID: 11; Initial Amount: 20.00; Recurring Amount: 29.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34513
2025-6-20 14:55:00 - Updating payment #134098 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3RcB1YDrMO37mudt0DqR9UVE',
  'status' => 'complete',
)
2025-6-20 14:55:00 - Completing registration for customer #25786 via payment #134098.
2025-6-20 14:55:00 - Activating membership #34513.
2025-6-20 14:55:00 - Disabling all memberships for customer #25786 except: '34513'.
2025-6-20 14:55:00 - Disabling membership #34512.
2025-6-20 14:55:00 - Updating membership #34512. New data: array (
  'disabled' => 1,
).
2025-6-20 14:55:00 - "Can cancel" status for membership #34512: true.
2025-6-20 14:55:00 - "Can cancel" status for membership #34512: true.
2025-6-20 14:55:01 - Failed to cancel Stripe payment profile sub_1RcB0lDrMO37mudtDjQBniTF. Error code: resource_missing; Error Message: No such subscription: 'sub_1RcB0lDrMO37mudtDjQBniTF'.
2025-6-20 14:55:01 - Updating membership #34512. New data: array (
  'status' => 'cancelled',
  'cancellation_date' => '2025-06-20 14:55:01',
).
2025-6-20 14:55:01 - Updating recurring status for membership #34512. Customer ID: 25786; Previous: true; New: false
2025-6-20 14:55:01 - Updating membership #34512. New data: array (
  'auto_renew' => 0,
).
2025-6-20 14:55:01 - Payment profile successfully cancelled for membership #34512.
2025-6-20 14:55:01 - Updating membership #34513. New data: array (
  'status' => 'active',
).
2025-6-20 14:55:01 - Removing old role subscriber, adding new role subscriber for membership #34513 (user ID #25848).
2025-6-20 14:55:01 - Active email sent to user #25848 for membership #34513.
2025-6-20 14:55:01 - Active email sent to admin(s) regarding membership #34513.
2025-6-20 14:55:01 - Payment Received email not sent to user #25848 for payment ID #134098 - message is empty or disabled.
2025-6-20 14:55:01 - Payment Received email not sent to admin(s) for payment ID #134098 - message is empty or disabled.
2025-6-20 14:55:01 - Updating membership #34513. New data: array (
  'times_billed' => 1,
).
2025-6-20 14:55:01 - Payment #134098 completed for member #25848 via Stripe gateway.
2025-6-20 14:55:03 - Stripe Gateway: Using newly calculated expiration for subscription start date: 2026-06-20 23:59:59
2025-6-20 14:55:03 - Stripe Gateway: Creating subscription with 1781999703 start date via trial_end.
2025-6-20 14:55:04 - Updating membership #34513. New data: array (
  'gateway_subscription_id' => 'sub_1RcB1fDrMO37mudtCjTsqsXi',
).
2025-6-20 14:55:51 - "Can cancel" status for membership #34513: true.
2025-6-20 14:55:51 - "Can cancel" status for membership #34513: true.
2025-6-20 14:56:06 - Adding 29.99 proration credits to registration for user #25848.
2025-6-20 14:56:07 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.3282135523614 (ID #12)
2025-6-20 14:56:07 - Adding 29.99 proration credits to registration for user #25848.
2025-6-20 14:56:07 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.3282135523614 (ID #12)
2025-6-20 14:56:07 - Adding 29.99 proration credits to registration for user #25848.
2025-6-20 14:56:17 - "Can cancel" status for membership #34513: true.
2025-6-20 14:56:17 - "Can cancel" status for membership #34513: true.
2025-6-21 05:57:34 - Starting rcp_check_member_counts() cron job.
2025-6-21 05:57:34 - Starting rcp_mark_abandoned_payments() cron job.
2025-6-21 07:10:36 - Started new registration for membership level #12 via stripe.
2025-6-21 07:10:37 - Started new registration for membership level #12 via stripe.
2025-6-21 07:10:37 - Adding a new customer. Args: array (
  'date_registered' => '2025-06-21 07:10:37',
  'has_trialed' => 0,
  'user_id' => 25056,
)
2025-6-21 07:10:37 - Created new customer #25787.
2025-6-21 07:10:37 - Registration type: new.
2025-6-21 07:10:37 - Adding new membership. Data: array (
  'customer_id' => '25787',
  'user_id' => '25056',
  'object_id' => 12,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 9.99,
  'recurring_amount' => 9.99,
  'created_date' => '2025-06-21 07:10:37',
  'expiration_date' => '2025-07-21 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => 'a373ef2f347c86e996e7d8fff6f425a1',
)
2025-6-21 07:10:37 - New payment inserted. ID: 134099; User ID: 25056; Amount: 9.99; Subscription: Calculator with ChatDTC (Unlimited Credits); Status: pending
2025-6-21 07:10:38 - Registration for user #25056 sent to gateway. Level ID: 12; Initial Amount: 9.99; Recurring Amount: 9.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34514
2025-6-21 07:10:38 - Updating membership #34514. New data: array (
  'gateway_customer_id' => 'cus_SXVGsv3krKWE67',
).
2025-6-21 07:10:41 - Using recovered payment #134099 for registration. Transaction type: new.
2025-6-21 07:10:41 - Registration for user #25056 sent to gateway. Level ID: 12; Initial Amount: 9.99; Recurring Amount: 9.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34514
2025-6-21 07:10:43 - Updating payment #134099 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3RcQFnDrMO37mudt12YeSqbo',
  'status' => 'complete',
)
2025-6-21 07:10:43 - Completing registration for customer #25787 via payment #134099.
2025-6-21 07:10:43 - Activating membership #34514.
2025-6-21 07:10:43 - Disabling all memberships for customer #25787 except: '34514'.
2025-6-21 07:10:43 - Updating membership #34514. New data: array (
  'activated_date' => '2025-06-21 07:10:43',
).
2025-6-21 07:10:43 - Updating membership #34514. New data: array (
  'status' => 'active',
).
2025-6-21 07:10:43 - Removing old role subscriber, adding new role subscriber for membership #34514 (user ID #25056).
2025-6-21 07:10:43 - Active email sent to user #25056 for membership #34514.
2025-6-21 07:10:43 - Active email sent to admin(s) regarding membership #34514.
2025-6-21 07:10:43 - Payment Received email not sent to user #25056 for payment ID #134099 - message is empty or disabled.
2025-6-21 07:10:43 - Payment Received email not sent to admin(s) for payment ID #134099 - message is empty or disabled.
2025-6-21 07:10:43 - Updating membership #34514. New data: array (
  'times_billed' => 1,
).
2025-6-21 07:10:43 - Payment #134099 completed for member #25056 via Stripe gateway.
2025-6-21 07:10:44 - Stripe Gateway: Using newly calculated expiration for subscription start date: 2025-07-21 23:59:59
2025-6-21 07:10:44 - Stripe Gateway: Creating subscription with 1753114244 start date via trial_end.
2025-6-21 07:10:46 - Updating membership #34514. New data: array (
  'gateway_subscription_id' => 'sub_1RcQFtDrMO37mudtUw9cheIx',
).
2025-6-21 07:11:06 - "Can cancel" status for membership #34514: true.
2025-6-21 07:11:06 - "Can cancel" status for membership #34514: true.
2025-6-21 07:11:21 - Adding 9.99 proration credits to registration for user #25056.
2025-6-21 07:11:22 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.22965092402464 (ID #9)
2025-6-21 07:11:22 - Adding 9.99 proration credits to registration for user #25056.
2025-6-21 07:11:22 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.22965092402464 (ID #9)
2025-6-21 07:11:22 - Adding 9.99 proration credits to registration for user #25056.
2025-6-21 07:11:29 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.082108145106092 (ID #11)
2025-6-21 07:11:29 - Adding 9.99 proration credits to registration for user #25056.
2025-6-21 07:11:29 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.082108145106092 (ID #11)
2025-6-21 07:11:29 - Adding 9.99 proration credits to registration for user #25056.
2025-6-21 07:12:14 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.082108145106092 (ID #11)
2025-6-21 07:12:14 - Adding 9.99 proration credits to registration for user #25056.
2025-6-21 07:12:14 - Started new registration for membership level #11 via stripe.
2025-6-21 07:12:15 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.082108145106092 (ID #11)
2025-6-21 07:12:15 - Adding 9.99 proration credits to registration for user #25056.
2025-6-21 07:12:15 - Started new registration for membership level #11 via stripe.
2025-6-21 07:12:15 - Registration type: downgrade.
2025-6-21 07:12:15 - Adding new membership. Data: array (
  'customer_id' => '25787',
  'user_id' => '25056',
  'object_id' => 11,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 20.0,
  'recurring_amount' => 29.99,
  'created_date' => '2025-06-21 07:12:15',
  'expiration_date' => '2026-06-21 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '3e06daf6d03328b6519d6f895371ebdc',
  'upgraded_from' => '34514',
)
2025-6-21 07:12:15 - Updating membership #34514. New data: array (
  'status' => 'active',
).
2025-6-21 07:12:15 - Updating membership #34515. New data: array (
  'status' => 'pending',
  'created_date' => 'July 21, 2025',
  'activated_date' => 'July 21, 2025',
).
2025-6-21 07:12:15 - New payment inserted. ID: 134100; User ID: 25056; Amount: 20.00; Subscription: Dynasty Trade Calculator (Yearly); Status: pending
2025-6-21 07:12:15 - Registration for user #25056 sent to gateway. Level ID: 11; Initial Amount: 20.00; Recurring Amount: 29.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34515
2025-6-21 07:12:16 - Updating membership #34515. New data: array (
  'gateway_customer_id' => 'cus_SXVGsv3krKWE67',
).
2025-6-21 07:12:18 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.082108145106092 (ID #11)
2025-6-21 07:12:18 - Using recovered payment #134100 for registration. Transaction type: downgrade.
2025-6-21 07:12:18 - Adding 9.99 proration credits to registration for user #25056.
2025-6-21 07:12:18 - Registration for user #25056 sent to gateway. Level ID: 11; Initial Amount: 20.00; Recurring Amount: 29.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34515
2025-6-21 07:12:19 - Updating payment #134100 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3RcQHMDrMO37mudt1FIvB4nz',
  'status' => 'complete',
)
2025-6-21 07:12:19 - Completing registration for customer #25787 via payment #134100.
2025-6-21 07:12:19 - Activating membership #34515.
2025-6-21 07:12:19 - Disabling all memberships for customer #25787 except: '34515'.
2025-6-21 07:12:19 - Disabling membership #34514.
2025-6-21 07:12:19 - Updating membership #34514. New data: array (
  'disabled' => 1,
).
2025-6-21 07:12:19 - "Can cancel" status for membership #34514: true.
2025-6-21 07:12:19 - "Can cancel" status for membership #34514: true.
2025-6-21 07:12:21 - Failed to cancel Stripe payment profile sub_1RcQFtDrMO37mudtUw9cheIx. Error code: resource_missing; Error Message: No such subscription: 'sub_1RcQFtDrMO37mudtUw9cheIx'.
2025-6-21 07:12:21 - Updating membership #34514. New data: array (
  'status' => 'cancelled',
  'cancellation_date' => '2025-06-21 07:12:21',
).
2025-6-21 07:12:21 - Updating recurring status for membership #34514. Customer ID: 25787; Previous: true; New: false
2025-6-21 07:12:21 - Updating membership #34514. New data: array (
  'auto_renew' => 0,
).
2025-6-21 07:12:21 - Payment profile successfully cancelled for membership #34514.
2025-6-21 07:12:21 - Updating membership #34515. New data: array (
  'status' => 'active',
).
2025-6-21 07:12:21 - Removing old role subscriber, adding new role subscriber for membership #34515 (user ID #25056).
2025-6-21 07:12:21 - Active email sent to user #25056 for membership #34515.
2025-6-21 07:12:21 - Active email sent to admin(s) regarding membership #34515.
2025-6-21 07:12:21 - Payment Received email not sent to user #25056 for payment ID #134100 - message is empty or disabled.
2025-6-21 07:12:21 - Payment Received email not sent to admin(s) for payment ID #134100 - message is empty or disabled.
2025-6-21 07:12:21 - Updating membership #34515. New data: array (
  'times_billed' => 1,
).
2025-6-21 07:12:21 - Payment #134100 completed for member #25056 via Stripe gateway.
2025-6-21 07:12:23 - Stripe Gateway: Using newly calculated expiration for subscription start date: 2026-06-21 23:59:59
2025-6-21 07:12:23 - Stripe Gateway: Creating subscription with 1782058343 start date via trial_end.
2025-6-21 07:12:24 - Updating membership #34515. New data: array (
  'gateway_subscription_id' => 'sub_1RcQHTDrMO37mudtnkE1ZepP',
).
2025-6-21 07:12:39 - "Can cancel" status for membership #34515: true.
2025-6-21 07:12:39 - "Can cancel" status for membership #34515: true.
2025-6-21 08:58:00 - Adding 29.99 proration credits to registration for user #25056.
2025-6-21 08:58:02 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.3282135523614 (ID #12)
2025-6-21 08:58:02 - Adding 29.99 proration credits to registration for user #25056.
2025-6-21 08:58:03 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.3282135523614 (ID #12)
2025-6-21 08:58:03 - Adding 29.99 proration credits to registration for user #25056.
2025-6-21 08:58:09 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.3282135523614 (ID #12)
2025-6-21 08:58:09 - Adding 29.99 proration credits to registration for user #25056.
2025-6-21 08:58:09 - Started new registration for membership level #12 via stripe.
2025-6-21 08:58:10 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.3282135523614 (ID #12)
2025-6-21 08:58:10 - Adding 29.99 proration credits to registration for user #25056.
2025-6-21 08:58:10 - Started new registration for membership level #12 via stripe.
2025-6-21 08:58:10 - Registration type: upgrade.
2025-6-21 08:58:10 - Adding new membership. Data: array (
  'customer_id' => '25787',
  'user_id' => '25056',
  'object_id' => 12,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => '0.00',
  'recurring_amount' => 9.99,
  'created_date' => '2025-06-21 08:58:10',
  'expiration_date' => '2025-10-21 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => 'eb2c85a0cb273c7424b64c267047ab86',
  'upgraded_from' => '34515',
)
2025-6-21 08:58:11 - New payment inserted. ID: 134101; User ID: 25056; Amount: 0.00; Subscription: Calculator with ChatDTC (Unlimited Credits); Status: pending
2025-6-21 08:58:11 - Registration for user #25056 sent to gateway. Level ID: 12; Initial Amount: 0.00; Recurring Amount: 9.99; Auto Renew: true; Trial: false; Subscription Start: 2025-10-21 08:58:11; Membership ID: 34516
2025-6-21 08:58:12 - Updating membership #34516. New data: array (
  'gateway_customer_id' => 'cus_SXVGsv3krKWE67',
).
2025-6-21 08:58:14 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.3282135523614 (ID #12)
2025-6-21 08:58:14 - Using recovered payment #134101 for registration. Transaction type: upgrade.
2025-6-21 08:58:14 - Adding 29.99 proration credits to registration for user #25056.
2025-6-21 08:58:14 - Registration for user #25056 sent to gateway. Level ID: 12; Initial Amount: 0.00; Recurring Amount: 9.99; Auto Renew: true; Trial: false; Subscription Start: 2025-10-21 08:58:14; Membership ID: 34516
2025-6-21 08:58:15 - Updating payment #134101 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => '',
  'status' => 'complete',
)
2025-6-21 08:58:15 - Completing registration for customer #25787 via payment #134101.
2025-6-21 08:58:15 - Activating membership #34516.
2025-6-21 08:58:15 - Disabling all memberships for customer #25787 except: '34516'.
2025-6-21 08:58:15 - Disabling membership #34515.
2025-6-21 08:58:15 - Updating membership #34515. New data: array (
  'disabled' => 1,
).
2025-6-21 08:58:15 - "Can cancel" status for membership #34515: true.
2025-6-21 08:58:15 - "Can cancel" status for membership #34515: true.
2025-6-21 08:58:17 - Failed to cancel Stripe payment profile sub_1RcQHTDrMO37mudtnkE1ZepP. Error code: resource_missing; Error Message: No such subscription: 'sub_1RcQHTDrMO37mudtnkE1ZepP'.
2025-6-21 08:58:17 - Updating membership #34515. New data: array (
  'status' => 'cancelled',
  'cancellation_date' => '2025-06-21 08:58:17',
).
2025-6-21 08:58:17 - Updating recurring status for membership #34515. Customer ID: 25787; Previous: true; New: false
2025-6-21 08:58:17 - Updating membership #34515. New data: array (
  'auto_renew' => 0,
).
2025-6-21 08:58:17 - Payment profile successfully cancelled for membership #34515.
2025-6-21 08:58:17 - Updating membership #34516. New data: array (
  'activated_date' => '2025-06-21 08:58:17',
).
2025-6-21 08:58:17 - Updating membership #34516. New data: array (
  'status' => 'active',
).
2025-6-21 08:58:17 - Removing old role subscriber, adding new role subscriber for membership #34516 (user ID #25056).
2025-6-21 08:58:17 - Active email sent to user #25056 for membership #34516.
2025-6-21 08:58:17 - Active email sent to admin(s) regarding membership #34516.
2025-6-21 08:58:17 - Payment Received email not sent to user #25056 - payment amount is 0.
2025-6-21 08:58:17 - Updating membership #34516. New data: array (
  'times_billed' => 1,
).
2025-6-21 08:58:17 - Payment #134101 completed for member #25056 via Stripe gateway.
2025-6-21 08:58:19 - Stripe Gateway: Using subscription start date for subscription: 2025-10-21 08:58:14
2025-6-21 08:58:19 - Stripe Gateway: Creating subscription with 1761037094 start date via trial_end.
2025-6-21 08:58:20 - Updating membership #34516. New data: array (
  'gateway_subscription_id' => 'sub_1RcRvzDrMO37mudtvSIdI05d',
).
2025-6-21 08:58:20 - Updating payment #134101 with new data: array (
  'transaction_id' => 'sub_1RcRvzDrMO37mudtvSIdI05d',
)
2025-6-21 08:58:52 - "Can cancel" status for membership #34516: true.
2025-6-21 08:58:52 - "Can cancel" status for membership #34516: true.
2025-6-21 08:59:14 - Adding 9.99 proration credits to registration for user #25056.
2025-6-21 08:59:15 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.22965092402464 (ID #9)
2025-6-21 08:59:15 - Adding 9.99 proration credits to registration for user #25056.
2025-6-21 08:59:15 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.22965092402464 (ID #9)
2025-6-21 08:59:15 - Adding 9.99 proration credits to registration for user #25056.
2025-6-21 08:59:23 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.082108145106092 (ID #11)
2025-6-21 08:59:23 - Adding 9.99 proration credits to registration for user #25056.
2025-6-21 08:59:23 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.082108145106092 (ID #11)
2025-6-21 08:59:23 - Adding 9.99 proration credits to registration for user #25056.
2025-6-21 08:59:33 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.082108145106092 (ID #11)
2025-6-21 08:59:33 - Adding 9.99 proration credits to registration for user #25056.
2025-6-21 08:59:33 - Started new registration for membership level #11 via stripe.
2025-6-21 08:59:34 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.082108145106092 (ID #11)
2025-6-21 08:59:34 - Adding 9.99 proration credits to registration for user #25056.
2025-6-21 08:59:34 - Started new registration for membership level #11 via stripe.
2025-6-21 08:59:34 - Registration type: downgrade.
2025-6-21 08:59:34 - Adding new membership. Data: array (
  'customer_id' => '25787',
  'user_id' => '25056',
  'object_id' => 11,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 20.0,
  'recurring_amount' => 29.99,
  'created_date' => '2025-06-21 08:59:34',
  'expiration_date' => '2026-06-21 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => 'ef21af1b615597df6b564054782ffd3b',
  'upgraded_from' => '34516',
)
2025-6-21 08:59:34 - Updating membership #34516. New data: array (
  'status' => 'active',
).
2025-6-21 08:59:34 - Updating membership #34517. New data: array (
  'status' => 'pending',
  'created_date' => 'October 21, 2025',
  'activated_date' => 'October 21, 2025',
).
2025-6-21 08:59:34 - New payment inserted. ID: 134102; User ID: 25056; Amount: 20.00; Subscription: Dynasty Trade Calculator (Yearly); Status: pending
2025-6-21 08:59:34 - Registration for user #25056 sent to gateway. Level ID: 11; Initial Amount: 20.00; Recurring Amount: 29.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34517
2025-6-21 08:59:35 - Updating membership #34517. New data: array (
  'gateway_customer_id' => 'cus_SXVGsv3krKWE67',
).
2025-6-21 08:59:37 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.082108145106092 (ID #11)
2025-6-21 08:59:37 - Using recovered payment #134102 for registration. Transaction type: downgrade.
2025-6-21 08:59:37 - Adding 9.99 proration credits to registration for user #25056.
2025-6-21 08:59:37 - Registration for user #25056 sent to gateway. Level ID: 11; Initial Amount: 20.00; Recurring Amount: 29.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34517
2025-6-21 08:59:39 - Updating payment #134102 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3RcRxDDrMO37mudt1p63Qlpr',
  'status' => 'complete',
)
2025-6-21 08:59:39 - Completing registration for customer #25787 via payment #134102.
2025-6-21 08:59:39 - Activating membership #34517.
2025-6-21 08:59:39 - Disabling all memberships for customer #25787 except: '34517'.
2025-6-21 08:59:39 - Disabling membership #34516.
2025-6-21 08:59:39 - Updating membership #34516. New data: array (
  'disabled' => 1,
).
2025-6-21 08:59:39 - "Can cancel" status for membership #34516: true.
2025-6-21 08:59:39 - "Can cancel" status for membership #34516: true.
2025-6-21 08:59:40 - Failed to cancel Stripe payment profile sub_1RcRvzDrMO37mudtvSIdI05d. Error code: resource_missing; Error Message: No such subscription: 'sub_1RcRvzDrMO37mudtvSIdI05d'.
2025-6-21 08:59:40 - Updating membership #34516. New data: array (
  'status' => 'cancelled',
  'cancellation_date' => '2025-06-21 08:59:40',
).
2025-6-21 08:59:40 - Updating recurring status for membership #34516. Customer ID: 25787; Previous: true; New: false
2025-6-21 08:59:40 - Updating membership #34516. New data: array (
  'auto_renew' => 0,
).
2025-6-21 08:59:40 - Payment profile successfully cancelled for membership #34516.
2025-6-21 08:59:40 - Updating membership #34517. New data: array (
  'status' => 'active',
).
2025-6-21 08:59:40 - Removing old role subscriber, adding new role subscriber for membership #34517 (user ID #25056).
2025-6-21 08:59:40 - Active email sent to user #25056 for membership #34517.
2025-6-21 08:59:40 - Active email sent to admin(s) regarding membership #34517.
2025-6-21 08:59:40 - Payment Received email not sent to user #25056 for payment ID #134102 - message is empty or disabled.
2025-6-21 08:59:40 - Payment Received email not sent to admin(s) for payment ID #134102 - message is empty or disabled.
2025-6-21 08:59:40 - Updating membership #34517. New data: array (
  'times_billed' => 1,
).
2025-6-21 08:59:40 - Payment #134102 completed for member #25056 via Stripe gateway.
2025-6-21 08:59:42 - Stripe Gateway: Using newly calculated expiration for subscription start date: 2026-06-21 23:59:59
2025-6-21 08:59:42 - Stripe Gateway: Creating subscription with 1782064782 start date via trial_end.
2025-6-21 08:59:43 - Updating membership #34517. New data: array (
  'gateway_subscription_id' => 'sub_1RcRxKDrMO37mudtwJeI2DYL',
).
2025-6-21 08:59:58 - "Can cancel" status for membership #34517: true.
2025-6-21 08:59:58 - "Can cancel" status for membership #34517: true.
2025-6-21 09:38:13 - Adding 29.99 proration credits to registration for user #25056.
2025-6-21 09:38:15 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.3282135523614 (ID #12)
2025-6-21 09:38:15 - Adding 29.99 proration credits to registration for user #25056.
2025-6-21 09:38:15 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.3282135523614 (ID #12)
2025-6-21 09:38:15 - Adding 29.99 proration credits to registration for user #25056.
2025-6-21 09:38:40 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.3282135523614 (ID #12)
2025-6-21 09:38:40 - Adding 29.99 proration credits to registration for user #25056.
2025-6-21 09:38:40 - Started new registration for membership level #12 via stripe.
2025-6-21 09:38:40 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.3282135523614 (ID #12)
2025-6-21 09:38:40 - Adding 29.99 proration credits to registration for user #25056.
2025-6-21 09:38:40 - Started new registration for membership level #12 via stripe.
2025-6-21 09:38:40 - Registration type: upgrade.
2025-6-21 09:38:40 - Adding new membership. Data: array (
  'customer_id' => '25787',
  'user_id' => '25056',
  'object_id' => 12,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => '0.00',
  'recurring_amount' => 9.99,
  'created_date' => '2025-06-21 09:38:40',
  'expiration_date' => '2025-10-21 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '131cc2ef28bdc104dd9bc3635b1cd701',
  'upgraded_from' => '34517',
)
2025-6-21 09:38:41 - New payment inserted. ID: 134103; User ID: 25056; Amount: 0.00; Subscription: Calculator with ChatDTC (Unlimited Credits); Status: pending
2025-6-21 09:38:42 - Registration for user #25056 sent to gateway. Level ID: 12; Initial Amount: 0.00; Recurring Amount: 9.99; Auto Renew: true; Trial: false; Subscription Start: 2025-10-21 09:38:42; Membership ID: 34518
2025-6-21 09:38:43 - Updating membership #34518. New data: array (
  'gateway_customer_id' => 'cus_SXVGsv3krKWE67',
).
2025-6-21 09:38:45 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.3282135523614 (ID #12)
2025-6-21 09:38:45 - Using recovered payment #134103 for registration. Transaction type: upgrade.
2025-6-21 09:38:45 - Adding 29.99 proration credits to registration for user #25056.
2025-6-21 09:38:45 - Registration for user #25056 sent to gateway. Level ID: 12; Initial Amount: 0.00; Recurring Amount: 9.99; Auto Renew: true; Trial: false; Subscription Start: 2025-10-21 09:38:45; Membership ID: 34518
2025-6-21 09:38:46 - Updating payment #134103 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => '',
  'status' => 'complete',
)
2025-6-21 09:38:46 - Completing registration for customer #25787 via payment #134103.
2025-6-21 09:38:46 - Activating membership #34518.
2025-6-21 09:38:46 - Disabling all memberships for customer #25787 except: '34518'.
2025-6-21 09:38:46 - Disabling membership #34517.
2025-6-21 09:38:46 - Updating membership #34517. New data: array (
  'disabled' => 1,
).
2025-6-21 09:38:46 - "Can cancel" status for membership #34517: true.
2025-6-21 09:38:46 - "Can cancel" status for membership #34517: true.
2025-6-21 09:38:48 - Failed to cancel Stripe payment profile sub_1RcRxKDrMO37mudtwJeI2DYL. Error code: resource_missing; Error Message: No such subscription: 'sub_1RcRxKDrMO37mudtwJeI2DYL'.
2025-6-21 09:38:48 - Updating membership #34517. New data: array (
  'status' => 'cancelled',
  'cancellation_date' => '2025-06-21 09:38:48',
).
2025-6-21 09:38:48 - Updating recurring status for membership #34517. Customer ID: 25787; Previous: true; New: false
2025-6-21 09:38:48 - Updating membership #34517. New data: array (
  'auto_renew' => 0,
).
2025-6-21 09:38:48 - Payment profile successfully cancelled for membership #34517.
2025-6-21 09:38:48 - Updating membership #34518. New data: array (
  'activated_date' => '2025-06-21 09:38:48',
).
2025-6-21 09:38:48 - Updating membership #34518. New data: array (
  'status' => 'active',
).
2025-6-21 09:38:48 - Removing old role subscriber, adding new role subscriber for membership #34518 (user ID #25056).
2025-6-21 09:38:48 - Active email sent to user #25056 for membership #34518.
2025-6-21 09:38:48 - Active email sent to admin(s) regarding membership #34518.
2025-6-21 09:38:48 - Payment Received email not sent to user #25056 - payment amount is 0.
2025-6-21 09:38:48 - Updating membership #34518. New data: array (
  'times_billed' => 1,
).
2025-6-21 09:38:48 - Payment #134103 completed for member #25056 via Stripe gateway.
2025-6-21 09:38:50 - Stripe Gateway: Using subscription start date for subscription: 2025-10-21 09:38:45
2025-6-21 09:38:50 - Stripe Gateway: Creating subscription with 1761039525 start date via trial_end.
2025-6-21 09:38:51 - Updating membership #34518. New data: array (
  'gateway_subscription_id' => 'sub_1RcSZCDrMO37mudtgZeYix3k',
).
2025-6-21 09:38:51 - Updating payment #134103 with new data: array (
  'transaction_id' => 'sub_1RcSZCDrMO37mudtgZeYix3k',
)
2025-6-21 09:39:00 - "Can cancel" status for membership #34518: true.
2025-6-21 09:39:00 - "Can cancel" status for membership #34518: true.
2025-6-21 09:39:11 - Adding 9.99 proration credits to registration for user #25056.
2025-6-21 09:39:13 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.22965092402464 (ID #9)
2025-6-21 09:39:13 - Adding 9.99 proration credits to registration for user #25056.
2025-6-21 09:39:13 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.22965092402464 (ID #9)
2025-6-21 09:39:13 - Adding 9.99 proration credits to registration for user #25056.
2025-6-21 09:39:18 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.082108145106092 (ID #11)
2025-6-21 09:39:18 - Adding 9.99 proration credits to registration for user #25056.
2025-6-21 09:39:18 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.082108145106092 (ID #11)
2025-6-21 09:39:18 - Adding 9.99 proration credits to registration for user #25056.
2025-6-21 09:39:25 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.082108145106092 (ID #11)
2025-6-21 09:39:25 - Adding 9.99 proration credits to registration for user #25056.
2025-6-21 09:39:25 - Started new registration for membership level #11 via stripe.
2025-6-21 09:39:25 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.082108145106092 (ID #11)
2025-6-21 09:39:25 - Adding 9.99 proration credits to registration for user #25056.
2025-6-21 09:39:25 - Started new registration for membership level #11 via stripe.
2025-6-21 09:39:25 - Registration type: downgrade.
2025-6-21 09:39:25 - Adding new membership. Data: array (
  'customer_id' => '25787',
  'user_id' => '25056',
  'object_id' => 11,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 20.0,
  'recurring_amount' => 29.99,
  'created_date' => '2025-06-21 09:39:25',
  'expiration_date' => '2026-06-21 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '25e160a3318a795fbd5d940df12554d6',
  'upgraded_from' => '34518',
)
2025-6-21 09:39:25 - Updating membership #34518. New data: array (
  'status' => 'active',
).
2025-6-21 09:39:25 - Updating membership #34519. New data: array (
  'status' => 'pending',
  'created_date' => 'October 21, 2025',
  'activated_date' => 'October 21, 2025',
).
2025-6-21 09:39:26 - New payment inserted. ID: 134104; User ID: 25056; Amount: 20.00; Subscription: Dynasty Trade Calculator (Yearly); Status: pending
2025-6-21 09:39:26 - Registration for user #25056 sent to gateway. Level ID: 11; Initial Amount: 20.00; Recurring Amount: 29.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34519
2025-6-21 09:39:26 - Updating membership #34519. New data: array (
  'gateway_customer_id' => 'cus_SXVGsv3krKWE67',
).
2025-6-21 09:39:29 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.082108145106092 (ID #11)
2025-6-21 09:39:29 - Using recovered payment #134104 for registration. Transaction type: downgrade.
2025-6-21 09:39:29 - Adding 9.99 proration credits to registration for user #25056.
2025-6-21 09:39:29 - Registration for user #25056 sent to gateway. Level ID: 11; Initial Amount: 20.00; Recurring Amount: 29.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34519
2025-6-21 09:39:30 - Updating payment #134104 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3RcSZmDrMO37mudt3glrMpjN',
  'status' => 'complete',
)
2025-6-21 09:39:30 - Completing registration for customer #25787 via payment #134104.
2025-6-21 09:39:30 - Activating membership #34519.
2025-6-21 09:39:30 - Disabling all memberships for customer #25787 except: '34519'.
2025-6-21 09:39:30 - Disabling membership #34518.
2025-6-21 09:39:30 - Updating membership #34518. New data: array (
  'disabled' => 1,
).
2025-6-21 09:39:30 - "Can cancel" status for membership #34518: true.
2025-6-21 09:39:30 - "Can cancel" status for membership #34518: true.
2025-6-21 09:39:32 - Failed to cancel Stripe payment profile sub_1RcSZCDrMO37mudtgZeYix3k. Error code: resource_missing; Error Message: No such subscription: 'sub_1RcSZCDrMO37mudtgZeYix3k'.
2025-6-21 09:39:32 - Updating membership #34518. New data: array (
  'status' => 'cancelled',
  'cancellation_date' => '2025-06-21 09:39:32',
).
2025-6-21 09:39:32 - Updating recurring status for membership #34518. Customer ID: 25787; Previous: true; New: false
2025-6-21 09:39:32 - Updating membership #34518. New data: array (
  'auto_renew' => 0,
).
2025-6-21 09:39:32 - Payment profile successfully cancelled for membership #34518.
2025-6-21 09:39:32 - Updating membership #34519. New data: array (
  'status' => 'active',
).
2025-6-21 09:39:32 - Removing old role subscriber, adding new role subscriber for membership #34519 (user ID #25056).
2025-6-21 09:39:32 - Active email sent to user #25056 for membership #34519.
2025-6-21 09:39:32 - Active email sent to admin(s) regarding membership #34519.
2025-6-21 09:39:32 - Payment Received email not sent to user #25056 for payment ID #134104 - message is empty or disabled.
2025-6-21 09:39:32 - Payment Received email not sent to admin(s) for payment ID #134104 - message is empty or disabled.
2025-6-21 09:39:32 - Updating membership #34519. New data: array (
  'times_billed' => 1,
).
2025-6-21 09:39:32 - Payment #134104 completed for member #25056 via Stripe gateway.
2025-6-21 09:39:33 - Stripe Gateway: Using newly calculated expiration for subscription start date: 2026-06-21 23:59:59
2025-6-21 09:39:33 - Stripe Gateway: Creating subscription with 1782067173 start date via trial_end.
2025-6-21 09:39:35 - Updating membership #34519. New data: array (
  'gateway_subscription_id' => 'sub_1RcSZuDrMO37mudte7snkjoH',
).
2025-6-21 09:39:46 - "Can cancel" status for membership #34519: true.
2025-6-21 09:39:46 - "Can cancel" status for membership #34519: true.
2025-6-21 09:56:39 - Started new registration for membership level #10 via stripe.
2025-6-21 09:56:40 - Started new registration for membership level #10 via stripe.
2025-6-21 09:56:40 - Adding a new customer. Args: array (
  'date_registered' => '2025-06-21 09:56:40',
  'has_trialed' => 0,
  'user_id' => 25849,
)
2025-6-21 09:56:40 - Created new customer #25788.
2025-6-21 09:56:40 - Registration type: new.
2025-6-21 09:56:40 - Adding new membership. Data: array (
  'customer_id' => '25788',
  'user_id' => '25849',
  'object_id' => 10,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 2.99,
  'recurring_amount' => 2.99,
  'created_date' => '2025-06-21 09:56:40',
  'expiration_date' => '2025-07-21 23:59:59',
  'auto_renew' => 0,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '800322491c73073a51cfe72849c8e2c2',
)
2025-6-21 09:56:41 - New payment inserted. ID: 134105; User ID: 25849; Amount: 2.99; Subscription: Dynasty Trade Calculator (Monthly); Status: pending
2025-6-21 09:56:41 - Registration for user #25849 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: false; Trial: false; Subscription Start: ; Membership ID: 34520
2025-6-21 09:56:42 - Updating membership #34520. New data: array (
  'gateway_customer_id' => 'cus_SXXwnkzPtfLCx3',
).
2025-6-21 09:56:44 - Using recovered payment #134105 for registration. Transaction type: new.
2025-6-21 09:56:44 - Registration for user #25849 sent to gateway. Level ID: 10; Initial Amount: 2.99; Recurring Amount: 2.99; Auto Renew: false; Trial: false; Subscription Start: ; Membership ID: 34520
2025-6-21 09:56:46 - Updating payment #134105 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3RcSqUDrMO37mudt3q61Bxn5',
  'status' => 'complete',
)
2025-6-21 09:56:46 - Completing registration for customer #25788 via payment #134105.
2025-6-21 09:56:46 - Activating membership #34520.
2025-6-21 09:56:46 - Disabling all memberships for customer #25788 except: '34520'.
2025-6-21 09:56:46 - Updating membership #34520. New data: array (
  'activated_date' => '2025-06-21 09:56:46',
).
2025-6-21 09:56:46 - Updating membership #34520. New data: array (
  'status' => 'active',
).
2025-6-21 09:56:46 - Active email sent to user #25849 for membership #34520.
2025-6-21 09:56:46 - Active email sent to admin(s) regarding membership #34520.
2025-6-21 09:56:46 - Payment Received email not sent to user #25849 for payment ID #134105 - message is empty or disabled.
2025-6-21 09:56:46 - Payment Received email not sent to admin(s) for payment ID #134105 - message is empty or disabled.
2025-6-21 09:56:46 - Updating membership #34520. New data: array (
  'times_billed' => 1,
).
2025-6-21 09:56:46 - Payment #134105 completed for member #25849 via Stripe gateway.
2025-6-21 09:57:48 - "Can cancel" status for membership #34520: false. Reason: membership not recurring.
2025-6-21 09:57:48 - "Can cancel" status for membership #34520: false. Reason: membership not recurring.
2025-6-21 09:58:03 - Adding 2.99 proration credits to registration for user #25849.
2025-6-21 09:58:05 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.3282135523614 (ID #12)
2025-6-21 09:58:05 - Adding 2.99 proration credits to registration for user #25849.
2025-6-21 09:58:05 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.3282135523614 (ID #12)
2025-6-21 09:58:05 - Adding 2.99 proration credits to registration for user #25849.
2025-6-21 09:58:14 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.16394250513347 (ID #7)
2025-6-21 09:58:14 - Adding 2.99 proration credits to registration for user #25849.
2025-6-21 09:58:14 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.16394250513347 (ID #7)
2025-6-21 09:58:14 - Adding 2.99 proration credits to registration for user #25849.
2025-6-21 09:58:21 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.16394250513347 (ID #7)
2025-6-21 09:58:21 - Adding 2.99 proration credits to registration for user #25849.
2025-6-21 09:58:21 - Started new registration for membership level #7 via stripe.
2025-6-21 09:58:22 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.16394250513347 (ID #7)
2025-6-21 09:58:22 - Adding 2.99 proration credits to registration for user #25849.
2025-6-21 09:58:22 - Started new registration for membership level #7 via stripe.
2025-6-21 09:58:22 - Registration type: upgrade.
2025-6-21 09:58:22 - Adding new membership. Data: array (
  'customer_id' => '25788',
  'user_id' => '25849',
  'object_id' => 7,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 2.0,
  'recurring_amount' => 4.99,
  'created_date' => '2025-06-21 09:58:22',
  'expiration_date' => '2025-07-21 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '580ab3e31d9ef7dae465d1b4f2ba9ad9',
  'upgraded_from' => '34520',
)
2025-6-21 09:58:23 - New payment inserted. ID: 134106; User ID: 25849; Amount: 2.00; Subscription: Calculator with ChatDTC (50 Credits); Status: pending
2025-6-21 09:58:23 - Registration for user #25849 sent to gateway. Level ID: 7; Initial Amount: 2.00; Recurring Amount: 4.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34521
2025-6-21 09:58:24 - Updating membership #34521. New data: array (
  'gateway_customer_id' => 'cus_SXXwnkzPtfLCx3',
).
2025-6-21 09:58:26 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.16394250513347 (ID #7)
2025-6-21 09:58:26 - Using recovered payment #134106 for registration. Transaction type: upgrade.
2025-6-21 09:58:26 - Adding 2.99 proration credits to registration for user #25849.
2025-6-21 09:58:26 - Registration for user #25849 sent to gateway. Level ID: 7; Initial Amount: 2.00; Recurring Amount: 4.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34521
2025-6-21 09:58:28 - Updating payment #134106 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3RcSs8DrMO37mudt1mOdKy6z',
  'status' => 'complete',
)
2025-6-21 09:58:28 - Completing registration for customer #25788 via payment #134106.
2025-6-21 09:58:28 - Activating membership #34521.
2025-6-21 09:58:28 - Disabling all memberships for customer #25788 except: '34521'.
2025-6-21 09:58:28 - Disabling membership #34520.
2025-6-21 09:58:28 - Updating membership #34520. New data: array (
  'disabled' => 1,
).
2025-6-21 09:58:28 - "Can cancel" status for membership #34520: false. Reason: membership not recurring.
2025-6-21 09:58:28 - Updating membership #34521. New data: array (
  'activated_date' => '2025-06-21 09:58:28',
).
2025-6-21 09:58:28 - Updating membership #34521. New data: array (
  'status' => 'active',
).
2025-6-21 09:58:28 - Removing old role subscriber, adding new role subscriber for membership #34521 (user ID #25849).
2025-6-21 09:58:28 - Active email sent to user #25849 for membership #34521.
2025-6-21 09:58:28 - Active email sent to admin(s) regarding membership #34521.
2025-6-21 09:58:28 - Payment Received email not sent to user #25849 for payment ID #134106 - message is empty or disabled.
2025-6-21 09:58:28 - Payment Received email not sent to admin(s) for payment ID #134106 - message is empty or disabled.
2025-6-21 09:58:28 - Updating membership #34521. New data: array (
  'times_billed' => 1,
).
2025-6-21 09:58:28 - Payment #134106 completed for member #25849 via Stripe gateway.
2025-6-21 09:58:30 - Stripe Gateway: Using newly calculated expiration for subscription start date: 2025-07-21 23:59:59
2025-6-21 09:58:30 - Stripe Gateway: Creating subscription with 1753124310 start date via trial_end.
2025-6-21 09:58:31 - Updating membership #34521. New data: array (
  'gateway_subscription_id' => 'sub_1RcSsEDrMO37mudtrKObHT6u',
).
2025-6-21 09:59:03 - "Can cancel" status for membership #34521: true.
2025-6-21 09:59:03 - "Can cancel" status for membership #34521: true.
2025-6-21 09:59:25 - Adding 4.99 proration credits to registration for user #25849.
2025-6-21 09:59:26 - Old price per day: 0.16394250513347 (ID #7); New price per day: 0.3282135523614 (ID #12)
2025-6-21 09:59:26 - Adding 4.99 proration credits to registration for user #25849.
2025-6-21 09:59:26 - Old price per day: 0.16394250513347 (ID #7); New price per day: 0.3282135523614 (ID #12)
2025-6-21 09:59:26 - Adding 4.99 proration credits to registration for user #25849.
2025-6-21 09:59:33 - Old price per day: 0.16394250513347 (ID #7); New price per day: 0.3282135523614 (ID #12)
2025-6-21 09:59:33 - Adding 4.99 proration credits to registration for user #25849.
2025-6-21 09:59:33 - Started new registration for membership level #12 via stripe.
2025-6-21 09:59:34 - Old price per day: 0.16394250513347 (ID #7); New price per day: 0.3282135523614 (ID #12)
2025-6-21 09:59:34 - Adding 4.99 proration credits to registration for user #25849.
2025-6-21 09:59:34 - Started new registration for membership level #12 via stripe.
2025-6-21 09:59:34 - Registration type: upgrade.
2025-6-21 09:59:34 - Adding new membership. Data: array (
  'customer_id' => '25788',
  'user_id' => '25849',
  'object_id' => 12,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 5.0,
  'recurring_amount' => 9.99,
  'created_date' => '2025-06-21 09:59:34',
  'expiration_date' => '2025-07-21 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => 'ac78728851991fd0cb906534d04c4680',
  'upgraded_from' => '34521',
)
2025-6-21 09:59:35 - New payment inserted. ID: 134107; User ID: 25849; Amount: 5.00; Subscription: Calculator with ChatDTC (Unlimited Credits); Status: pending
2025-6-21 09:59:35 - Registration for user #25849 sent to gateway. Level ID: 12; Initial Amount: 5.00; Recurring Amount: 9.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34522
2025-6-21 09:59:35 - Updating membership #34522. New data: array (
  'gateway_customer_id' => 'cus_SXXwnkzPtfLCx3',
).
2025-6-21 09:59:38 - Old price per day: 0.16394250513347 (ID #7); New price per day: 0.3282135523614 (ID #12)
2025-6-21 09:59:38 - Using recovered payment #134107 for registration. Transaction type: upgrade.
2025-6-21 09:59:38 - Adding 4.99 proration credits to registration for user #25849.
2025-6-21 09:59:38 - Registration for user #25849 sent to gateway. Level ID: 12; Initial Amount: 5.00; Recurring Amount: 9.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34522
2025-6-21 09:59:39 - Updating payment #134107 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3RcStIDrMO37mudt3vqAakqv',
  'status' => 'complete',
)
2025-6-21 09:59:39 - Completing registration for customer #25788 via payment #134107.
2025-6-21 09:59:39 - Activating membership #34522.
2025-6-21 09:59:39 - Disabling all memberships for customer #25788 except: '34522'.
2025-6-21 09:59:39 - Disabling membership #34521.
2025-6-21 09:59:39 - Updating membership #34521. New data: array (
  'disabled' => 1,
).
2025-6-21 09:59:39 - "Can cancel" status for membership #34521: true.
2025-6-21 09:59:39 - "Can cancel" status for membership #34521: true.
2025-6-21 09:59:41 - Failed to cancel Stripe payment profile sub_1RcSsEDrMO37mudtrKObHT6u. Error code: resource_missing; Error Message: No such subscription: 'sub_1RcSsEDrMO37mudtrKObHT6u'.
2025-6-21 09:59:41 - Updating membership #34521. New data: array (
  'status' => 'cancelled',
  'cancellation_date' => '2025-06-21 09:59:41',
).
2025-6-21 09:59:41 - Updating recurring status for membership #34521. Customer ID: 25788; Previous: true; New: false
2025-6-21 09:59:41 - Updating membership #34521. New data: array (
  'auto_renew' => 0,
).
2025-6-21 09:59:41 - Payment profile successfully cancelled for membership #34521.
2025-6-21 09:59:41 - Updating membership #34522. New data: array (
  'activated_date' => '2025-06-21 09:59:41',
).
2025-6-21 09:59:41 - Updating membership #34522. New data: array (
  'status' => 'active',
).
2025-6-21 09:59:41 - Removing old role subscriber, adding new role subscriber for membership #34522 (user ID #25849).
2025-6-21 09:59:41 - Active email sent to user #25849 for membership #34522.
2025-6-21 09:59:41 - Active email sent to admin(s) regarding membership #34522.
2025-6-21 09:59:41 - Payment Received email not sent to user #25849 for payment ID #134107 - message is empty or disabled.
2025-6-21 09:59:41 - Payment Received email not sent to admin(s) for payment ID #134107 - message is empty or disabled.
2025-6-21 09:59:41 - Updating membership #34522. New data: array (
  'times_billed' => 1,
).
2025-6-21 09:59:41 - Payment #134107 completed for member #25849 via Stripe gateway.
2025-6-21 09:59:43 - Stripe Gateway: Using newly calculated expiration for subscription start date: 2025-07-21 23:59:59
2025-6-21 09:59:43 - Stripe Gateway: Creating subscription with 1753124383 start date via trial_end.
2025-6-21 09:59:44 - Updating membership #34522. New data: array (
  'gateway_subscription_id' => 'sub_1RcStPDrMO37mudtv4Knr6SG',
).
2025-6-21 09:59:56 - "Can cancel" status for membership #34522: true.
2025-6-21 09:59:56 - "Can cancel" status for membership #34522: true.
2025-6-21 14:20:18 - Starting rcp_check_for_expired_users() cron job.
2025-6-21 14:20:18 - No expired memberships found.
2025-6-21 14:20:18 - Starting rcp_check_for_soon_to_expire_users() cron job.
2025-6-21 14:20:18 - Processing expiration reminder. ID: 0; Period: -1month; Levels: 1, 3, 2.
2025-6-21 14:20:18 - Reminder is not enabled - exiting.
2025-6-22 11:10:50 - Starting rcp_check_member_counts() cron job.
2025-6-22 11:10:50 - Starting rcp_mark_abandoned_payments() cron job.
2025-6-22 11:13:46 - Started new registration for membership level #12 via stripe.
2025-6-22 11:13:47 - Started new registration for membership level #12 via stripe.
2025-6-22 11:13:47 - Adding a new customer. Args: array (
  'date_registered' => '2025-06-22 11:13:47',
  'has_trialed' => 0,
  'user_id' => 25850,
)
2025-6-22 11:13:47 - Created new customer #25789.
2025-6-22 11:13:47 - Registration type: new.
2025-6-22 11:13:47 - Adding new membership. Data: array (
  'customer_id' => '25789',
  'user_id' => '25850',
  'object_id' => 12,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 9.99,
  'recurring_amount' => 9.99,
  'created_date' => '2025-06-22 11:13:47',
  'expiration_date' => '2025-07-22 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '********************************',
)
2025-6-22 11:13:48 - New payment inserted. ID: 134108; User ID: 25850; Amount: 9.99; Subscription: Calculator with ChatDTC (Unlimited Credits); Status: pending
2025-6-22 11:13:48 - Registration for user #25850 sent to gateway. Level ID: 12; Initial Amount: 9.99; Recurring Amount: 9.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34523
2025-6-22 11:13:49 - Updating membership #34523. New data: array (
  'gateway_customer_id' => 'cus_SXwPyRtjJhBZPP',
).
2025-6-22 11:13:51 - Using recovered payment #134108 for registration. Transaction type: new.
2025-6-22 11:13:51 - Registration for user #25850 sent to gateway. Level ID: 12; Initial Amount: 9.99; Recurring Amount: 9.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34523
2025-6-22 11:13:53 - Updating payment #134108 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3RcqWfDrMO37mudt0YcyRzvO',
  'status' => 'complete',
)
2025-6-22 11:13:53 - Completing registration for customer #25789 via payment #134108.
2025-6-22 11:13:53 - Activating membership #34523.
2025-6-22 11:13:53 - Disabling all memberships for customer #25789 except: '34523'.
2025-6-22 11:13:53 - Updating membership #34523. New data: array (
  'activated_date' => '2025-06-22 11:13:53',
).
2025-6-22 11:13:53 - Updating membership #34523. New data: array (
  'status' => 'active',
).
2025-6-22 11:13:53 - Active email sent to user #25850 for membership #34523.
2025-6-22 11:13:53 - Active email sent to admin(s) regarding membership #34523.
2025-6-22 11:13:53 - Payment Received email not sent to user #25850 for payment ID #134108 - message is empty or disabled.
2025-6-22 11:13:53 - Payment Received email not sent to admin(s) for payment ID #134108 - message is empty or disabled.
2025-6-22 11:13:53 - Updating membership #34523. New data: array (
  'times_billed' => 1,
).
2025-6-22 11:13:53 - Payment #134108 completed for member #25850 via Stripe gateway.
2025-6-22 11:13:54 - Stripe Gateway: Using newly calculated expiration for subscription start date: 2025-07-22 23:59:59
2025-6-22 11:13:54 - Stripe Gateway: Creating subscription with 1753215234 start date via trial_end.
2025-6-22 11:13:56 - Updating membership #34523. New data: array (
  'gateway_subscription_id' => 'sub_1RcqWlDrMO37mudtfWItB0t8',
).
2025-6-22 11:16:21 - "Can cancel" status for membership #34523: true.
2025-6-22 11:16:21 - "Can cancel" status for membership #34523: true.
2025-6-22 11:16:32 - Adding 9.99 proration credits to registration for user #25850.
2025-6-22 11:16:33 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.22965092402464 (ID #9)
2025-6-22 11:16:33 - Adding 9.99 proration credits to registration for user #25850.
2025-6-22 11:16:33 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.22965092402464 (ID #9)
2025-6-22 11:16:33 - Adding 9.99 proration credits to registration for user #25850.
2025-6-22 11:16:40 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.082108145106092 (ID #11)
2025-6-22 11:16:40 - Adding 9.99 proration credits to registration for user #25850.
2025-6-22 11:16:40 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.082108145106092 (ID #11)
2025-6-22 11:16:40 - Adding 9.99 proration credits to registration for user #25850.
2025-6-22 11:16:43 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.0982340862423 (ID #10)
2025-6-22 11:16:43 - Adding 9.99 proration credits to registration for user #25850.
2025-6-22 11:16:43 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.0982340862423 (ID #10)
2025-6-22 11:16:43 - Adding 9.99 proration credits to registration for user #25850.
2025-6-22 11:16:48 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.082108145106092 (ID #11)
2025-6-22 11:16:48 - Adding 9.99 proration credits to registration for user #25850.
2025-6-22 11:16:49 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.082108145106092 (ID #11)
2025-6-22 11:16:49 - Adding 9.99 proration credits to registration for user #25850.
2025-6-22 11:17:04 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.082108145106092 (ID #11)
2025-6-22 11:17:04 - Adding 9.99 proration credits to registration for user #25850.
2025-6-22 11:17:04 - Started new registration for membership level #11 via stripe.
2025-6-22 11:17:04 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.082108145106092 (ID #11)
2025-6-22 11:17:04 - Adding 9.99 proration credits to registration for user #25850.
2025-6-22 11:17:04 - Started new registration for membership level #11 via stripe.
2025-6-22 11:17:04 - Registration type: downgrade.
2025-6-22 11:17:04 - Adding new membership. Data: array (
  'customer_id' => '25789',
  'user_id' => '25850',
  'object_id' => 11,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 20.0,
  'recurring_amount' => 29.99,
  'created_date' => '2025-06-22 11:17:04',
  'expiration_date' => '2026-06-22 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '09684feda76491b11b02f02abcd04592',
  'upgraded_from' => '34523',
)
2025-6-22 11:17:04 - Updating membership #34523. New data: array (
  'status' => 'active',
).
2025-6-22 11:17:04 - Updating membership #34524. New data: array (
  'status' => 'pending',
  'created_date' => 'July 22, 2025',
  'activated_date' => 'July 22, 2025',
).
2025-6-22 11:17:04 - New payment inserted. ID: 134109; User ID: 25850; Amount: 20.00; Subscription: Dynasty Trade Calculator (Yearly); Status: pending
2025-6-22 11:17:04 - Registration for user #25850 sent to gateway. Level ID: 11; Initial Amount: 20.00; Recurring Amount: 29.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34524
2025-6-22 11:17:05 - Updating membership #34524. New data: array (
  'gateway_customer_id' => 'cus_SXwPyRtjJhBZPP',
).
2025-6-22 11:17:08 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.082108145106092 (ID #11)
2025-6-22 11:17:08 - Using recovered payment #134109 for registration. Transaction type: downgrade.
2025-6-22 11:17:08 - Adding 9.99 proration credits to registration for user #25850.
2025-6-22 11:17:08 - Registration for user #25850 sent to gateway. Level ID: 11; Initial Amount: 20.00; Recurring Amount: 29.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34524
2025-6-22 11:17:09 - Updating payment #134109 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3RcqZpDrMO37mudt2KsrCf6n',
  'status' => 'complete',
)
2025-6-22 11:17:09 - Completing registration for customer #25789 via payment #134109.
2025-6-22 11:17:09 - Activating membership #34524.
2025-6-22 11:17:09 - Disabling all memberships for customer #25789 except: '34524'.
2025-6-22 11:17:09 - Disabling membership #34523.
2025-6-22 11:17:09 - Updating membership #34523. New data: array (
  'disabled' => 1,
).
2025-6-22 11:17:09 - "Can cancel" status for membership #34523: true.
2025-6-22 11:17:09 - "Can cancel" status for membership #34523: true.
2025-6-22 11:17:11 - Failed to cancel Stripe payment profile sub_1RcqWlDrMO37mudtfWItB0t8. Error code: resource_missing; Error Message: No such subscription: 'sub_1RcqWlDrMO37mudtfWItB0t8'.
2025-6-22 11:17:11 - Updating membership #34523. New data: array (
  'status' => 'cancelled',
  'cancellation_date' => '2025-06-22 11:17:11',
).
2025-6-22 11:17:11 - Updating recurring status for membership #34523. Customer ID: 25789; Previous: true; New: false
2025-6-22 11:17:11 - Updating membership #34523. New data: array (
  'auto_renew' => 0,
).
2025-6-22 11:17:11 - Payment profile successfully cancelled for membership #34523.
2025-6-22 11:17:11 - Updating membership #34524. New data: array (
  'status' => 'active',
).
2025-6-22 11:17:11 - Removing old role subscriber, adding new role subscriber for membership #34524 (user ID #25850).
2025-6-22 11:17:11 - Active email sent to user #25850 for membership #34524.
2025-6-22 11:17:11 - Active email sent to admin(s) regarding membership #34524.
2025-6-22 11:17:11 - Payment Received email not sent to user #25850 for payment ID #134109 - message is empty or disabled.
2025-6-22 11:17:11 - Payment Received email not sent to admin(s) for payment ID #134109 - message is empty or disabled.
2025-6-22 11:17:11 - Updating membership #34524. New data: array (
  'times_billed' => 1,
).
2025-6-22 11:17:11 - Payment #134109 completed for member #25850 via Stripe gateway.
2025-6-22 11:17:13 - Stripe Gateway: Using newly calculated expiration for subscription start date: 2026-06-22 23:59:59
2025-6-22 11:17:13 - Stripe Gateway: Creating subscription with 1782159433 start date via trial_end.
2025-6-22 11:17:14 - Updating membership #34524. New data: array (
  'gateway_subscription_id' => 'sub_1RcqZxDrMO37mudtwZKBqPe2',
).
2025-6-22 11:17:24 - "Can cancel" status for membership #34524: true.
2025-6-22 11:17:24 - "Can cancel" status for membership #34524: true.
2025-6-22 11:17:35 - Adding 29.99 proration credits to registration for user #25850.
2025-6-22 11:17:36 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.3282135523614 (ID #12)
2025-6-22 11:17:36 - Adding 29.99 proration credits to registration for user #25850.
2025-6-22 11:17:36 - Old price per day: 0.082108145106092 (ID #11); New price per day: 0.3282135523614 (ID #12)
2025-6-22 11:17:36 - Adding 29.99 proration credits to registration for user #25850.
2025-6-22 11:17:41 - Adding 29.99 proration credits to registration for user #25850.
2025-6-22 11:17:42 - Adding 29.99 proration credits to registration for user #25850.
2025-6-22 11:17:42 - Adding 29.99 proration credits to registration for user #25850.
2025-6-22 11:17:48 - Adding 29.99 proration credits to registration for user #25850.
2025-6-22 11:17:48 - Adding 29.99 proration credits to registration for user #25850.
2025-6-22 11:17:57 - Adding 29.99 proration credits to registration for user #25850.
2025-6-22 11:17:57 - Started new registration for membership level #10 via stripe.
2025-6-22 11:17:57 - Adding 29.99 proration credits to registration for user #25850.
2025-6-22 11:17:57 - Started new registration for membership level #10 via stripe.
2025-6-22 11:17:57 - Registration type: downgrade.
2025-6-22 11:17:57 - Adding new membership. Data: array (
  'customer_id' => '25789',
  'user_id' => '25850',
  'object_id' => 10,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => '0.00',
  'recurring_amount' => 2.99,
  'created_date' => '2025-06-22 11:17:57',
  'expiration_date' => '2026-05-22 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '76c6bbea5af18eb4056841f52053ed62',
  'upgraded_from' => '34524',
)
2025-6-22 11:17:57 - Updating membership #34524. New data: array (
  'status' => 'active',
).
2025-6-22 11:17:57 - Updating membership #34525. New data: array (
  'status' => 'pending',
  'created_date' => 'June 22, 2026',
  'activated_date' => 'June 22, 2026',
).
2025-6-22 11:17:57 - New payment inserted. ID: 134110; User ID: 25850; Amount: 0.00; Subscription: Dynasty Trade Calculator (Monthly); Status: pending
2025-6-22 11:17:58 - Registration for user #25850 sent to gateway. Level ID: 10; Initial Amount: 0.00; Recurring Amount: 2.99; Auto Renew: true; Trial: false; Subscription Start: 2025-07-22 11:17:58; Membership ID: 34525
2025-6-22 11:17:58 - Updating membership #34525. New data: array (
  'gateway_customer_id' => 'cus_SXwPyRtjJhBZPP',
).
2025-6-22 11:18:00 - Using recovered payment #134110 for registration. Transaction type: downgrade.
2025-6-22 11:18:00 - Adding 29.99 proration credits to registration for user #25850.
2025-6-22 11:18:00 - Registration for user #25850 sent to gateway. Level ID: 10; Initial Amount: 0.00; Recurring Amount: 2.99; Auto Renew: true; Trial: false; Subscription Start: 2025-07-22 11:18:00; Membership ID: 34525
2025-6-22 11:18:01 - Updating payment #134110 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => '',
  'status' => 'complete',
)
2025-6-22 11:18:01 - Completing registration for customer #25789 via payment #134110.
2025-6-22 11:18:01 - Activating membership #34525.
2025-6-22 11:18:01 - Disabling all memberships for customer #25789 except: '34525'.
2025-6-22 11:18:01 - Disabling membership #34524.
2025-6-22 11:18:01 - Updating membership #34524. New data: array (
  'disabled' => 1,
).
2025-6-22 11:18:01 - "Can cancel" status for membership #34524: true.
2025-6-22 11:18:01 - "Can cancel" status for membership #34524: true.
2025-6-22 11:18:03 - Failed to cancel Stripe payment profile sub_1RcqZxDrMO37mudtwZKBqPe2. Error code: resource_missing; Error Message: No such subscription: 'sub_1RcqZxDrMO37mudtwZKBqPe2'.
2025-6-22 11:18:03 - Updating membership #34524. New data: array (
  'status' => 'cancelled',
  'cancellation_date' => '2025-06-22 11:18:03',
).
2025-6-22 11:18:03 - Updating recurring status for membership #34524. Customer ID: 25789; Previous: true; New: false
2025-6-22 11:18:03 - Updating membership #34524. New data: array (
  'auto_renew' => 0,
).
2025-6-22 11:18:03 - Payment profile successfully cancelled for membership #34524.
2025-6-22 11:18:03 - Updating membership #34525. New data: array (
  'status' => 'active',
).
2025-6-22 11:18:03 - Removing old role subscriber, adding new role subscriber for membership #34525 (user ID #25850).
2025-6-22 11:18:03 - Active email sent to user #25850 for membership #34525.
2025-6-22 11:18:03 - Active email sent to admin(s) regarding membership #34525.
2025-6-22 11:18:03 - Payment Received email not sent to user #25850 - payment amount is 0.
2025-6-22 11:18:03 - Updating membership #34525. New data: array (
  'times_billed' => 1,
).
2025-6-22 11:18:03 - Payment #134110 completed for member #25850 via Stripe gateway.
2025-6-22 11:18:05 - Stripe Gateway: Using subscription start date for subscription: 2025-07-22 11:18:00
2025-6-22 11:18:05 - Stripe Gateway: Creating subscription with 1753183080 start date via billing_cycle_anchor.
2025-6-22 11:18:06 - Updating membership #34525. New data: array (
  'gateway_subscription_id' => 'sub_1RcqanDrMO37mudtKcH8n5JE',
).
2025-6-22 11:18:06 - Updating payment #134110 with new data: array (
  'transaction_id' => 'sub_1RcqanDrMO37mudtKcH8n5JE',
)
2025-6-22 11:18:14 - "Can cancel" status for membership #34525: true.
2025-6-22 11:18:14 - "Can cancel" status for membership #34525: true.
2025-6-22 11:18:25 - Adding 2.99 proration credits to registration for user #25850.
2025-6-22 11:18:26 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.3282135523614 (ID #12)
2025-6-22 11:18:26 - Adding 2.99 proration credits to registration for user #25850.
2025-6-22 11:18:26 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.3282135523614 (ID #12)
2025-6-22 11:18:26 - Adding 2.99 proration credits to registration for user #25850.
2025-6-22 11:18:30 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.3282135523614 (ID #12)
2025-6-22 11:18:30 - Adding 2.99 proration credits to registration for user #25850.
2025-6-22 11:18:30 - Started new registration for membership level #12 via stripe.
2025-6-22 11:18:31 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.3282135523614 (ID #12)
2025-6-22 11:18:31 - Adding 2.99 proration credits to registration for user #25850.
2025-6-22 11:18:31 - Started new registration for membership level #12 via stripe.
2025-6-22 11:18:31 - Registration type: upgrade.
2025-6-22 11:18:31 - Adding new membership. Data: array (
  'customer_id' => '25789',
  'user_id' => '25850',
  'object_id' => 12,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 7.0,
  'recurring_amount' => 9.99,
  'created_date' => '2025-06-22 11:18:31',
  'expiration_date' => '2025-07-22 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => 'ed55c6e36cebfd62ac2044b3de98ebb5',
  'upgraded_from' => '34525',
)
2025-6-22 11:18:32 - New payment inserted. ID: 134111; User ID: 25850; Amount: 7.00; Subscription: Calculator with ChatDTC (Unlimited Credits); Status: pending
2025-6-22 11:18:32 - Registration for user #25850 sent to gateway. Level ID: 12; Initial Amount: 7.00; Recurring Amount: 9.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34526
2025-6-22 11:18:32 - Updating membership #34526. New data: array (
  'gateway_customer_id' => 'cus_SXwPyRtjJhBZPP',
).
2025-6-22 11:18:35 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.3282135523614 (ID #12)
2025-6-22 11:18:35 - Using recovered payment #134111 for registration. Transaction type: upgrade.
2025-6-22 11:18:35 - Adding 2.99 proration credits to registration for user #25850.
2025-6-22 11:18:35 - Registration for user #25850 sent to gateway. Level ID: 12; Initial Amount: 7.00; Recurring Amount: 9.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34526
2025-6-22 11:18:36 - Updating payment #134111 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3RcqbFDrMO37mudt29Cm3xyc',
  'status' => 'complete',
)
2025-6-22 11:18:36 - Completing registration for customer #25789 via payment #134111.
2025-6-22 11:18:36 - Activating membership #34526.
2025-6-22 11:18:36 - Disabling all memberships for customer #25789 except: '34526'.
2025-6-22 11:18:36 - Disabling membership #34525.
2025-6-22 11:18:36 - Updating membership #34525. New data: array (
  'disabled' => 1,
).
2025-6-22 11:18:36 - "Can cancel" status for membership #34525: true.
2025-6-22 11:18:36 - "Can cancel" status for membership #34525: true.
2025-6-22 11:18:38 - Failed to cancel Stripe payment profile sub_1RcqanDrMO37mudtKcH8n5JE. Error code: resource_missing; Error Message: No such subscription: 'sub_1RcqanDrMO37mudtKcH8n5JE'.
2025-6-22 11:18:38 - Updating membership #34525. New data: array (
  'status' => 'cancelled',
  'cancellation_date' => '2025-06-22 11:18:38',
).
2025-6-22 11:18:38 - Updating recurring status for membership #34525. Customer ID: 25789; Previous: true; New: false
2025-6-22 11:18:38 - Updating membership #34525. New data: array (
  'auto_renew' => 0,
).
2025-6-22 11:18:38 - Payment profile successfully cancelled for membership #34525.
2025-6-22 11:18:38 - Updating membership #34526. New data: array (
  'activated_date' => '2025-06-22 11:18:38',
).
2025-6-22 11:18:38 - Updating membership #34526. New data: array (
  'status' => 'active',
).
2025-6-22 11:18:38 - Removing old role subscriber, adding new role subscriber for membership #34526 (user ID #25850).
2025-6-22 11:18:38 - Active email sent to user #25850 for membership #34526.
2025-6-22 11:18:38 - Active email sent to admin(s) regarding membership #34526.
2025-6-22 11:18:38 - Payment Received email not sent to user #25850 for payment ID #134111 - message is empty or disabled.
2025-6-22 11:18:38 - Payment Received email not sent to admin(s) for payment ID #134111 - message is empty or disabled.
2025-6-22 11:18:38 - Updating membership #34526. New data: array (
  'times_billed' => 1,
).
2025-6-22 11:18:38 - Payment #134111 completed for member #25850 via Stripe gateway.
2025-6-22 11:18:39 - Stripe Gateway: Using newly calculated expiration for subscription start date: 2025-07-22 23:59:59
2025-6-22 11:18:39 - Stripe Gateway: Creating subscription with 1753215519 start date via trial_end.
2025-6-22 11:18:41 - Updating membership #34526. New data: array (
  'gateway_subscription_id' => 'sub_1RcqbMDrMO37mudtcuuwOxLW',
).
2025-6-22 11:19:06 - "Can cancel" status for membership #34526: true.
2025-6-22 11:19:06 - "Can cancel" status for membership #34526: true.
2025-6-22 11:19:26 - Adding 9.99 proration credits to registration for user #25850.
2025-6-22 11:19:27 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.22965092402464 (ID #9)
2025-6-22 11:19:27 - Adding 9.99 proration credits to registration for user #25850.
2025-6-22 11:19:27 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.22965092402464 (ID #9)
2025-6-22 11:19:27 - Adding 9.99 proration credits to registration for user #25850.
2025-6-22 11:21:23 - Adding 9.99 proration credits to registration for user #25850.
2025-6-22 11:21:24 - Adding 9.99 proration credits to registration for user #25850.
2025-6-22 11:21:24 - Adding 9.99 proration credits to registration for user #25850.
2025-6-22 11:21:46 - Adding 9.99 proration credits to registration for user #25850.
2025-6-22 11:21:46 - Started new registration for membership level #9 via stripe.
2025-6-22 11:21:47 - Adding 9.99 proration credits to registration for user #25850.
2025-6-22 11:21:47 - Started new registration for membership level #9 via stripe.
2025-6-22 11:21:47 - Registration type: downgrade.
2025-6-22 11:21:47 - Adding new membership. Data: array (
  'customer_id' => '25789',
  'user_id' => '25850',
  'object_id' => 9,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => '0.00',
  'recurring_amount' => 6.99,
  'created_date' => '2025-06-22 11:21:47',
  'expiration_date' => '2025-08-22 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => 'aadf316c8f22f51dc37f976f4956c439',
  'upgraded_from' => '34526',
)
2025-6-22 11:21:47 - Updating membership #34526. New data: array (
  'status' => 'active',
).
2025-6-22 11:21:47 - Updating membership #34527. New data: array (
  'status' => 'pending',
  'created_date' => 'July 22, 2025',
  'activated_date' => 'July 22, 2025',
).
2025-6-22 11:21:47 - New payment inserted. ID: 134112; User ID: 25850; Amount: 0.00; Subscription: Calculator with ChatDTC (100 Credits); Status: pending
2025-6-22 11:21:47 - Registration for user #25850 sent to gateway. Level ID: 9; Initial Amount: 0.00; Recurring Amount: 6.99; Auto Renew: true; Trial: false; Subscription Start: 2025-07-22 11:21:47; Membership ID: 34527
2025-6-22 11:21:47 - Updating membership #34527. New data: array (
  'gateway_customer_id' => 'cus_SXwPyRtjJhBZPP',
).
2025-6-22 11:21:49 - Using recovered payment #134112 for registration. Transaction type: downgrade.
2025-6-22 11:21:49 - Adding 9.99 proration credits to registration for user #25850.
2025-6-22 11:21:49 - Registration for user #25850 sent to gateway. Level ID: 9; Initial Amount: 0.00; Recurring Amount: 6.99; Auto Renew: true; Trial: false; Subscription Start: 2025-07-22 11:21:49; Membership ID: 34527
2025-6-22 11:21:50 - Updating payment #134112 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => '',
  'status' => 'complete',
)
2025-6-22 11:21:51 - Completing registration for customer #25789 via payment #134112.
2025-6-22 11:21:51 - Activating membership #34527.
2025-6-22 11:21:51 - Disabling all memberships for customer #25789 except: '34527'.
2025-6-22 11:21:51 - Disabling membership #34526.
2025-6-22 11:21:51 - Updating membership #34526. New data: array (
  'disabled' => 1,
).
2025-6-22 11:21:51 - "Can cancel" status for membership #34526: true.
2025-6-22 11:21:51 - "Can cancel" status for membership #34526: true.
2025-6-22 11:21:52 - Failed to cancel Stripe payment profile sub_1RcqbMDrMO37mudtcuuwOxLW. Error code: resource_missing; Error Message: No such subscription: 'sub_1RcqbMDrMO37mudtcuuwOxLW'.
2025-6-22 11:21:52 - Updating membership #34526. New data: array (
  'status' => 'cancelled',
  'cancellation_date' => '2025-06-22 11:21:52',
).
2025-6-22 11:21:52 - Updating recurring status for membership #34526. Customer ID: 25789; Previous: true; New: false
2025-6-22 11:21:52 - Updating membership #34526. New data: array (
  'auto_renew' => 0,
).
2025-6-22 11:21:52 - Payment profile successfully cancelled for membership #34526.
2025-6-22 11:21:52 - Updating membership #34527. New data: array (
  'status' => 'active',
).
2025-6-22 11:21:52 - Removing old role subscriber, adding new role subscriber for membership #34527 (user ID #25850).
2025-6-22 11:21:52 - Active email sent to user #25850 for membership #34527.
2025-6-22 11:21:52 - Active email sent to admin(s) regarding membership #34527.
2025-6-22 11:21:52 - Payment Received email not sent to user #25850 - payment amount is 0.
2025-6-22 11:21:52 - Updating membership #34527. New data: array (
  'times_billed' => 1,
).
2025-6-22 11:21:52 - Payment #134112 completed for member #25850 via Stripe gateway.
2025-6-22 11:21:54 - Stripe Gateway: Using subscription start date for subscription: 2025-07-22 11:21:49
2025-6-22 11:21:54 - Stripe Gateway: Creating subscription with 1753183309 start date via billing_cycle_anchor.
2025-6-22 11:21:55 - Updating membership #34527. New data: array (
  'gateway_subscription_id' => 'sub_1RcqeUDrMO37mudtRWBFnXWe',
).
2025-6-22 11:21:55 - Updating payment #134112 with new data: array (
  'transaction_id' => 'sub_1RcqeUDrMO37mudtRWBFnXWe',
)
2025-6-22 11:22:11 - "Can cancel" status for membership #34527: true.
2025-6-22 11:22:11 - "Can cancel" status for membership #34527: true.
2025-6-22 11:37:26 - Started new registration for membership level #9 via stripe.
2025-6-22 11:37:27 - Started new registration for membership level #9 via stripe.
2025-6-22 11:37:27 - Adding a new customer. Args: array (
  'date_registered' => '2025-06-22 11:37:27',
  'has_trialed' => 0,
  'user_id' => 25851,
)
2025-6-22 11:37:27 - Created new customer #25790.
2025-6-22 11:37:27 - Registration type: new.
2025-6-22 11:37:27 - Adding new membership. Data: array (
  'customer_id' => '25790',
  'user_id' => '25851',
  'object_id' => 9,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 6.99,
  'recurring_amount' => 6.99,
  'created_date' => '2025-06-22 11:37:27',
  'expiration_date' => '2025-07-22 23:59:59',
  'auto_renew' => 0,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '534c5701fe2541fa10bcd32586af8299',
)
2025-6-22 11:37:27 - New payment inserted. ID: 134113; User ID: 25851; Amount: 6.99; Subscription: Calculator with ChatDTC (100 Credits); Status: pending
2025-6-22 11:37:28 - Registration for user #25851 sent to gateway. Level ID: 9; Initial Amount: 6.99; Recurring Amount: 6.99; Auto Renew: false; Trial: false; Subscription Start: ; Membership ID: 34528
2025-6-22 11:37:28 - Updating membership #34528. New data: array (
  'gateway_customer_id' => 'cus_SXwnLZxej3hpph',
).
2025-6-22 11:37:32 - Using recovered payment #134113 for registration. Transaction type: new.
2025-6-22 11:37:32 - Registration for user #25851 sent to gateway. Level ID: 9; Initial Amount: 6.99; Recurring Amount: 6.99; Auto Renew: false; Trial: false; Subscription Start: ; Membership ID: 34528
2025-6-22 11:37:33 - Updating payment #134113 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3RcqtZDrMO37mudt31KsANDT',
  'status' => 'complete',
)
2025-6-22 11:37:33 - Completing registration for customer #25790 via payment #134113.
2025-6-22 11:37:33 - Activating membership #34528.
2025-6-22 11:37:33 - Disabling all memberships for customer #25790 except: '34528'.
2025-6-22 11:37:33 - Updating membership #34528. New data: array (
  'activated_date' => '2025-06-22 11:37:33',
).
2025-6-22 11:37:33 - Updating membership #34528. New data: array (
  'status' => 'active',
).
2025-6-22 11:37:33 - Active email sent to user #25851 for membership #34528.
2025-6-22 11:37:33 - Active email sent to admin(s) regarding membership #34528.
2025-6-22 11:37:33 - Payment Received email not sent to user #25851 for payment ID #134113 - message is empty or disabled.
2025-6-22 11:37:33 - Payment Received email not sent to admin(s) for payment ID #134113 - message is empty or disabled.
2025-6-22 11:37:33 - Updating membership #34528. New data: array (
  'times_billed' => 1,
).
2025-6-22 11:37:33 - Payment #134113 completed for member #25851 via Stripe gateway.
2025-6-22 11:38:08 - "Can cancel" status for membership #34528: false. Reason: membership not recurring.
2025-6-22 11:38:08 - "Can cancel" status for membership #34528: false. Reason: membership not recurring.
2025-6-22 11:38:23 - Adding 6.99 proration credits to registration for user #25851.
2025-6-22 11:38:24 - Old price per day: 0.22965092402464 (ID #9); New price per day: 0.3282135523614 (ID #12)
2025-6-22 11:38:24 - Adding 6.99 proration credits to registration for user #25851.
2025-6-22 11:38:24 - Old price per day: 0.22965092402464 (ID #9); New price per day: 0.3282135523614 (ID #12)
2025-6-22 11:38:24 - Adding 6.99 proration credits to registration for user #25851.
2025-6-22 11:38:31 - Old price per day: 0.22965092402464 (ID #9); New price per day: 0.082108145106092 (ID #11)
2025-6-22 11:38:31 - Adding 6.99 proration credits to registration for user #25851.
2025-6-22 11:38:31 - Old price per day: 0.22965092402464 (ID #9); New price per day: 0.082108145106092 (ID #11)
2025-6-22 11:38:31 - Adding 6.99 proration credits to registration for user #25851.
2025-6-22 11:38:35 - Old price per day: 0.22965092402464 (ID #9); New price per day: 0.16394250513347 (ID #7)
2025-6-22 11:38:35 - Adding 6.99 proration credits to registration for user #25851.
2025-6-22 11:38:35 - Old price per day: 0.22965092402464 (ID #9); New price per day: 0.16394250513347 (ID #7)
2025-6-22 11:38:35 - Adding 6.99 proration credits to registration for user #25851.
2025-6-22 11:38:38 - Old price per day: 0.22965092402464 (ID #9); New price per day: 0.3282135523614 (ID #12)
2025-6-22 11:38:38 - Adding 6.99 proration credits to registration for user #25851.
2025-6-22 11:38:38 - Old price per day: 0.22965092402464 (ID #9); New price per day: 0.3282135523614 (ID #12)
2025-6-22 11:38:38 - Adding 6.99 proration credits to registration for user #25851.
2025-6-22 11:38:49 - Old price per day: 0.22965092402464 (ID #9); New price per day: 0.3282135523614 (ID #12)
2025-6-22 11:38:49 - Adding 6.99 proration credits to registration for user #25851.
2025-6-22 11:38:49 - Started new registration for membership level #12 via stripe.
2025-6-22 11:38:50 - Old price per day: 0.22965092402464 (ID #9); New price per day: 0.3282135523614 (ID #12)
2025-6-22 11:38:50 - Adding 6.99 proration credits to registration for user #25851.
2025-6-22 11:38:50 - Started new registration for membership level #12 via stripe.
2025-6-22 11:38:50 - Registration type: upgrade.
2025-6-22 11:38:50 - Adding new membership. Data: array (
  'customer_id' => '25790',
  'user_id' => '25851',
  'object_id' => 12,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 3.0,
  'recurring_amount' => 9.99,
  'created_date' => '2025-06-22 11:38:50',
  'expiration_date' => '2025-07-22 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '9e8e32e47e1e1d2a4a390b391ed46091',
  'upgraded_from' => '34528',
)
2025-6-22 11:38:50 - New payment inserted. ID: 134114; User ID: 25851; Amount: 3.00; Subscription: Calculator with ChatDTC (Unlimited Credits); Status: pending
2025-6-22 11:38:50 - Registration for user #25851 sent to gateway. Level ID: 12; Initial Amount: 3.00; Recurring Amount: 9.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34529
2025-6-22 11:38:51 - Updating membership #34529. New data: array (
  'gateway_customer_id' => 'cus_SXwnLZxej3hpph',
).
2025-6-22 11:38:53 - Old price per day: 0.22965092402464 (ID #9); New price per day: 0.3282135523614 (ID #12)
2025-6-22 11:38:53 - Using recovered payment #134114 for registration. Transaction type: upgrade.
2025-6-22 11:38:53 - Adding 6.99 proration credits to registration for user #25851.
2025-6-22 11:38:53 - Registration for user #25851 sent to gateway. Level ID: 12; Initial Amount: 3.00; Recurring Amount: 9.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34529
2025-6-22 11:38:55 - Updating payment #134114 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3RcqutDrMO37mudt2QjbdQb7',
  'status' => 'complete',
)
2025-6-22 11:38:55 - Completing registration for customer #25790 via payment #134114.
2025-6-22 11:38:55 - Activating membership #34529.
2025-6-22 11:38:55 - Disabling all memberships for customer #25790 except: '34529'.
2025-6-22 11:38:55 - Disabling membership #34528.
2025-6-22 11:38:55 - Updating membership #34528. New data: array (
  'disabled' => 1,
).
2025-6-22 11:38:55 - "Can cancel" status for membership #34528: false. Reason: membership not recurring.
2025-6-22 11:38:55 - Updating membership #34529. New data: array (
  'activated_date' => '2025-06-22 11:38:55',
).
2025-6-22 11:38:55 - Updating membership #34529. New data: array (
  'status' => 'active',
).
2025-6-22 11:38:55 - Removing old role subscriber, adding new role subscriber for membership #34529 (user ID #25851).
2025-6-22 11:38:55 - Active email sent to user #25851 for membership #34529.
2025-6-22 11:38:55 - Active email sent to admin(s) regarding membership #34529.
2025-6-22 11:38:55 - Payment Received email not sent to user #25851 for payment ID #134114 - message is empty or disabled.
2025-6-22 11:38:55 - Payment Received email not sent to admin(s) for payment ID #134114 - message is empty or disabled.
2025-6-22 11:38:55 - Updating membership #34529. New data: array (
  'times_billed' => 1,
).
2025-6-22 11:38:55 - Payment #134114 completed for member #25851 via Stripe gateway.
2025-6-22 11:38:56 - Stripe Gateway: Using newly calculated expiration for subscription start date: 2025-07-22 23:59:59
2025-6-22 11:38:56 - Stripe Gateway: Creating subscription with 1753216736 start date via trial_end.
2025-6-22 11:38:58 - Updating membership #34529. New data: array (
  'gateway_subscription_id' => 'sub_1RcquzDrMO37mudtAzXlBRlT',
).
2025-6-22 11:39:16 - "Can cancel" status for membership #34529: true.
2025-6-22 11:39:16 - "Can cancel" status for membership #34529: true.
2025-6-22 11:40:08 - Adding 9.99 proration credits to registration for user #25851.
2025-6-22 11:40:10 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.22965092402464 (ID #9)
2025-6-22 11:40:10 - Adding 9.99 proration credits to registration for user #25851.
2025-6-22 11:40:10 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.22965092402464 (ID #9)
2025-6-22 11:40:10 - Adding 9.99 proration credits to registration for user #25851.
2025-6-22 11:40:25 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.22965092402464 (ID #9)
2025-6-22 11:40:25 - Adding 9.99 proration credits to registration for user #25851.
2025-6-22 11:40:25 - Started new registration for membership level #9 via stripe.
2025-6-22 11:40:25 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.22965092402464 (ID #9)
2025-6-22 11:40:25 - Adding 9.99 proration credits to registration for user #25851.
2025-6-22 11:40:25 - Started new registration for membership level #9 via stripe.
2025-6-22 11:40:25 - Registration type: downgrade.
2025-6-22 11:40:25 - Adding new membership. Data: array (
  'customer_id' => '25790',
  'user_id' => '25851',
  'object_id' => 9,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => '0.00',
  'recurring_amount' => 6.99,
  'created_date' => '2025-06-22 11:40:25',
  'expiration_date' => '2025-08-22 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => 'f902877d16e6a1fb630a65fee6af6280',
  'upgraded_from' => '34529',
)
2025-6-22 11:40:25 - New payment inserted. ID: 134115; User ID: 25851; Amount: 0.00; Subscription: Calculator with ChatDTC (100 Credits); Status: pending
2025-6-22 11:40:26 - Registration for user #25851 sent to gateway. Level ID: 9; Initial Amount: 0.00; Recurring Amount: 6.99; Auto Renew: true; Trial: false; Subscription Start: 2025-07-22 11:40:26; Membership ID: 34530
2025-6-22 11:40:26 - Updating membership #34530. New data: array (
  'gateway_customer_id' => 'cus_SXwnLZxej3hpph',
).
2025-6-22 11:40:28 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.22965092402464 (ID #9)
2025-6-22 11:40:28 - Using recovered payment #134115 for registration. Transaction type: downgrade.
2025-6-22 11:40:28 - Adding 9.99 proration credits to registration for user #25851.
2025-6-22 11:40:28 - Registration for user #25851 sent to gateway. Level ID: 9; Initial Amount: 0.00; Recurring Amount: 6.99; Auto Renew: true; Trial: false; Subscription Start: 2025-07-22 11:40:28; Membership ID: 34530
2025-6-22 11:40:29 - Updating payment #134115 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => '',
  'status' => 'complete',
)
2025-6-22 11:40:29 - Completing registration for customer #25790 via payment #134115.
2025-6-22 11:40:29 - Activating membership #34530.
2025-6-22 11:40:29 - Disabling all memberships for customer #25790 except: '34530'.
2025-6-22 11:40:29 - Disabling membership #34529.
2025-6-22 11:40:29 - Updating membership #34529. New data: array (
  'disabled' => 1,
).
2025-6-22 11:40:29 - "Can cancel" status for membership #34529: true.
2025-6-22 11:40:29 - "Can cancel" status for membership #34529: true.
2025-6-22 11:40:31 - Failed to cancel Stripe payment profile sub_1RcquzDrMO37mudtAzXlBRlT. Error code: resource_missing; Error Message: No such subscription: 'sub_1RcquzDrMO37mudtAzXlBRlT'.
2025-6-22 11:40:31 - Updating membership #34529. New data: array (
  'status' => 'cancelled',
  'cancellation_date' => '2025-06-22 11:40:31',
).
2025-6-22 11:40:31 - Updating recurring status for membership #34529. Customer ID: 25790; Previous: true; New: false
2025-6-22 11:40:31 - Updating membership #34529. New data: array (
  'auto_renew' => 0,
).
2025-6-22 11:40:31 - Payment profile successfully cancelled for membership #34529.
2025-6-22 11:40:31 - Updating membership #34530. New data: array (
  'activated_date' => '2025-06-22 11:40:31',
).
2025-6-22 11:40:31 - Updating membership #34530. New data: array (
  'status' => 'active',
).
2025-6-22 11:40:31 - Removing old role subscriber, adding new role subscriber for membership #34530 (user ID #25851).
2025-6-22 11:40:31 - Active email sent to user #25851 for membership #34530.
2025-6-22 11:40:31 - Active email sent to admin(s) regarding membership #34530.
2025-6-22 11:40:31 - Payment Received email not sent to user #25851 - payment amount is 0.
2025-6-22 11:40:31 - Updating membership #34530. New data: array (
  'times_billed' => 1,
).
2025-6-22 11:40:31 - Payment #134115 completed for member #25851 via Stripe gateway.
2025-6-22 11:40:33 - Stripe Gateway: Using subscription start date for subscription: 2025-07-22 11:40:28
2025-6-22 11:40:33 - Stripe Gateway: Creating subscription with 1753184428 start date via billing_cycle_anchor.
2025-6-22 11:40:34 - Updating membership #34530. New data: array (
  'gateway_subscription_id' => 'sub_1RcqwXDrMO37mudtrz9eYrU0',
).
2025-6-22 11:40:34 - Updating payment #134115 with new data: array (
  'transaction_id' => 'sub_1RcqwXDrMO37mudtrz9eYrU0',
)
2025-6-22 11:40:45 - "Can cancel" status for membership #34530: true.
2025-6-22 11:40:45 - "Can cancel" status for membership #34530: true.
2025-6-22 11:42:26 - Adding 6.99 proration credits to registration for user #25851.
2025-6-22 11:42:28 - Old price per day: 0.22965092402464 (ID #9); New price per day: 0.3282135523614 (ID #12)
2025-6-22 11:42:28 - Adding 6.99 proration credits to registration for user #25851.
2025-6-22 11:42:28 - Old price per day: 0.22965092402464 (ID #9); New price per day: 0.3282135523614 (ID #12)
2025-6-22 11:42:28 - Adding 6.99 proration credits to registration for user #25851.
2025-6-22 11:42:34 - Old price per day: 0.22965092402464 (ID #9); New price per day: 0.16394250513347 (ID #7)
2025-6-22 11:42:34 - Adding 6.99 proration credits to registration for user #25851.
2025-6-22 11:42:34 - Old price per day: 0.22965092402464 (ID #9); New price per day: 0.16394250513347 (ID #7)
2025-6-22 11:42:34 - Adding 6.99 proration credits to registration for user #25851.
2025-6-22 11:42:44 - Old price per day: 0.22965092402464 (ID #9); New price per day: 0.16394250513347 (ID #7)
2025-6-22 11:42:44 - Adding 6.99 proration credits to registration for user #25851.
2025-6-22 11:42:44 - Started new registration for membership level #7 via stripe.
2025-6-22 11:42:44 - Old price per day: 0.22965092402464 (ID #9); New price per day: 0.16394250513347 (ID #7)
2025-6-22 11:42:44 - Adding 6.99 proration credits to registration for user #25851.
2025-6-22 11:42:44 - Started new registration for membership level #7 via stripe.
2025-6-22 11:42:44 - Registration type: downgrade.
2025-6-22 11:42:44 - Adding new membership. Data: array (
  'customer_id' => '25790',
  'user_id' => '25851',
  'object_id' => 7,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => '0.00',
  'recurring_amount' => 4.99,
  'created_date' => '2025-06-22 11:42:44',
  'expiration_date' => '2025-08-22 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '9ea54a6cdc14f8c5c156fa0278a3a740',
  'upgraded_from' => '34530',
)
2025-6-22 11:42:44 - New payment inserted. ID: 134116; User ID: 25851; Amount: 0.00; Subscription: Calculator with ChatDTC (50 Credits); Status: pending
2025-6-22 11:42:44 - Registration for user #25851 sent to gateway. Level ID: 7; Initial Amount: 0.00; Recurring Amount: 4.99; Auto Renew: true; Trial: false; Subscription Start: 2025-07-22 11:42:44; Membership ID: 34531
2025-6-22 11:42:45 - Updating membership #34531. New data: array (
  'gateway_customer_id' => 'cus_SXwnLZxej3hpph',
).
2025-6-22 11:42:47 - Old price per day: 0.22965092402464 (ID #9); New price per day: 0.16394250513347 (ID #7)
2025-6-22 11:42:47 - Using recovered payment #134116 for registration. Transaction type: downgrade.
2025-6-22 11:42:47 - Adding 6.99 proration credits to registration for user #25851.
2025-6-22 11:42:47 - Registration for user #25851 sent to gateway. Level ID: 7; Initial Amount: 0.00; Recurring Amount: 4.99; Auto Renew: true; Trial: false; Subscription Start: 2025-07-22 11:42:47; Membership ID: 34531
2025-6-22 11:42:48 - Updating payment #134116 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => '',
  'status' => 'complete',
)
2025-6-22 11:42:48 - Completing registration for customer #25790 via payment #134116.
2025-6-22 11:42:48 - Activating membership #34531.
2025-6-22 11:42:48 - Disabling all memberships for customer #25790 except: '34531'.
2025-6-22 11:42:48 - Disabling membership #34530.
2025-6-22 11:42:48 - Updating membership #34530. New data: array (
  'disabled' => 1,
).
2025-6-22 11:42:48 - "Can cancel" status for membership #34530: true.
2025-6-22 11:42:48 - "Can cancel" status for membership #34530: true.
2025-6-22 11:42:50 - Failed to cancel Stripe payment profile sub_1RcqwXDrMO37mudtrz9eYrU0. Error code: resource_missing; Error Message: No such subscription: 'sub_1RcqwXDrMO37mudtrz9eYrU0'.
2025-6-22 11:42:50 - Updating membership #34530. New data: array (
  'status' => 'cancelled',
  'cancellation_date' => '2025-06-22 11:42:50',
).
2025-6-22 11:42:50 - Updating recurring status for membership #34530. Customer ID: 25790; Previous: true; New: false
2025-6-22 11:42:50 - Updating membership #34530. New data: array (
  'auto_renew' => 0,
).
2025-6-22 11:42:50 - Payment profile successfully cancelled for membership #34530.
2025-6-22 11:42:50 - Updating membership #34531. New data: array (
  'activated_date' => '2025-06-22 11:42:50',
).
2025-6-22 11:42:50 - Updating membership #34531. New data: array (
  'status' => 'active',
).
2025-6-22 11:42:50 - Removing old role subscriber, adding new role subscriber for membership #34531 (user ID #25851).
2025-6-22 11:42:50 - Active email sent to user #25851 for membership #34531.
2025-6-22 11:42:50 - Active email sent to admin(s) regarding membership #34531.
2025-6-22 11:42:50 - Payment Received email not sent to user #25851 - payment amount is 0.
2025-6-22 11:42:50 - Updating membership #34531. New data: array (
  'times_billed' => 1,
).
2025-6-22 11:42:50 - Payment #134116 completed for member #25851 via Stripe gateway.
2025-6-22 11:42:52 - Stripe Gateway: Using subscription start date for subscription: 2025-07-22 11:42:47
2025-6-22 11:42:52 - Stripe Gateway: Creating subscription with 1753184567 start date via billing_cycle_anchor.
2025-6-22 11:42:53 - Updating membership #34531. New data: array (
  'gateway_subscription_id' => 'sub_1RcqymDrMO37mudtRhuqd2zP',
).
2025-6-22 11:42:53 - Updating payment #134116 with new data: array (
  'transaction_id' => 'sub_1RcqymDrMO37mudtRhuqd2zP',
)
2025-6-22 11:44:20 - "Can cancel" status for membership #34531: true.
2025-6-22 11:44:20 - "Can cancel" status for membership #34531: true.
2025-6-22 11:55:30 - Started new registration for membership level #9 via stripe.
2025-6-22 11:55:30 - Registration cancelled with the following errors: The discount you entered is invalid.
2025-6-22 11:55:40 - Started new registration for membership level #9 via stripe.
2025-6-22 11:55:41 - Started new registration for membership level #9 via stripe.
2025-6-22 11:55:41 - Adding a new customer. Args: array (
  'date_registered' => '2025-06-22 11:55:41',
  'has_trialed' => 0,
  'user_id' => 25852,
)
2025-6-22 11:55:41 - Created new customer #25791.
2025-6-22 11:55:41 - Registration type: new.
2025-6-22 11:55:41 - Adding new membership. Data: array (
  'customer_id' => '25791',
  'user_id' => '25852',
  'object_id' => 9,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 6.99,
  'recurring_amount' => 6.99,
  'created_date' => '2025-06-22 11:55:41',
  'expiration_date' => '2025-07-22 23:59:59',
  'auto_renew' => 0,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => 'c43ba15b939f178d7203dcc7ce2c015d',
)
2025-6-22 11:55:41 - New payment inserted. ID: 134117; User ID: 25852; Amount: 6.99; Subscription: Calculator with ChatDTC (100 Credits); Status: pending
2025-6-22 11:55:42 - Registration for user #25852 sent to gateway. Level ID: 9; Initial Amount: 6.99; Recurring Amount: 6.99; Auto Renew: false; Trial: false; Subscription Start: ; Membership ID: 34532
2025-6-22 11:55:42 - Updating membership #34532. New data: array (
  'gateway_customer_id' => 'cus_SXx5d4fRAUYBPS',
).
2025-6-22 11:55:45 - Using recovered payment #134117 for registration. Transaction type: new.
2025-6-22 11:55:45 - Registration for user #25852 sent to gateway. Level ID: 9; Initial Amount: 6.99; Recurring Amount: 6.99; Auto Renew: false; Trial: false; Subscription Start: ; Membership ID: 34532
2025-6-22 11:55:47 - Updating payment #134117 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3RcrBDDrMO37mudt1g8ZIQMG',
  'status' => 'complete',
)
2025-6-22 11:55:47 - Completing registration for customer #25791 via payment #134117.
2025-6-22 11:55:47 - Activating membership #34532.
2025-6-22 11:55:47 - Disabling all memberships for customer #25791 except: '34532'.
2025-6-22 11:55:47 - Updating membership #34532. New data: array (
  'activated_date' => '2025-06-22 11:55:47',
).
2025-6-22 11:55:47 - Updating membership #34532. New data: array (
  'status' => 'active',
).
2025-6-22 11:55:47 - Active email sent to user #25852 for membership #34532.
2025-6-22 11:55:47 - Active email sent to admin(s) regarding membership #34532.
2025-6-22 11:55:47 - Payment Received email not sent to user #25852 for payment ID #134117 - message is empty or disabled.
2025-6-22 11:55:47 - Payment Received email not sent to admin(s) for payment ID #134117 - message is empty or disabled.
2025-6-22 11:55:47 - Updating membership #34532. New data: array (
  'times_billed' => 1,
).
2025-6-22 11:55:47 - Payment #134117 completed for member #25852 via Stripe gateway.
2025-6-22 11:56:00 - "Can cancel" status for membership #34532: false. Reason: membership not recurring.
2025-6-22 11:56:00 - "Can cancel" status for membership #34532: false. Reason: membership not recurring.
2025-6-22 11:56:26 - Adding 6.99 proration credits to registration for user #25852.
2025-6-22 11:56:27 - Old price per day: 0.22965092402464 (ID #9); New price per day: 0.3282135523614 (ID #12)
2025-6-22 11:56:27 - Adding 6.99 proration credits to registration for user #25852.
2025-6-22 11:56:27 - Old price per day: 0.22965092402464 (ID #9); New price per day: 0.3282135523614 (ID #12)
2025-6-22 11:56:27 - Adding 6.99 proration credits to registration for user #25852.
2025-6-22 11:56:32 - Old price per day: 0.22965092402464 (ID #9); New price per day: 0.3282135523614 (ID #12)
2025-6-22 11:56:32 - Adding 6.99 proration credits to registration for user #25852.
2025-6-22 11:56:32 - Started new registration for membership level #12 via stripe.
2025-6-22 11:56:33 - Old price per day: 0.22965092402464 (ID #9); New price per day: 0.3282135523614 (ID #12)
2025-6-22 11:56:33 - Adding 6.99 proration credits to registration for user #25852.
2025-6-22 11:56:33 - Started new registration for membership level #12 via stripe.
2025-6-22 11:56:33 - Registration type: upgrade.
2025-6-22 11:56:33 - Adding new membership. Data: array (
  'customer_id' => '25791',
  'user_id' => '25852',
  'object_id' => 12,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 3.0,
  'recurring_amount' => 9.99,
  'created_date' => '2025-06-22 11:56:33',
  'expiration_date' => '2025-07-22 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '548192d845e6459fd07d06c88797a987',
  'upgraded_from' => '34532',
)
2025-6-22 11:56:33 - New payment inserted. ID: 134118; User ID: 25852; Amount: 3.00; Subscription: Calculator with ChatDTC (Unlimited Credits); Status: pending
2025-6-22 11:56:33 - Registration for user #25852 sent to gateway. Level ID: 12; Initial Amount: 3.00; Recurring Amount: 9.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34533
2025-6-22 11:56:34 - Updating membership #34533. New data: array (
  'gateway_customer_id' => 'cus_SXx5d4fRAUYBPS',
).
2025-6-22 11:56:36 - Old price per day: 0.22965092402464 (ID #9); New price per day: 0.3282135523614 (ID #12)
2025-6-22 11:56:36 - Using recovered payment #134118 for registration. Transaction type: upgrade.
2025-6-22 11:56:36 - Adding 6.99 proration credits to registration for user #25852.
2025-6-22 11:56:36 - Registration for user #25852 sent to gateway. Level ID: 12; Initial Amount: 3.00; Recurring Amount: 9.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34533
2025-6-22 11:56:37 - Updating payment #134118 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3RcrC2DrMO37mudt1ovEs1O3',
  'status' => 'complete',
)
2025-6-22 11:56:37 - Completing registration for customer #25791 via payment #134118.
2025-6-22 11:56:37 - Activating membership #34533.
2025-6-22 11:56:37 - Disabling all memberships for customer #25791 except: '34533'.
2025-6-22 11:56:37 - Disabling membership #34532.
2025-6-22 11:56:37 - Updating membership #34532. New data: array (
  'disabled' => 1,
).
2025-6-22 11:56:37 - "Can cancel" status for membership #34532: false. Reason: membership not recurring.
2025-6-22 11:56:37 - Updating membership #34533. New data: array (
  'activated_date' => '2025-06-22 11:56:37',
).
2025-6-22 11:56:37 - Updating membership #34533. New data: array (
  'status' => 'active',
).
2025-6-22 11:56:37 - Removing old role subscriber, adding new role subscriber for membership #34533 (user ID #25852).
2025-6-22 11:56:37 - Active email sent to user #25852 for membership #34533.
2025-6-22 11:56:37 - Active email sent to admin(s) regarding membership #34533.
2025-6-22 11:56:37 - Payment Received email not sent to user #25852 for payment ID #134118 - message is empty or disabled.
2025-6-22 11:56:37 - Payment Received email not sent to admin(s) for payment ID #134118 - message is empty or disabled.
2025-6-22 11:56:37 - Updating membership #34533. New data: array (
  'times_billed' => 1,
).
2025-6-22 11:56:37 - Payment #134118 completed for member #25852 via Stripe gateway.
2025-6-22 11:56:39 - Stripe Gateway: Using newly calculated expiration for subscription start date: 2025-07-22 23:59:59
2025-6-22 11:56:39 - Stripe Gateway: Creating subscription with 1753217799 start date via trial_end.
2025-6-22 11:56:41 - Updating membership #34533. New data: array (
  'gateway_subscription_id' => 'sub_1RcrC7DrMO37mudtRJnb4AzW',
).
2025-6-22 11:56:52 - "Can cancel" status for membership #34533: true.
2025-6-22 11:56:52 - "Can cancel" status for membership #34533: true.
2025-6-22 11:57:07 - Adding 9.99 proration credits to registration for user #25852.
2025-6-22 11:57:08 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.22965092402464 (ID #9)
2025-6-22 11:57:08 - Adding 9.99 proration credits to registration for user #25852.
2025-6-22 11:57:08 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.22965092402464 (ID #9)
2025-6-22 11:57:08 - Adding 9.99 proration credits to registration for user #25852.
2025-6-22 11:57:16 - Adding 9.99 proration credits to registration for user #25852.
2025-6-22 11:57:17 - Adding 9.99 proration credits to registration for user #25852.
2025-6-22 11:57:17 - Adding 9.99 proration credits to registration for user #25852.
2025-6-22 11:57:23 - Adding 9.99 proration credits to registration for user #25852.
2025-6-22 11:57:23 - Adding 9.99 proration credits to registration for user #25852.
2025-6-22 11:57:29 - Adding 9.99 proration credits to registration for user #25852.
2025-6-22 11:57:29 - Started new registration for membership level #7 via stripe.
2025-6-22 11:57:30 - Adding 9.99 proration credits to registration for user #25852.
2025-6-22 11:57:30 - Started new registration for membership level #7 via stripe.
2025-6-22 11:57:30 - Registration type: downgrade.
2025-6-22 11:57:30 - Adding new membership. Data: array (
  'customer_id' => '25791',
  'user_id' => '25852',
  'object_id' => 7,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => '0.00',
  'recurring_amount' => 4.99,
  'created_date' => '2025-06-22 11:57:30',
  'expiration_date' => '2025-09-22 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => 'e5f6eab5de44eab71f1e8bd0f3ebe676',
  'upgraded_from' => '34533',
)
2025-6-22 11:57:30 - New payment inserted. ID: 134119; User ID: 25852; Amount: 0.00; Subscription: Calculator with ChatDTC (50 Credits); Status: pending
2025-6-22 11:57:30 - Registration for user #25852 sent to gateway. Level ID: 7; Initial Amount: 0.00; Recurring Amount: 4.99; Auto Renew: true; Trial: false; Subscription Start: 2025-07-22 11:57:30; Membership ID: 34534
2025-6-22 11:57:31 - Updating membership #34534. New data: array (
  'gateway_customer_id' => 'cus_SXx5d4fRAUYBPS',
).
2025-6-22 11:57:32 - Using recovered payment #134119 for registration. Transaction type: downgrade.
2025-6-22 11:57:32 - Adding 9.99 proration credits to registration for user #25852.
2025-6-22 11:57:32 - Registration for user #25852 sent to gateway. Level ID: 7; Initial Amount: 0.00; Recurring Amount: 4.99; Auto Renew: true; Trial: false; Subscription Start: 2025-07-22 11:57:32; Membership ID: 34534
2025-6-22 11:57:34 - Updating payment #134119 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => '',
  'status' => 'complete',
)
2025-6-22 11:57:34 - Completing registration for customer #25791 via payment #134119.
2025-6-22 11:57:34 - Activating membership #34534.
2025-6-22 11:57:34 - Disabling all memberships for customer #25791 except: '34534'.
2025-6-22 11:57:34 - Disabling membership #34533.
2025-6-22 11:57:34 - Updating membership #34533. New data: array (
  'disabled' => 1,
).
2025-6-22 11:57:34 - "Can cancel" status for membership #34533: true.
2025-6-22 11:57:34 - "Can cancel" status for membership #34533: true.
2025-6-22 11:57:35 - Failed to cancel Stripe payment profile sub_1RcrC7DrMO37mudtRJnb4AzW. Error code: resource_missing; Error Message: No such subscription: 'sub_1RcrC7DrMO37mudtRJnb4AzW'.
2025-6-22 11:57:35 - Updating membership #34533. New data: array (
  'status' => 'cancelled',
  'cancellation_date' => '2025-06-22 11:57:35',
).
2025-6-22 11:57:35 - Updating recurring status for membership #34533. Customer ID: 25791; Previous: true; New: false
2025-6-22 11:57:35 - Updating membership #34533. New data: array (
  'auto_renew' => 0,
).
2025-6-22 11:57:35 - Payment profile successfully cancelled for membership #34533.
2025-6-22 11:57:35 - Updating membership #34534. New data: array (
  'activated_date' => '2025-06-22 11:57:35',
).
2025-6-22 11:57:35 - Updating membership #34534. New data: array (
  'status' => 'active',
).
2025-6-22 11:57:35 - Removing old role subscriber, adding new role subscriber for membership #34534 (user ID #25852).
2025-6-22 11:57:35 - Active email sent to user #25852 for membership #34534.
2025-6-22 11:57:35 - Active email sent to admin(s) regarding membership #34534.
2025-6-22 11:57:35 - Payment Received email not sent to user #25852 - payment amount is 0.
2025-6-22 11:57:35 - Updating membership #34534. New data: array (
  'times_billed' => 1,
).
2025-6-22 11:57:35 - Payment #134119 completed for member #25852 via Stripe gateway.
2025-6-22 11:57:37 - Stripe Gateway: Using subscription start date for subscription: 2025-07-22 11:57:32
2025-6-22 11:57:37 - Stripe Gateway: Creating subscription with 1753185452 start date via billing_cycle_anchor.
2025-6-22 11:57:38 - Updating membership #34534. New data: array (
  'gateway_subscription_id' => 'sub_1RcrD3DrMO37mudt9w9ApbRS',
).
2025-6-22 11:57:38 - Updating payment #134119 with new data: array (
  'transaction_id' => 'sub_1RcrD3DrMO37mudt9w9ApbRS',
)
2025-6-22 11:57:50 - "Can cancel" status for membership #34534: true.
2025-6-22 11:57:50 - "Can cancel" status for membership #34534: true.
2025-6-22 11:58:10 - Adding 4.99 proration credits to registration for user #25852.
2025-6-22 11:58:11 - Old price per day: 0.16394250513347 (ID #7); New price per day: 0.3282135523614 (ID #12)
2025-6-22 11:58:11 - Adding 4.99 proration credits to registration for user #25852.
2025-6-22 11:58:11 - Old price per day: 0.16394250513347 (ID #7); New price per day: 0.3282135523614 (ID #12)
2025-6-22 11:58:11 - Adding 4.99 proration credits to registration for user #25852.
2025-6-22 11:58:15 - Old price per day: 0.16394250513347 (ID #7); New price per day: 0.0982340862423 (ID #10)
2025-6-22 11:58:15 - Adding 4.99 proration credits to registration for user #25852.
2025-6-22 11:58:15 - Old price per day: 0.16394250513347 (ID #7); New price per day: 0.0982340862423 (ID #10)
2025-6-22 11:58:15 - Adding 4.99 proration credits to registration for user #25852.
2025-6-22 11:58:21 - Old price per day: 0.16394250513347 (ID #7); New price per day: 0.0982340862423 (ID #10)
2025-6-22 11:58:21 - Adding 4.99 proration credits to registration for user #25852.
2025-6-22 11:58:21 - Started new registration for membership level #10 via stripe.
2025-6-22 11:58:22 - Old price per day: 0.16394250513347 (ID #7); New price per day: 0.0982340862423 (ID #10)
2025-6-22 11:58:22 - Adding 4.99 proration credits to registration for user #25852.
2025-6-22 11:58:22 - Started new registration for membership level #10 via stripe.
2025-6-22 11:58:22 - Registration type: downgrade.
2025-6-22 11:58:22 - Adding new membership. Data: array (
  'customer_id' => '25791',
  'user_id' => '25852',
  'object_id' => 10,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => '0.00',
  'recurring_amount' => 2.99,
  'created_date' => '2025-06-22 11:58:22',
  'expiration_date' => '2025-08-22 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => 'c53946b95cc51aeb605c7d7ff37a3091',
  'upgraded_from' => '34534',
)
2025-6-22 11:58:22 - New payment inserted. ID: 134120; User ID: 25852; Amount: 0.00; Subscription: Dynasty Trade Calculator (Monthly); Status: pending
2025-6-22 11:58:22 - Registration for user #25852 sent to gateway. Level ID: 10; Initial Amount: 0.00; Recurring Amount: 2.99; Auto Renew: true; Trial: false; Subscription Start: 2025-07-22 11:58:22; Membership ID: 34535
2025-6-22 11:58:23 - Updating membership #34535. New data: array (
  'gateway_customer_id' => 'cus_SXx5d4fRAUYBPS',
).
2025-6-22 11:58:24 - Old price per day: 0.16394250513347 (ID #7); New price per day: 0.0982340862423 (ID #10)
2025-6-22 11:58:24 - Using recovered payment #134120 for registration. Transaction type: downgrade.
2025-6-22 11:58:24 - Adding 4.99 proration credits to registration for user #25852.
2025-6-22 11:58:24 - Registration for user #25852 sent to gateway. Level ID: 10; Initial Amount: 0.00; Recurring Amount: 2.99; Auto Renew: true; Trial: false; Subscription Start: 2025-07-22 11:58:24; Membership ID: 34535
2025-6-22 11:58:26 - Updating payment #134120 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => '',
  'status' => 'complete',
)
2025-6-22 11:58:26 - Completing registration for customer #25791 via payment #134120.
2025-6-22 11:58:26 - Activating membership #34535.
2025-6-22 11:58:26 - Disabling all memberships for customer #25791 except: '34535'.
2025-6-22 11:58:26 - Disabling membership #34534.
2025-6-22 11:58:26 - Updating membership #34534. New data: array (
  'disabled' => 1,
).
2025-6-22 11:58:26 - "Can cancel" status for membership #34534: true.
2025-6-22 11:58:26 - "Can cancel" status for membership #34534: true.
2025-6-22 11:58:27 - Failed to cancel Stripe payment profile sub_1RcrD3DrMO37mudt9w9ApbRS. Error code: resource_missing; Error Message: No such subscription: 'sub_1RcrD3DrMO37mudt9w9ApbRS'.
2025-6-22 11:58:27 - Updating membership #34534. New data: array (
  'status' => 'cancelled',
  'cancellation_date' => '2025-06-22 11:58:27',
).
2025-6-22 11:58:27 - Updating recurring status for membership #34534. Customer ID: 25791; Previous: true; New: false
2025-6-22 11:58:27 - Updating membership #34534. New data: array (
  'auto_renew' => 0,
).
2025-6-22 11:58:27 - Payment profile successfully cancelled for membership #34534.
2025-6-22 11:58:27 - Updating membership #34535. New data: array (
  'activated_date' => '2025-06-22 11:58:27',
).
2025-6-22 11:58:27 - Updating membership #34535. New data: array (
  'status' => 'active',
).
2025-6-22 11:58:27 - Removing old role subscriber, adding new role subscriber for membership #34535 (user ID #25852).
2025-6-22 11:58:27 - Active email sent to user #25852 for membership #34535.
2025-6-22 11:58:27 - Active email sent to admin(s) regarding membership #34535.
2025-6-22 11:58:27 - Payment Received email not sent to user #25852 - payment amount is 0.
2025-6-22 11:58:27 - Updating membership #34535. New data: array (
  'times_billed' => 1,
).
2025-6-22 11:58:27 - Payment #134120 completed for member #25852 via Stripe gateway.
2025-6-22 11:58:29 - Stripe Gateway: Using subscription start date for subscription: 2025-07-22 11:58:24
2025-6-22 11:58:29 - Stripe Gateway: Creating subscription with 1753185504 start date via billing_cycle_anchor.
2025-6-22 11:58:30 - Updating membership #34535. New data: array (
  'gateway_subscription_id' => 'sub_1RcrDtDrMO37mudtwOUBojnU',
).
2025-6-22 11:58:30 - Updating payment #134120 with new data: array (
  'transaction_id' => 'sub_1RcrDtDrMO37mudtwOUBojnU',
)
2025-6-22 11:58:40 - "Can cancel" status for membership #34535: true.
2025-6-22 11:58:40 - "Can cancel" status for membership #34535: true.
2025-6-22 12:03:50 - Adding 2.99 proration credits to registration for user #25852.
2025-6-22 12:03:51 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.3282135523614 (ID #12)
2025-6-22 12:03:51 - Adding 2.99 proration credits to registration for user #25852.
2025-6-22 12:03:51 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.3282135523614 (ID #12)
2025-6-22 12:03:51 - Adding 2.99 proration credits to registration for user #25852.
2025-6-22 12:04:37 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.3282135523614 (ID #12)
2025-6-22 12:04:37 - Adding 2.99 proration credits to registration for user #25852.
2025-6-22 12:04:37 - Started new registration for membership level #12 via stripe.
2025-6-22 12:04:37 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.3282135523614 (ID #12)
2025-6-22 12:04:37 - Adding 2.99 proration credits to registration for user #25852.
2025-6-22 12:04:37 - Started new registration for membership level #12 via stripe.
2025-6-22 12:04:37 - Registration type: upgrade.
2025-6-22 12:04:37 - Adding new membership. Data: array (
  'customer_id' => '25791',
  'user_id' => '25852',
  'object_id' => 12,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 7.0,
  'recurring_amount' => 9.99,
  'created_date' => '2025-06-22 12:04:37',
  'expiration_date' => '2025-07-22 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => 'b1d4348083601f913f3b40e7547a9f64',
  'upgraded_from' => '34535',
)
2025-6-22 12:04:37 - New payment inserted. ID: 134121; User ID: 25852; Amount: 7.00; Subscription: Calculator with ChatDTC (Unlimited Credits); Status: pending
2025-6-22 12:04:38 - Registration for user #25852 sent to gateway. Level ID: 12; Initial Amount: 7.00; Recurring Amount: 9.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34536
2025-6-22 12:04:38 - Updating membership #34536. New data: array (
  'gateway_customer_id' => 'cus_SXx5d4fRAUYBPS',
).
2025-6-22 12:04:41 - Old price per day: 0.0982340862423 (ID #10); New price per day: 0.3282135523614 (ID #12)
2025-6-22 12:04:41 - Using recovered payment #134121 for registration. Transaction type: upgrade.
2025-6-22 12:04:41 - Adding 2.99 proration credits to registration for user #25852.
2025-6-22 12:04:41 - Registration for user #25852 sent to gateway. Level ID: 12; Initial Amount: 7.00; Recurring Amount: 9.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34536
2025-6-22 12:04:42 - Updating payment #134121 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3RcrJqDrMO37mudt0eqYz94M',
  'status' => 'complete',
)
2025-6-22 12:04:42 - Completing registration for customer #25791 via payment #134121.
2025-6-22 12:04:42 - Activating membership #34536.
2025-6-22 12:04:42 - Disabling all memberships for customer #25791 except: '34536'.
2025-6-22 12:04:42 - Disabling membership #34535.
2025-6-22 12:04:42 - Updating membership #34535. New data: array (
  'disabled' => 1,
).
2025-6-22 12:04:42 - "Can cancel" status for membership #34535: true.
2025-6-22 12:04:42 - "Can cancel" status for membership #34535: true.
2025-6-22 12:04:44 - Failed to cancel Stripe payment profile sub_1RcrDtDrMO37mudtwOUBojnU. Error code: resource_missing; Error Message: No such subscription: 'sub_1RcrDtDrMO37mudtwOUBojnU'.
2025-6-22 12:04:44 - Updating membership #34535. New data: array (
  'status' => 'cancelled',
  'cancellation_date' => '2025-06-22 12:04:44',
).
2025-6-22 12:04:44 - Updating recurring status for membership #34535. Customer ID: 25791; Previous: true; New: false
2025-6-22 12:04:44 - Updating membership #34535. New data: array (
  'auto_renew' => 0,
).
2025-6-22 12:04:44 - Payment profile successfully cancelled for membership #34535.
2025-6-22 12:04:44 - Updating membership #34536. New data: array (
  'activated_date' => '2025-06-22 12:04:44',
).
2025-6-22 12:04:44 - Updating membership #34536. New data: array (
  'status' => 'active',
).
2025-6-22 12:04:44 - Removing old role subscriber, adding new role subscriber for membership #34536 (user ID #25852).
2025-6-22 12:04:44 - Active email sent to user #25852 for membership #34536.
2025-6-22 12:04:44 - Active email sent to admin(s) regarding membership #34536.
2025-6-22 12:04:44 - Payment Received email not sent to user #25852 for payment ID #134121 - message is empty or disabled.
2025-6-22 12:04:44 - Payment Received email not sent to admin(s) for payment ID #134121 - message is empty or disabled.
2025-6-22 12:04:44 - Updating membership #34536. New data: array (
  'times_billed' => 1,
).
2025-6-22 12:04:44 - Payment #134121 completed for member #25852 via Stripe gateway.
2025-6-22 12:04:45 - Stripe Gateway: Using newly calculated expiration for subscription start date: 2025-07-22 23:59:59
2025-6-22 12:04:45 - Stripe Gateway: Creating subscription with 1753218285 start date via trial_end.
2025-6-22 12:04:47 - Updating membership #34536. New data: array (
  'gateway_subscription_id' => 'sub_1RcrJyDrMO37mudtG0QQDJBu',
).
2025-6-22 12:04:57 - "Can cancel" status for membership #34536: true.
2025-6-22 12:04:57 - "Can cancel" status for membership #34536: true.
2025-6-22 12:05:01 - Adding 9.99 proration credits to registration for user #25852.
2025-6-22 12:05:02 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.22965092402464 (ID #9)
2025-6-22 12:05:02 - Adding 9.99 proration credits to registration for user #25852.
2025-6-22 12:05:03 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.22965092402464 (ID #9)
2025-6-22 12:05:03 - Adding 9.99 proration credits to registration for user #25852.
2025-6-22 12:05:06 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.16394250513347 (ID #7)
2025-6-22 12:05:06 - Adding 9.99 proration credits to registration for user #25852.
2025-6-22 12:05:06 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.16394250513347 (ID #7)
2025-6-22 12:05:06 - Adding 9.99 proration credits to registration for user #25852.
2025-6-22 12:05:11 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.16394250513347 (ID #7)
2025-6-22 12:05:11 - Adding 9.99 proration credits to registration for user #25852.
2025-6-22 12:05:12 - Started new registration for membership level #7 via stripe.
2025-6-22 12:05:12 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.16394250513347 (ID #7)
2025-6-22 12:05:12 - Adding 9.99 proration credits to registration for user #25852.
2025-6-22 12:05:12 - Started new registration for membership level #7 via stripe.
2025-6-22 12:05:12 - Registration type: downgrade.
2025-6-22 12:05:12 - Adding new membership. Data: array (
  'customer_id' => '25791',
  'user_id' => '25852',
  'object_id' => 7,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => '0.00',
  'recurring_amount' => 4.99,
  'created_date' => '2025-06-22 12:05:12',
  'expiration_date' => '2025-09-22 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '4837f8718e00d9c1592ef235f2941198',
  'upgraded_from' => '34536',
)
2025-6-22 12:05:12 - New payment inserted. ID: 134122; User ID: 25852; Amount: 0.00; Subscription: Calculator with ChatDTC (50 Credits); Status: pending
2025-6-22 12:05:12 - Registration for user #25852 sent to gateway. Level ID: 7; Initial Amount: 0.00; Recurring Amount: 4.99; Auto Renew: true; Trial: false; Subscription Start: 2025-07-22 12:05:12; Membership ID: 34537
2025-6-22 12:05:13 - Updating membership #34537. New data: array (
  'gateway_customer_id' => 'cus_SXx5d4fRAUYBPS',
).
2025-6-22 12:05:15 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.16394250513347 (ID #7)
2025-6-22 12:05:15 - Using recovered payment #134122 for registration. Transaction type: downgrade.
2025-6-22 12:05:15 - Adding 9.99 proration credits to registration for user #25852.
2025-6-22 12:05:15 - Registration for user #25852 sent to gateway. Level ID: 7; Initial Amount: 0.00; Recurring Amount: 4.99; Auto Renew: true; Trial: false; Subscription Start: 2025-07-22 12:05:15; Membership ID: 34537
2025-6-22 12:05:16 - Updating payment #134122 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => '',
  'status' => 'complete',
)
2025-6-22 12:05:16 - Completing registration for customer #25791 via payment #134122.
2025-6-22 12:05:16 - Activating membership #34537.
2025-6-22 12:05:16 - Disabling all memberships for customer #25791 except: '34537'.
2025-6-22 12:05:16 - Disabling membership #34536.
2025-6-22 12:05:16 - Updating membership #34536. New data: array (
  'disabled' => 1,
).
2025-6-22 12:05:16 - "Can cancel" status for membership #34536: true.
2025-6-22 12:05:16 - "Can cancel" status for membership #34536: true.
2025-6-22 12:05:18 - Failed to cancel Stripe payment profile sub_1RcrJyDrMO37mudtG0QQDJBu. Error code: resource_missing; Error Message: No such subscription: 'sub_1RcrJyDrMO37mudtG0QQDJBu'.
2025-6-22 12:05:18 - Updating membership #34536. New data: array (
  'status' => 'cancelled',
  'cancellation_date' => '2025-06-22 12:05:18',
).
2025-6-22 12:05:18 - Updating recurring status for membership #34536. Customer ID: 25791; Previous: true; New: false
2025-6-22 12:05:18 - Updating membership #34536. New data: array (
  'auto_renew' => 0,
).
2025-6-22 12:05:18 - Payment profile successfully cancelled for membership #34536.
2025-6-22 12:05:18 - Updating membership #34537. New data: array (
  'activated_date' => '2025-06-22 12:05:18',
).
2025-6-22 12:05:18 - Updating membership #34537. New data: array (
  'status' => 'active',
).
2025-6-22 12:05:18 - Removing old role subscriber, adding new role subscriber for membership #34537 (user ID #25852).
2025-6-22 12:05:18 - Active email sent to user #25852 for membership #34537.
2025-6-22 12:05:18 - Active email sent to admin(s) regarding membership #34537.
2025-6-22 12:05:18 - Payment Received email not sent to user #25852 - payment amount is 0.
2025-6-22 12:05:18 - Updating membership #34537. New data: array (
  'times_billed' => 1,
).
2025-6-22 12:05:18 - Payment #134122 completed for member #25852 via Stripe gateway.
2025-6-22 12:05:19 - Stripe Gateway: Using subscription start date for subscription: 2025-07-22 12:05:15
2025-6-22 12:05:19 - Stripe Gateway: Creating subscription with 1753185915 start date via billing_cycle_anchor.
2025-6-22 12:05:20 - Updating membership #34537. New data: array (
  'gateway_subscription_id' => 'sub_1RcrKWDrMO37mudtAOg9qZHk',
).
2025-6-22 12:05:20 - Updating payment #134122 with new data: array (
  'transaction_id' => 'sub_1RcrKWDrMO37mudtAOg9qZHk',
)
2025-6-22 12:05:29 - "Can cancel" status for membership #34537: true.
2025-6-22 12:05:29 - "Can cancel" status for membership #34537: true.
2025-6-22 12:07:36 - Adding 4.99 proration credits to registration for user #25852.
2025-6-22 12:07:37 - Old price per day: 0.16394250513347 (ID #7); New price per day: 0.3282135523614 (ID #12)
2025-6-22 12:07:37 - Adding 4.99 proration credits to registration for user #25852.
2025-6-22 12:07:37 - Old price per day: 0.16394250513347 (ID #7); New price per day: 0.3282135523614 (ID #12)
2025-6-22 12:07:37 - Adding 4.99 proration credits to registration for user #25852.
2025-6-22 12:07:40 - Old price per day: 0.16394250513347 (ID #7); New price per day: 0.082108145106092 (ID #11)
2025-6-22 12:07:40 - Adding 4.99 proration credits to registration for user #25852.
2025-6-22 12:07:40 - Old price per day: 0.16394250513347 (ID #7); New price per day: 0.082108145106092 (ID #11)
2025-6-22 12:07:40 - Adding 4.99 proration credits to registration for user #25852.
2025-6-22 12:07:48 - Old price per day: 0.16394250513347 (ID #7); New price per day: 0.082108145106092 (ID #11)
2025-6-22 12:07:48 - Adding 4.99 proration credits to registration for user #25852.
2025-6-22 12:07:48 - Started new registration for membership level #11 via stripe.
2025-6-22 12:07:49 - Old price per day: 0.16394250513347 (ID #7); New price per day: 0.082108145106092 (ID #11)
2025-6-22 12:07:49 - Adding 4.99 proration credits to registration for user #25852.
2025-6-22 12:07:49 - Started new registration for membership level #11 via stripe.
2025-6-22 12:07:49 - Registration type: downgrade.
2025-6-22 12:07:49 - Adding new membership. Data: array (
  'customer_id' => '25791',
  'user_id' => '25852',
  'object_id' => 11,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 25.0,
  'recurring_amount' => 29.99,
  'created_date' => '2025-06-22 12:07:49',
  'expiration_date' => '2026-06-22 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => '21ed0ee2c44d224670df519e9a5b482d',
  'upgraded_from' => '34537',
)
2025-6-22 12:07:49 - New payment inserted. ID: 134123; User ID: 25852; Amount: 25.00; Subscription: Dynasty Trade Calculator (Yearly); Status: pending
2025-6-22 12:07:49 - Registration for user #25852 sent to gateway. Level ID: 11; Initial Amount: 25.00; Recurring Amount: 29.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34538
2025-6-22 12:07:50 - Updating membership #34538. New data: array (
  'gateway_customer_id' => 'cus_SXx5d4fRAUYBPS',
).
2025-6-22 12:07:52 - Old price per day: 0.16394250513347 (ID #7); New price per day: 0.082108145106092 (ID #11)
2025-6-22 12:07:52 - Using recovered payment #134123 for registration. Transaction type: downgrade.
2025-6-22 12:07:52 - Adding 4.99 proration credits to registration for user #25852.
2025-6-22 12:07:52 - Registration for user #25852 sent to gateway. Level ID: 11; Initial Amount: 25.00; Recurring Amount: 29.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34538
2025-6-22 12:07:54 - Updating payment #134123 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3RcrMwDrMO37mudt1ZTPAgqm',
  'status' => 'complete',
)
2025-6-22 12:07:54 - Completing registration for customer #25791 via payment #134123.
2025-6-22 12:07:54 - Activating membership #34538.
2025-6-22 12:07:54 - Disabling all memberships for customer #25791 except: '34538'.
2025-6-22 12:07:54 - Disabling membership #34537.
2025-6-22 12:07:54 - Updating membership #34537. New data: array (
  'disabled' => 1,
).
2025-6-22 12:07:54 - "Can cancel" status for membership #34537: true.
2025-6-22 12:07:54 - "Can cancel" status for membership #34537: true.
2025-6-22 12:07:55 - Failed to cancel Stripe payment profile sub_1RcrKWDrMO37mudtAOg9qZHk. Error code: resource_missing; Error Message: No such subscription: 'sub_1RcrKWDrMO37mudtAOg9qZHk'.
2025-6-22 12:07:55 - Updating membership #34537. New data: array (
  'status' => 'cancelled',
  'cancellation_date' => '2025-06-22 12:07:55',
).
2025-6-22 12:07:55 - Updating recurring status for membership #34537. Customer ID: 25791; Previous: true; New: false
2025-6-22 12:07:55 - Updating membership #34537. New data: array (
  'auto_renew' => 0,
).
2025-6-22 12:07:55 - Payment profile successfully cancelled for membership #34537.
2025-6-22 12:07:55 - Updating membership #34538. New data: array (
  'activated_date' => '2025-06-22 12:07:55',
).
2025-6-22 12:07:55 - Updating membership #34538. New data: array (
  'status' => 'active',
).
2025-6-22 12:07:55 - Removing old role subscriber, adding new role subscriber for membership #34538 (user ID #25852).
2025-6-22 12:07:56 - Active email sent to user #25852 for membership #34538.
2025-6-22 12:07:56 - Active email sent to admin(s) regarding membership #34538.
2025-6-22 12:07:56 - Payment Received email not sent to user #25852 for payment ID #134123 - message is empty or disabled.
2025-6-22 12:07:56 - Payment Received email not sent to admin(s) for payment ID #134123 - message is empty or disabled.
2025-6-22 12:07:56 - Updating membership #34538. New data: array (
  'times_billed' => 1,
).
2025-6-22 12:07:56 - Payment #134123 completed for member #25852 via Stripe gateway.
2025-6-22 12:07:57 - Stripe Gateway: Using newly calculated expiration for subscription start date: 2026-06-22 23:59:59
2025-6-22 12:07:57 - Stripe Gateway: Creating subscription with 1782162477 start date via trial_end.
2025-6-22 12:07:59 - Updating membership #34538. New data: array (
  'gateway_subscription_id' => 'sub_1RcrN4DrMO37mudtqAjWic3l',
).
2025-6-22 14:08:24 - Starting rcp_check_for_expired_users() cron job.
2025-6-22 14:08:24 - No expired memberships found.
2025-6-22 14:08:24 - Starting rcp_check_for_soon_to_expire_users() cron job.
2025-6-22 14:08:24 - Processing expiration reminder. ID: 0; Period: -1month; Levels: 1, 3, 2.
2025-6-22 14:08:24 - Reminder is not enabled - exiting.
2025-6-22 14:09:28 - Started new registration for membership level #12 via stripe.
2025-6-22 14:09:28 - Started new registration for membership level #12 via stripe.
2025-6-22 14:09:29 - Adding a new customer. Args: array (
  'date_registered' => '2025-06-22 14:09:29',
  'has_trialed' => 0,
  'user_id' => 25853,
)
2025-6-22 14:09:29 - Created new customer #25792.
2025-6-22 14:09:29 - Registration type: new.
2025-6-22 14:09:29 - Adding new membership. Data: array (
  'customer_id' => '25792',
  'user_id' => '25853',
  'object_id' => 12,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => 9.99,
  'recurring_amount' => 9.99,
  'created_date' => '2025-06-22 14:09:29',
  'expiration_date' => '2025-07-22 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => 'b3df2489fd1f0faaeb914a5a41ee8455',
)
2025-6-22 14:09:29 - New payment inserted. ID: 134124; User ID: 25853; Amount: 9.99; Subscription: Calculator with ChatDTC (Unlimited Credits); Status: pending
2025-6-22 14:09:30 - Registration for user #25853 sent to gateway. Level ID: 12; Initial Amount: 9.99; Recurring Amount: 9.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34539
2025-6-22 14:09:30 - Updating membership #34539. New data: array (
  'gateway_customer_id' => 'cus_SXzFeM1FRsl2wd',
).
2025-6-22 14:09:33 - Using recovered payment #134124 for registration. Transaction type: new.
2025-6-22 14:09:33 - Registration for user #25853 sent to gateway. Level ID: 12; Initial Amount: 9.99; Recurring Amount: 9.99; Auto Renew: true; Trial: false; Subscription Start: ; Membership ID: 34539
2025-6-22 14:09:34 - Updating payment #134124 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => 'ch_3RctGgDrMO37mudt3Xb8gSeO',
  'status' => 'complete',
)
2025-6-22 14:09:34 - Completing registration for customer #25792 via payment #134124.
2025-6-22 14:09:34 - Activating membership #34539.
2025-6-22 14:09:34 - Disabling all memberships for customer #25792 except: '34539'.
2025-6-22 14:09:34 - Updating membership #34539. New data: array (
  'activated_date' => '2025-06-22 14:09:34',
).
2025-6-22 14:09:34 - Updating membership #34539. New data: array (
  'status' => 'active',
).
2025-6-22 14:09:34 - Active email sent to user #25853 for membership #34539.
2025-6-22 14:09:34 - Active email sent to admin(s) regarding membership #34539.
2025-6-22 14:09:34 - Payment Received email not sent to user #25853 for payment ID #134124 - message is empty or disabled.
2025-6-22 14:09:34 - Payment Received email not sent to admin(s) for payment ID #134124 - message is empty or disabled.
2025-6-22 14:09:34 - Updating membership #34539. New data: array (
  'times_billed' => 1,
).
2025-6-22 14:09:34 - Payment #134124 completed for member #25853 via Stripe gateway.
2025-6-22 14:09:36 - Stripe Gateway: Using newly calculated expiration for subscription start date: 2025-07-22 23:59:59
2025-6-22 14:09:36 - Stripe Gateway: Creating subscription with 1753225776 start date via trial_end.
2025-6-22 14:09:38 - Updating membership #34539. New data: array (
  'gateway_subscription_id' => 'sub_1RctGmDrMO37mudtLw9cNaVh',
).
2025-6-22 14:09:47 - "Can cancel" status for membership #34539: true.
2025-6-22 14:09:47 - "Can cancel" status for membership #34539: true.
2025-6-22 14:09:54 - Adding 9.99 proration credits to registration for user #25853.
2025-6-22 14:09:55 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.22965092402464 (ID #9)
2025-6-22 14:09:55 - Adding 9.99 proration credits to registration for user #25853.
2025-6-22 14:09:55 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.22965092402464 (ID #9)
2025-6-22 14:09:55 - Adding 9.99 proration credits to registration for user #25853.
2025-6-22 14:10:00 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.22965092402464 (ID #9)
2025-6-22 14:10:00 - Adding 9.99 proration credits to registration for user #25853.
2025-6-22 14:10:00 - Started new registration for membership level #9 via stripe.
2025-6-22 14:10:01 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.22965092402464 (ID #9)
2025-6-22 14:10:01 - Adding 9.99 proration credits to registration for user #25853.
2025-6-22 14:10:01 - Started new registration for membership level #9 via stripe.
2025-6-22 14:10:01 - Registration type: downgrade.
2025-6-22 14:10:01 - Adding new membership. Data: array (
  'customer_id' => '25792',
  'user_id' => '25853',
  'object_id' => 9,
  'object_type' => 'membership',
  'currency' => 'USD',
  'initial_amount' => '0.00',
  'recurring_amount' => 6.99,
  'created_date' => '2025-06-22 14:10:01',
  'expiration_date' => '2025-08-22 23:59:59',
  'auto_renew' => 1,
  'times_billed' => 0,
  'maximum_renewals' => 0,
  'status' => 'pending',
  'signup_method' => 'live',
  'disabled' => 0,
  'gateway' => 'stripe',
  'subscription_key' => 'aacdf564da9abc0c83b3801e6d502e2e',
  'upgraded_from' => '34539',
)
2025-6-22 14:10:01 - Updating membership #34539. New data: array (
  'status' => 'active',
).
2025-6-22 14:10:01 - Updating membership #34540. New data: array (
  'status' => 'pending',
  'created_date' => 'July 22, 2025',
  'activated_date' => 'July 22, 2025',
).
2025-6-22 14:10:01 - New payment inserted. ID: 134125; User ID: 25853; Amount: 0.00; Subscription: Calculator with ChatDTC (100 Credits); Status: pending
2025-6-22 14:10:01 - Registration for user #25853 sent to gateway. Level ID: 9; Initial Amount: 0.00; Recurring Amount: 6.99; Auto Renew: true; Trial: false; Subscription Start: 2025-07-22 14:10:01; Membership ID: 34540
2025-6-22 14:10:02 - Updating membership #34540. New data: array (
  'gateway_customer_id' => 'cus_SXzFeM1FRsl2wd',
).
2025-6-22 14:10:04 - Old price per day: 0.3282135523614 (ID #12); New price per day: 0.22965092402464 (ID #9)
2025-6-22 14:10:04 - Using recovered payment #134125 for registration. Transaction type: downgrade.
2025-6-22 14:10:04 - Adding 9.99 proration credits to registration for user #25853.
2025-6-22 14:10:04 - Registration for user #25853 sent to gateway. Level ID: 9; Initial Amount: 0.00; Recurring Amount: 6.99; Auto Renew: true; Trial: false; Subscription Start: 2025-07-22 14:10:04; Membership ID: 34540
2025-6-22 14:10:05 - Updating payment #134125 with new data: array (
  'payment_type' => 'Credit Card',
  'transaction_id' => '',
  'status' => 'complete',
)
2025-6-22 14:10:05 - Completing registration for customer #25792 via payment #134125.
2025-6-22 14:10:05 - Activating membership #34540.
2025-6-22 14:10:05 - Disabling all memberships for customer #25792 except: '34540'.
2025-6-22 14:10:05 - Disabling membership #34539.
2025-6-22 14:10:05 - Updating membership #34539. New data: array (
  'disabled' => 1,
).
2025-6-22 14:10:05 - "Can cancel" status for membership #34539: true.
2025-6-22 14:10:05 - "Can cancel" status for membership #34539: true.
2025-6-22 14:10:07 - Failed to cancel Stripe payment profile sub_1RctGmDrMO37mudtLw9cNaVh. Error code: resource_missing; Error Message: No such subscription: 'sub_1RctGmDrMO37mudtLw9cNaVh'.
2025-6-22 14:10:07 - Updating membership #34539. New data: array (
  'status' => 'cancelled',
  'cancellation_date' => '2025-06-22 14:10:07',
).
2025-6-22 14:10:07 - Updating recurring status for membership #34539. Customer ID: 25792; Previous: true; New: false
2025-6-22 14:10:07 - Updating membership #34539. New data: array (
  'auto_renew' => 0,
).
2025-6-22 14:10:07 - Payment profile successfully cancelled for membership #34539.
2025-6-22 14:10:07 - Updating membership #34540. New data: array (
  'status' => 'active',
).
2025-6-22 14:10:07 - Removing old role subscriber, adding new role subscriber for membership #34540 (user ID #25853).
2025-6-22 14:10:07 - Active email sent to user #25853 for membership #34540.
2025-6-22 14:10:07 - Active email sent to admin(s) regarding membership #34540.
2025-6-22 14:10:07 - Payment Received email not sent to user #25853 - payment amount is 0.
2025-6-22 14:10:07 - Updating membership #34540. New data: array (
  'times_billed' => 1,
).
2025-6-22 14:10:07 - Payment #134125 completed for member #25853 via Stripe gateway.
2025-6-22 14:10:08 - Stripe Gateway: Using subscription start date for subscription: 2025-07-22 14:10:04
2025-6-22 14:10:08 - Stripe Gateway: Creating subscription with 1753193404 start date via billing_cycle_anchor.
2025-6-22 14:10:09 - Updating membership #34540. New data: array (
  'gateway_subscription_id' => 'sub_1RctHJDrMO37mudtaAw6smEm',
).
2025-6-22 14:10:09 - Updating payment #134125 with new data: array (
  'transaction_id' => 'sub_1RctHJDrMO37mudtaAw6smEm',
)
2025-6-22 14:10:19 - "Can cancel" status for membership #34540: true.
2025-6-22 14:10:19 - "Can cancel" status for membership #34540: true.
